"""
Enterprise Database Manager
Professional-grade database operations with connection pooling, transactions, and monitoring
"""

import sqlite3
import threading
import time
from contextlib import contextmanager
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from queue import Queue
from enterprise_config import config
from enterprise_logger import get_logger

@dataclass
class DatabaseMetrics:
    """Database performance metrics"""
    total_queries: int = 0
    total_connections: int = 0
    active_connections: int = 0
    failed_queries: int = 0
    average_query_time: float = 0.0
    last_backup: Optional[datetime] = None

class ConnectionPool:
    """Thread-safe database connection pool"""
    
    def __init__(self, database_path: str, pool_size: int = 10):
        self.database_path = database_path
        self.pool_size = pool_size
        self.pool = Queue(maxsize=pool_size)
        self.lock = threading.Lock()
        self.logger = get_logger('database.pool')
        
        # Initialize pool
        for _ in range(pool_size):
            conn = self._create_connection()
            self.pool.put(conn)
    
    def _create_connection(self) -> sqlite3.Connection:
        """Create a new database connection"""
        conn = sqlite3.connect(
            self.database_path,
            timeout=config.database.timeout,
            check_same_thread=False
        )
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA synchronous=NORMAL")
        conn.execute("PRAGMA cache_size=10000")
        conn.execute("PRAGMA temp_store=MEMORY")
        return conn
    
    @contextmanager
    def get_connection(self):
        """Get connection from pool"""
        conn = None
        try:
            conn = self.pool.get(timeout=30)
            yield conn
        except Exception as e:
            self.logger.error(f"Connection pool error: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                self.pool.put(conn)

class EnterpriseDatabase:
    """Enterprise-grade database manager"""
    
    def __init__(self):
        self.logger = get_logger('database')
        self.metrics = DatabaseMetrics()
        self.pool = ConnectionPool(config.database.path, config.database.connection_pool_size)
        self._initialize_schema()
        self._start_background_tasks()
    
    def _initialize_schema(self):
        """Initialize database schema"""
        with self.logger.performance_timer("schema_initialization"):
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # Guilds table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS guilds (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        icon TEXT,
                        owner_id TEXT,
                        member_count INTEGER,
                        created_at TEXT,
                        last_seen TEXT DEFAULT CURRENT_TIMESTAMP,
                        is_accessible INTEGER DEFAULT 1
                    )
                ''')
                
                # Channels table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS channels (
                        id TEXT PRIMARY KEY,
                        guild_id TEXT,
                        name TEXT NOT NULL,
                        type INTEGER,
                        topic TEXT,
                        position INTEGER,
                        parent_id TEXT,
                        nsfw INTEGER DEFAULT 0,
                        created_at TEXT,
                        last_seen TEXT DEFAULT CURRENT_TIMESTAMP,
                        last_message_id TEXT,
                        message_count INTEGER DEFAULT 0,
                        is_accessible INTEGER DEFAULT 1,
                        was_temporarily_accessible INTEGER DEFAULT 0,
                        first_accessible_at TEXT,
                        last_accessible_at TEXT,
                        FOREIGN KEY (guild_id) REFERENCES guilds (id)
                    )
                ''')
                
                # Messages table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS messages (
                        id TEXT PRIMARY KEY,
                        channel_id TEXT,
                        guild_id TEXT,
                        author_id TEXT,
                        author_name TEXT,
                        author_discriminator TEXT,
                        author_avatar TEXT,
                        content TEXT,
                        embeds TEXT,
                        attachments TEXT,
                        reactions TEXT,
                        mentions TEXT,
                        timestamp TEXT,
                        edited_timestamp TEXT,
                        message_type INTEGER DEFAULT 0,
                        is_deleted INTEGER DEFAULT 0,
                        deleted_at TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (channel_id) REFERENCES channels (id),
                        FOREIGN KEY (guild_id) REFERENCES guilds (id)
                    )
                ''')
                
                # Scan history table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scan_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        guild_id TEXT,
                        channel_id TEXT,
                        scan_type TEXT,
                        status TEXT,
                        messages_found INTEGER DEFAULT 0,
                        error_message TEXT,
                        duration REAL,
                        timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (guild_id) REFERENCES guilds (id),
                        FOREIGN KEY (channel_id) REFERENCES channels (id)
                    )
                ''')
                
                # Create indexes for performance
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_messages_channel_id ON messages(channel_id)",
                    "CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp)",
                    "CREATE INDEX IF NOT EXISTS idx_messages_author_id ON messages(author_id)",
                    "CREATE INDEX IF NOT EXISTS idx_channels_guild_id ON channels(guild_id)",
                    "CREATE INDEX IF NOT EXISTS idx_channels_accessible ON channels(is_accessible)",
                    "CREATE INDEX IF NOT EXISTS idx_scan_history_timestamp ON scan_history(timestamp)",
                ]
                
                for index_sql in indexes:
                    cursor.execute(index_sql)
                
                conn.commit()
                self.logger.info("Database schema initialized successfully")
    
    def _start_background_tasks(self):
        """Start background maintenance tasks"""
        def backup_worker():
            while True:
                time.sleep(config.database.backup_interval)
                try:
                    self.create_backup()
                except Exception as e:
                    self.logger.error(f"Backup failed: {e}")
        
        backup_thread = threading.Thread(target=backup_worker, daemon=True)
        backup_thread.start()
        self.logger.info("Background database tasks started")
    
    @contextmanager
    def transaction(self):
        """Database transaction context manager"""
        with self.pool.get_connection() as conn:
            try:
                yield conn
                conn.commit()
            except Exception:
                conn.rollback()
                raise
    
    def execute_query(self, query: str, params: tuple = (), fetch: str = None) -> Union[List[sqlite3.Row], sqlite3.Row, None]:
        """Execute database query with metrics tracking"""
        start_time = time.time()
        self.metrics.total_queries += 1
        
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                if fetch == 'all':
                    result = cursor.fetchall()
                elif fetch == 'one':
                    result = cursor.fetchone()
                else:
                    result = None
                
                conn.commit()
                
                # Update metrics
                duration = time.time() - start_time
                self.metrics.average_query_time = (
                    (self.metrics.average_query_time * (self.metrics.total_queries - 1) + duration) 
                    / self.metrics.total_queries
                )
                
                self.logger.log_database_operation(
                    operation=query.split()[0].upper(),
                    table=self._extract_table_name(query),
                    duration=duration,
                    rows_affected=cursor.rowcount if cursor.rowcount > 0 else None
                )
                
                return result
                
        except Exception as e:
            self.metrics.failed_queries += 1
            self.logger.error(f"Database query failed: {e}", query=query[:100])
            raise
    
    def _extract_table_name(self, query: str) -> str:
        """Extract table name from SQL query"""
        query_upper = query.upper()
        if 'FROM' in query_upper:
            parts = query_upper.split('FROM')[1].strip().split()
            return parts[0] if parts else 'unknown'
        elif 'INTO' in query_upper:
            parts = query_upper.split('INTO')[1].strip().split()
            return parts[0] if parts else 'unknown'
        elif 'UPDATE' in query_upper:
            parts = query_upper.split('UPDATE')[1].strip().split()
            return parts[0] if parts else 'unknown'
        return 'unknown'
    
    def create_backup(self):
        """Create database backup"""
        backup_path = f"{config.database.path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        with self.logger.performance_timer("database_backup"):
            with self.pool.get_connection() as conn:
                backup_conn = sqlite3.connect(backup_path)
                conn.backup(backup_conn)
                backup_conn.close()
                
                self.metrics.last_backup = datetime.now()
                self.logger.info(f"Database backup created: {backup_path}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get database metrics"""
        return {
            'total_queries': self.metrics.total_queries,
            'failed_queries': self.metrics.failed_queries,
            'success_rate': (self.metrics.total_queries - self.metrics.failed_queries) / max(self.metrics.total_queries, 1),
            'average_query_time': self.metrics.average_query_time,
            'last_backup': self.metrics.last_backup.isoformat() if self.metrics.last_backup else None,
            'pool_size': self.pool.pool_size,
            'active_connections': self.pool.pool_size - self.pool.pool.qsize()
        }

# Global database instance
db = EnterpriseDatabase()
