"""
Enterprise Discord Logger Configuration
Professional-grade configuration management
"""

import os
from dataclasses import dataclass
from typing import Optional, List
from enum import Enum

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class ScanStrategy(Enum):
    AGGRESSIVE = "aggressive"  # Scan all channels immediately
    SMART = "smart"           # Scan based on patterns and history
    CONSERVATIVE = "conservative"  # Scan only when needed

@dataclass
class DatabaseConfig:
    """Database configuration"""
    path: str = "enterprise_discord.db"
    connection_pool_size: int = 10
    timeout: int = 30
    backup_interval: int = 3600  # seconds
    
@dataclass
class DiscordConfig:
    """Discord API configuration"""
    token: Optional[str] = None
    base_url: str = "https://discord.com/api/v9"
    rate_limit_per_second: int = 50
    max_retries: int = 3
    timeout: int = 10
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

@dataclass
class ScannerConfig:
    """Channel scanner configuration"""
    strategy: ScanStrategy = ScanStrategy.AGGRESSIVE
    concurrent_channels: int = 10
    scan_interval: int = 300  # seconds
    message_batch_size: int = 100
    max_scan_depth: int = 1000  # messages per channel
    retry_failed_channels: bool = True
    
@dataclass
class WebConfig:
    """Web server configuration"""
    host: str = "127.0.0.1"
    port: int = 5001
    debug: bool = True
    secret_key: str = "enterprise-discord-logger-secret-key"
    cors_origins: List[str] = None
    
    def __post_init__(self):
        if self.cors_origins is None:
            self.cors_origins = ["*"]

@dataclass
class MonitoringConfig:
    """Monitoring and metrics configuration"""
    enable_metrics: bool = True
    metrics_interval: int = 60  # seconds
    log_performance: bool = True
    alert_on_errors: bool = True
    max_error_rate: float = 0.1  # 10% error rate threshold

@dataclass
class CacheConfig:
    """Caching configuration"""
    enable_redis: bool = False
    redis_url: str = "redis://localhost:6379"
    memory_cache_size: int = 1000
    cache_ttl: int = 3600  # seconds

class EnterpriseConfig:
    """Main configuration class"""
    
    def __init__(self):
        self.database = DatabaseConfig()
        self.discord = DiscordConfig()
        self.scanner = ScannerConfig()
        self.web = WebConfig()
        self.monitoring = MonitoringConfig()
        self.cache = CacheConfig()
        self.log_level = LogLevel.INFO
        
        # Load from environment variables
        self._load_from_env()
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        # Discord configuration
        self.discord.token = os.getenv('DISCORD_TOKEN')
        
        # Database configuration
        self.database.path = os.getenv('DATABASE_PATH', self.database.path)
        
        # Web configuration
        self.web.host = os.getenv('WEB_HOST', self.web.host)
        self.web.port = int(os.getenv('WEB_PORT', self.web.port))
        self.web.debug = os.getenv('WEB_DEBUG', 'true').lower() == 'true'
        
        # Scanner configuration
        strategy = os.getenv('SCAN_STRATEGY', 'aggressive').lower()
        if strategy in [s.value for s in ScanStrategy]:
            self.scanner.strategy = ScanStrategy(strategy)
        
        self.scanner.concurrent_channels = int(os.getenv('CONCURRENT_CHANNELS', self.scanner.concurrent_channels))
        
        # Monitoring configuration
        self.monitoring.enable_metrics = os.getenv('ENABLE_METRICS', 'true').lower() == 'true'
        
        # Log level
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        if log_level in [l.value for l in LogLevel]:
            self.log_level = LogLevel(log_level)
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        if not self.discord.token:
            errors.append("Discord token is required")
        
        if self.scanner.concurrent_channels < 1:
            errors.append("Concurrent channels must be at least 1")
        
        if self.web.port < 1 or self.web.port > 65535:
            errors.append("Web port must be between 1 and 65535")
        
        if self.database.connection_pool_size < 1:
            errors.append("Database connection pool size must be at least 1")
        
        return errors
    
    def to_dict(self) -> dict:
        """Convert configuration to dictionary"""
        return {
            'database': self.database.__dict__,
            'discord': {**self.discord.__dict__, 'token': '***' if self.discord.token else None},
            'scanner': {**self.scanner.__dict__, 'strategy': self.scanner.strategy.value},
            'web': self.web.__dict__,
            'monitoring': self.monitoring.__dict__,
            'cache': self.cache.__dict__,
            'log_level': self.log_level.value
        }

# Global configuration instance
config = EnterpriseConfig()
