#!/usr/bin/env python3
"""
Discord Logger - Discord message logger with web interface
Usage: python main.py
Web interface: http://localhost:5000
"""

import os
import sys
import asyncio
import threading
import logging
from datetime import datetime

# Set UTF-8 encoding for Windows
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from database import DatabaseManager
from discord_client import DiscordLogger
from web_server import WebServer

# Setup logging with UTF-8 encoding
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('discord_logger.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class DiscordLoggerApp:
    def __init__(self):
        self.db_manager = None
        self.discord_client = None
        self.web_server = None
        self.discord_thread = None
        self.running = False
        
    def setup_database(self):
        """Initialize database"""
        logger.info("Initializing database...")
        self.db_manager = DatabaseManager(Config.DATABASE_PATH)
        logger.info("Database initialized")

    def setup_discord_client(self, token):
        """Initialize Discord client"""
        logger.info("Initializing Discord client...")
        self.discord_client = DiscordLogger(self.db_manager)
        return self.discord_client

    def setup_web_server(self):
        """Initialize web server"""
        logger.info("Initializing web server...")
        self.web_server = WebServer(self.discord_client)
        logger.info("Web server initialized")
    
    def run_discord_client(self, token):
        """Run Discord client in separate thread"""
        def discord_runner():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.discord_client.start(token))
            except Exception as e:
                logger.error(f"Discord client error: {e}")

        self.discord_thread = threading.Thread(target=discord_runner, daemon=True)
        self.discord_thread.start()
        logger.info("Discord client started in separate thread")
    
    def start(self, token=None):
        """Start application"""
        try:
            logger.info("Starting Discord Logger...")

            # Initialize components
            self.setup_database()

            # If token not provided, use from config or environment
            if not token:
                token = Config.DISCORD_TOKEN or os.getenv('DISCORD_TOKEN')

            if not token:
                logger.warning("Discord token not found. Starting web server only...")
                self.discord_client = None
            else:
                self.discord_client = self.setup_discord_client(token)
                self.run_discord_client(token)

            self.setup_web_server()

            self.running = True

            # Start web server
            logger.info(f"Starting web server on http://{Config.HOST}:{Config.PORT}")
            print(f"\nDiscord Logger started!")
            print(f"Web interface: http://{Config.HOST}:{Config.PORT}")

            if not token:
                print(f"Discord token not found. Go to http://{Config.HOST}:{Config.PORT}/setup to configure")
            else:
                print(f"Discord client connected")

            print(f"Logs saved to: discord_logger.log")
            print(f"Database: {Config.DATABASE_PATH}")
            print(f"\nPress Ctrl+C to stop")

            self.web_server.run()

        except KeyboardInterrupt:
            logger.info("Received stop signal...")
            self.stop()
        except Exception as e:
            logger.error(f"Critical error: {e}")
            self.stop()
    
    def stop(self):
        """Stop application"""
        logger.info("Stopping Discord Logger...")
        self.running = False

        if self.discord_client:
            try:
                asyncio.run(self.discord_client.close())
            except Exception as e:
                logger.error(f"Error stopping Discord client: {e}")

        logger.info("Discord Logger stopped")

def main():
    """Main function"""
    print("=" * 50)
    print("Discord Logger")
    print("=" * 50)

    # Check Python version
    if sys.version_info < (3, 8):
        print("Python 3.8 or higher required")
        sys.exit(1)

    # Check dependencies
    try:
        import discord
        import flask
        import flask_socketio
    except ImportError as e:
        print(f"Missing dependency: {e}")
        print("Install dependencies: pip install -r requirements.txt")
        sys.exit(1)
    
    # Создание и запуск приложения
    app = DiscordLoggerApp()
    
    # Проверка аргументов командной строки
    token = None
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            print("Usage:")
            print("  python main.py [DISCORD_TOKEN]")
            print("  python main.py --help")
            print("\nEnvironment variables:")
            print("  DISCORD_TOKEN - Discord user token")
            print("\nExample:")
            print("  python main.py YOUR_DISCORD_TOKEN")
            print("  DISCORD_TOKEN=YOUR_TOKEN python main.py")
            return
        else:
            token = sys.argv[1]
    
    try:
        app.start(token)
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        print(f"Startup error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
