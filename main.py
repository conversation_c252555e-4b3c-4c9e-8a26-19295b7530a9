#!/usr/bin/env python3
"""
Discord Logger - Discord message logger with web interface
Usage: python main.py
Web interface: http://localhost:5000
"""

import os
import sys
import asyncio
import threading
import logging
import requests
import json
from datetime import datetime

# Set UTF-8 encoding for Windows
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from database import DatabaseManager
from discord_client import DiscordLogger
from web_server import WebServer

# Setup logging with UTF-8 encoding
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('discord_logger.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class DiscordLoggerApp:
    def __init__(self):
        self.db_manager = None
        self.discord_client = None
        self.web_server = None
        self.discord_thread = None
        self.running = False
        
    def setup_database(self):
        """Initialize database"""
        logger.info("Initializing database...")
        self.db_manager = DatabaseManager(Config.DATABASE_PATH)
        logger.info("Database initialized")

    def setup_discord_client(self, token):
        """Initialize Discord client"""
        logger.info("Initializing Discord client...")
        self.discord_client = DiscordLogger(self.db_manager)
        return self.discord_client

    def setup_web_server(self):
        """Initialize web server"""
        logger.info("Initializing web server...")
        self.web_server = WebServer(self.discord_client)
        # Add reference to main app for Discord client restart
        self.web_server.main_app = self
        logger.info("Web server initialized")
    
    def run_discord_client(self, token):
        """Run Discord client in separate thread"""
        def discord_runner():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.discord_client.start(token))
            except Exception as e:
                logger.error(f"Discord client error: {e}")

        self.discord_thread = threading.Thread(target=discord_runner, daemon=True)
        self.discord_thread.start()
        logger.info("Discord client started in separate thread")
    
    def start(self, token=None):
        """Start application"""
        try:
            logger.info("Starting Discord Logger...")

            # Initialize components
            self.setup_database()

            # If token not provided, use from config or environment
            if not token:
                token = Config.DISCORD_TOKEN or os.getenv('DISCORD_TOKEN')

            if not token:
                logger.warning("Discord token not found. Starting web server only...")
                self.discord_client = None
            else:
                self.discord_client = self.setup_discord_client(token)
                self.run_discord_client(token)

            self.setup_web_server()

            self.running = True

            # Start web server
            logger.info(f"Starting web server on http://{Config.HOST}:{Config.PORT}")
            print(f"\nDiscord Logger started!")
            print(f"Web interface: http://{Config.HOST}:{Config.PORT}")

            if not token:
                print(f"Discord token not found. Go to http://{Config.HOST}:{Config.PORT}/setup to configure")
            else:
                print(f"Discord client connected")

            print(f"Logs saved to: discord_logger.log")
            print(f"Database: {Config.DATABASE_PATH}")
            print(f"\nPress Ctrl+C to stop")

            self.web_server.run()

        except KeyboardInterrupt:
            logger.info("Received stop signal...")
            self.stop()
        except Exception as e:
            logger.error(f"Critical error: {e}")
            self.stop()
    
    def stop(self):
        """Stop application"""
        logger.info("Stopping Discord Logger...")
        self.running = False

        if self.discord_client:
            try:
                asyncio.run(self.discord_client.close())
            except Exception as e:
                logger.error(f"Error stopping Discord client: {e}")

        logger.info("Discord Logger stopped")

    def restart_discord_client(self, token):
        """Restart Discord client with new token"""
        try:
            # Stop existing client
            if self.discord_client:
                try:
                    asyncio.run(self.discord_client.close())
                except:
                    pass

            # Create new client
            self.discord_client = self.setup_discord_client(token)
            self.run_discord_client(token)

            # Update web server reference
            if self.web_server:
                self.web_server.discord_client = self.discord_client

            logger.info("Discord client restarted successfully")
            return True

        except Exception as e:
            logger.error(f"Error restarting Discord client: {e}")
            return False

    def login_with_credentials(self, email, password):
        """Login to Discord using email and password to get token"""
        try:
            logger.info("Attempting to login with email and password...")

            # Create a session to maintain cookies
            session = requests.Session()

            # First, get the login page to establish session
            session.get("https://discord.com/login")

            # Discord login endpoint
            login_url = "https://discord.com/api/v9/auth/login"

            # Prepare login data
            login_data = {
                "login": email,
                "password": password,
                "undelete": False,
                "captcha_key": None,
                "login_source": None,
                "gift_code_sku_id": None
            }

            # Updated headers to mimic a real browser more accurately
            import base64
            super_properties = {
                "os": "Windows",
                "browser": "Chrome",
                "device": "",
                "system_locale": "en-US",
                "browser_user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "browser_version": "120.0.0.0",
                "os_version": "10",
                "referrer": "",
                "referring_domain": "",
                "referrer_current": "",
                "referring_domain_current": "",
                "release_channel": "stable",
                "client_build_number": 253853,
                "client_event_source": None
            }

            headers = {
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US,en;q=0.9",
                "Content-Type": "application/json",
                "Origin": "https://discord.com",
                "Referer": "https://discord.com/login",
                "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "X-Debug-Options": "bugReporterEnabled",
                "X-Discord-Locale": "en-US",
                "X-Discord-Timezone": "Europe/London",
                "X-Super-Properties": base64.b64encode(json.dumps(super_properties, separators=(',', ':')).encode()).decode()
            }

            # Make login request
            response = session.post(login_url, json=login_data, headers=headers)

            logger.info(f"Login response status: {response.status_code}")
            logger.info(f"Login response: {response.text[:500]}...")  # Log first 500 chars

            if response.status_code == 200:
                data = response.json()

                if 'token' in data:
                    token = data['token'].strip()
                    logger.info(f"Successfully obtained token from email/password login: {token[:20]}...")

                    # Validate token format (Discord tokens are usually 70+ characters)
                    if len(token) < 50:
                        logger.error(f"Token seems too short: {len(token)} characters")
                        return {'success': False, 'error': 'Received invalid token format'}

                    # Save token and restart client
                    from config import Config
                    Config.DISCORD_TOKEN = token

                    success = self.restart_discord_client(token)
                    if success:
                        return {'success': True, 'message': 'Login successful'}
                    else:
                        return {'success': False, 'error': 'Failed to connect with obtained token'}

                elif 'captcha_key' in data or 'captcha_sitekey' in data:
                    return {'success': False, 'error': 'Captcha required. Discord has detected automated login attempts. Please try again later or use token authentication.'}

                elif 'mfa' in data and data['mfa']:
                    return {'success': False, 'error': '2FA is enabled on your account. Please use token authentication instead.'}

                elif 'errors' in data:
                    errors = data['errors']
                    if 'login' in errors:
                        login_errors = errors['login']['_errors']
                        if any(err.get('code') == 'EMAIL_INVALID' for err in login_errors):
                            return {'success': False, 'error': 'Invalid email address format.'}
                        elif any(err.get('code') == 'BASE_TYPE_REQUIRED' for err in login_errors):
                            return {'success': False, 'error': 'Email field is required.'}
                        else:
                            return {'success': False, 'error': 'Invalid email address.'}
                    elif 'password' in errors:
                        password_errors = errors['password']['_errors']
                        if any(err.get('code') == 'PASSWORD_INVALID' for err in password_errors):
                            return {'success': False, 'error': 'Invalid password.'}
                        else:
                            return {'success': False, 'error': 'Password is required.'}
                    else:
                        return {'success': False, 'error': f'Login failed: {errors}'}

                else:
                    return {'success': False, 'error': 'Login failed. Please check your credentials.'}

            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    if 'errors' in error_data:
                        return {'success': False, 'error': f'Invalid credentials: {error_data["errors"]}'}
                    else:
                        return {'success': False, 'error': 'Invalid email or password.'}
                except:
                    return {'success': False, 'error': 'Invalid email or password.'}

            elif response.status_code == 429:
                return {'success': False, 'error': 'Too many login attempts. Please wait and try again later.'}

            else:
                return {'success': False, 'error': f'Login failed with status {response.status_code}. Discord may be blocking automated logins.'}

        except Exception as e:
            logger.error(f"Error during email/password login: {e}")
            # Try alternative method with selenium
            return self.login_with_selenium(email, password)

    def login_with_selenium(self, email, password):
        """Alternative login method using selenium"""
        try:
            logger.info("Trying alternative login method with browser automation...")

            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            import time

            # Setup Chrome options
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # Run in background
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Setup driver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)

            try:
                # Go to Discord login page
                driver.get("https://discord.com/login")

                # Wait for page to load
                wait = WebDriverWait(driver, 10)

                # Find and fill email field
                email_field = wait.until(EC.presence_of_element_located((By.NAME, "email")))
                email_field.send_keys(email)

                # Find and fill password field
                password_field = driver.find_element(By.NAME, "password")
                password_field.send_keys(password)

                # Click login button
                login_button = driver.find_element(By.XPATH, "//button[@type='submit']")
                login_button.click()

                # Wait a bit for login to process
                time.sleep(3)

                # Check if we're redirected to the main page (successful login)
                current_url = driver.current_url
                if "channels" in current_url or "app" in current_url:
                    # Login successful, now get the token from localStorage
                    token = driver.execute_script("""
                        return (webpackChunkdiscord_app.push([[''],{},e=>{m=[];for(let c in e.c)m.push(e.c[c])}]),m).find(m=>m?.exports?.default?.getToken!==void 0).exports.default.getToken()
                    """)

                    if token:
                        logger.info("Successfully obtained token using selenium method")

                        # Save token and restart client
                        from config import Config
                        Config.DISCORD_TOKEN = token

                        success = self.restart_discord_client(token)
                        if success:
                            return {'success': True, 'message': 'Login successful using browser automation'}
                        else:
                            return {'success': False, 'error': 'Failed to connect with obtained token'}
                    else:
                        return {'success': False, 'error': 'Could not extract token from browser'}

                else:
                    # Check for error messages
                    try:
                        error_element = driver.find_element(By.CSS_SELECTOR, "[class*='error']")
                        error_text = error_element.text
                        return {'success': False, 'error': f'Login failed: {error_text}'}
                    except:
                        return {'success': False, 'error': 'Login failed. Please check your credentials.'}

            finally:
                driver.quit()

        except ImportError:
            return {'success': False, 'error': 'Browser automation not available. Please install selenium and webdriver-manager, or use token authentication.'}
        except Exception as e:
            logger.error(f"Error during selenium login: {e}")
            return {'success': False, 'error': f'Browser automation failed: {str(e)}. Please use token authentication.'}

def main():
    """Main function"""
    print("=" * 50)
    print("Discord Logger")
    print("=" * 50)

    # Check Python version
    if sys.version_info < (3, 8):
        print("Python 3.8 or higher required")
        sys.exit(1)

    # Check dependencies
    try:
        import discord
        import flask
        import flask_socketio
    except ImportError as e:
        print(f"Missing dependency: {e}")
        print("Install dependencies: pip install -r requirements.txt")
        sys.exit(1)
    
    # Создание и запуск приложения
    app = DiscordLoggerApp()
    
    # Проверка аргументов командной строки
    token = None
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            print("Usage:")
            print("  python main.py [DISCORD_TOKEN]")
            print("  python main.py --help")
            print("\nEnvironment variables:")
            print("  DISCORD_TOKEN - Discord user token")
            print("\nExample:")
            print("  python main.py YOUR_DISCORD_TOKEN")
            print("  DISCORD_TOKEN=YOUR_TOKEN python main.py")
            return
        else:
            token = sys.argv[1]
    
    try:
        app.start(token)
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        print(f"Startup error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
