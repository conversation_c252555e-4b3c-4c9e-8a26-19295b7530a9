import os

class Config:
    # Discord settings
    DISCORD_TOKEN = os.getenv('DISCORD_TOKEN', '')
    
    # Flask settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
    HOST = '127.0.0.1'
    PORT = 5000
    DEBUG = True
    
    # Database settings
    DATABASE_PATH = 'discord_logger.db'
    
    # Logging settings
    LOG_LEVEL = 'INFO'
    MAX_MESSAGE_HISTORY = 1000  # Per channel
    
    # Update intervals (seconds)
    MESSAGE_CHECK_INTERVAL = 2
    CHANNEL_CHECK_INTERVAL = 30
    
    # UI settings
    MESSAGES_PER_PAGE = 50
