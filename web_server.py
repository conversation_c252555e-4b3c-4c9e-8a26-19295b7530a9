from flask import Flask, render_template, request, jsonify, redirect, url_for, session
from flask_socketio import <PERSON>cket<PERSON>, emit
import json
import logging
from typing import Optional
from config import Config

class WebServer:
    def __init__(self, discord_client):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = Config.SECRET_KEY
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        self.discord_client = discord_client
        self.logger = logging.getLogger(__name__)
        
        self.setup_routes()
        self.setup_socketio()
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            """Main page"""
            if not Config.DISCORD_TOKEN:
                return redirect(url_for('setup'))
            return render_template('index.html')
        
        @self.app.route('/setup')
        def setup():
            """Setup page for Discord token"""
            return render_template('setup.html')
        
        @self.app.route('/api/guilds')
        def get_guilds():
            """Get all guilds"""
            try:
                if not self.discord_client:
                    return jsonify({'success': True, 'guilds': []})
                guilds = self.discord_client.get_guild_data()
                return jsonify({'success': True, 'guilds': guilds})
            except Exception as e:
                self.logger.error(f'Error getting guilds: {e}')
                return jsonify({'success': True, 'guilds': []})
        
        @self.app.route('/api/channels/<int:guild_id>')
        def get_channels(guild_id):
            """Get channels for a guild"""
            try:
                channels = self.discord_client.get_channel_data(guild_id)
                return jsonify({'success': True, 'channels': channels})
            except Exception as e:
                self.logger.error(f'Error getting channels for guild {guild_id}: {e}')
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/dms')
        def get_dms():
            """Get DM channels"""
            try:
                if not self.discord_client:
                    return jsonify({'success': True, 'dms': []})
                dms = self.discord_client.get_dm_data()
                return jsonify({'success': True, 'dms': dms})
            except Exception as e:
                self.logger.error(f'Error getting DMs: {e}')
                return jsonify({'success': True, 'dms': []})
        
        @self.app.route('/api/messages/<int:channel_id>')
        def get_messages(channel_id):
            """Get messages for a channel"""
            try:
                page = request.args.get('page', 0, type=int)
                limit = request.args.get('limit', Config.MESSAGES_PER_PAGE, type=int)
                offset = page * limit
                
                messages = self.discord_client.get_message_data(channel_id, limit, offset)
                return jsonify({'success': True, 'messages': messages})
            except Exception as e:
                self.logger.error(f'Error getting messages for channel {channel_id}: {e}')
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/status')
        def get_status():
            """Get Discord client status"""
            try:
                if (self.discord_client and
                    hasattr(self.discord_client, 'user') and
                    self.discord_client.user and
                    not self.discord_client.is_closed()):
                    return jsonify({
                        'success': True,
                        'connected': True,
                        'user': {
                            'id': self.discord_client.user.id,
                            'name': str(self.discord_client.user),
                            'avatar': str(self.discord_client.user.avatar) if self.discord_client.user.avatar else None
                        }
                    })
                else:
                    return jsonify({'success': True, 'connected': False})
            except Exception as e:
                self.logger.error(f'Error getting status: {e}')
                return jsonify({'success': True, 'connected': False})

        @self.app.route('/api/setup', methods=['POST'])
        def setup_discord():
            """Setup Discord authentication"""
            try:
                data = request.get_json()

                # Check if using token or email/password
                if 'token' in data:
                    # Token authentication
                    token = data.get('token', '').strip()
                    if not token:
                        return jsonify({'success': False, 'error': 'Token is required'})

                    # Save token to config
                    from config import Config
                    Config.DISCORD_TOKEN = token

                    # Try to restart Discord client with token
                    if hasattr(self, 'main_app') and hasattr(self.main_app, 'restart_discord_client'):
                        success = self.main_app.restart_discord_client(token)
                        if success:
                            return jsonify({'success': True, 'message': 'Discord client connected successfully'})
                        else:
                            return jsonify({'success': False, 'error': 'Failed to connect Discord client'})
                    else:
                        return jsonify({'success': True, 'message': 'Token saved. Please restart the application.'})

                elif 'email' in data and 'password' in data:
                    # Email/password authentication
                    email = data.get('email', '').strip()
                    password = data.get('password', '').strip()

                    if not email or not password:
                        return jsonify({'success': False, 'error': 'Email and password are required'})

                    # Try to login and get token
                    if hasattr(self, 'main_app') and hasattr(self.main_app, 'login_with_credentials'):
                        result = self.main_app.login_with_credentials(email, password)
                        if result['success']:
                            return jsonify({'success': True, 'message': 'Discord client connected successfully'})
                        else:
                            return jsonify({'success': False, 'error': result['error']})
                    else:
                        return jsonify({'success': False, 'error': 'Email/password authentication not supported'})

                else:
                    return jsonify({'success': False, 'error': 'Either token or email/password is required'})

            except Exception as e:
                self.logger.error(f'Error setting up Discord: {e}')
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/refresh', methods=['POST'])
        def refresh_data():
            """Force refresh Discord data"""
            try:
                if not self.discord_client:
                    return jsonify({'success': False, 'error': 'Discord client not connected'})

                # Force refresh data
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                success = loop.run_until_complete(self.discord_client.force_refresh_data())
                loop.close()

                if success:
                    return jsonify({'success': True, 'message': 'Data refreshed successfully'})
                else:
                    return jsonify({'success': False, 'error': 'Failed to refresh data'})

            except Exception as e:
                self.logger.error(f'Error refreshing data: {e}')
                return jsonify({'success': False, 'error': str(e)})
    
    def setup_socketio(self):
        """Setup SocketIO events"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            self.logger.info('Client connected')
            emit('status', {'connected': True})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            self.logger.info('Client disconnected')
        
        @self.socketio.on('join_channel')
        def handle_join_channel(data):
            """Handle joining a channel for real-time updates"""
            channel_id = data.get('channel_id')
            if channel_id:
                # Join room for this channel
                from flask_socketio import join_room
                join_room(f'channel_{channel_id}')
                emit('joined_channel', {'channel_id': channel_id})
        
        @self.socketio.on('leave_channel')
        def handle_leave_channel(data):
            """Handle leaving a channel"""
            channel_id = data.get('channel_id')
            if channel_id:
                from flask_socketio import leave_room
                leave_room(f'channel_{channel_id}')
                emit('left_channel', {'channel_id': channel_id})
    
    def broadcast_new_message(self, message_data):
        """Broadcast new message to connected clients"""
        try:
            channel_id = message_data.get('channel_id')
            if channel_id:
                self.socketio.emit('new_message', message_data, room=f'channel_{channel_id}')
        except Exception as e:
            self.logger.error(f'Error broadcasting message: {e}')
    
    def broadcast_message_update(self, message_data):
        """Broadcast message update to connected clients"""
        try:
            channel_id = message_data.get('channel_id')
            if channel_id:
                self.socketio.emit('message_updated', message_data, room=f'channel_{channel_id}')
        except Exception as e:
            self.logger.error(f'Error broadcasting message update: {e}')
    
    def broadcast_message_delete(self, message_id, channel_id):
        """Broadcast message deletion to connected clients"""
        try:
            self.socketio.emit('message_deleted', {
                'message_id': message_id,
                'channel_id': channel_id
            }, room=f'channel_{channel_id}')
        except Exception as e:
            self.logger.error(f'Error broadcasting message deletion: {e}')
    
    def run(self, host=None, port=None, debug=None):
        """Run the web server"""
        host = host or Config.HOST
        port = port or Config.PORT
        debug = debug if debug is not None else Config.DEBUG

        self.logger.info(f'Starting web server on {host}:{port}')
        self.socketio.run(self.app, host=host, port=port, debug=debug, allow_unsafe_werkzeug=True)
