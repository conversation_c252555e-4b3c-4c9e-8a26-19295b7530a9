from flask import Flask, render_template, request, jsonify, redirect, url_for, session
from flask_socketio import <PERSON>cket<PERSON>, emit
import json
import logging
from typing import Optional
from config import Config

class WebServer:
    def __init__(self, discord_client):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = Config.SECRET_KEY
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        self.discord_client = discord_client
        self.discord_api = None  # Will be set when API client is created
        self.logger = logging.getLogger(__name__)

        self.setup_routes()
        self.setup_socketio()
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            """Main page"""
            if not Config.DISCORD_TOKEN:
                return redirect(url_for('setup'))
            return render_template('index.html')
        
        @self.app.route('/setup')
        def setup():
            """Setup page for Discord token"""
            return render_template('setup.html')
        
        @self.app.route('/api/guilds')
        def get_guilds():
            """Get all guilds"""
            try:
                if self.discord_api:
                    # Get fresh data from API and database
                    self.discord_api.get_guilds()

                # Always return from database
                from database import DatabaseManager
                db = DatabaseManager(Config.DATABASE_PATH)
                guilds = db.get_guilds()
                return jsonify({'success': True, 'guilds': guilds})
            except Exception as e:
                self.logger.error(f'Error getting guilds: {e}')
                return jsonify({'success': True, 'guilds': []})
        
        @self.app.route('/api/channels/<int:guild_id>')
        def get_channels(guild_id):
            """Get channels for a guild"""
            try:
                if self.discord_api:
                    # Get fresh data from API
                    self.discord_api.get_guild_channels(guild_id)

                # Return from database
                from database import DatabaseManager
                db = DatabaseManager(Config.DATABASE_PATH)
                channels = db.get_channels(guild_id)
                return jsonify({'success': True, 'channels': channels})
            except Exception as e:
                self.logger.error(f'Error getting channels for guild {guild_id}: {e}')
                return jsonify({'success': True, 'channels': []})
        
        @self.app.route('/api/dms')
        def get_dms():
            """Get DM channels"""
            try:
                if self.discord_api:
                    # Get fresh data from API
                    self.discord_api.get_dm_channels()

                # Return from database
                from database import DatabaseManager
                db = DatabaseManager(Config.DATABASE_PATH)
                dms = db.get_dm_channels()
                return jsonify({'success': True, 'dms': dms})
            except Exception as e:
                self.logger.error(f'Error getting DMs: {e}')
                return jsonify({'success': True, 'dms': []})
        
        @self.app.route('/api/messages/<int:channel_id>')
        def get_messages(channel_id):
            """Get messages for a channel"""
            try:
                page = request.args.get('page', 0, type=int)
                limit = request.args.get('limit', Config.MESSAGES_PER_PAGE, type=int)
                offset = page * limit

                if self.discord_api and page == 0:
                    # Get fresh messages from API for first page
                    self.discord_api.get_channel_messages(channel_id, limit)

                # Return from database
                from database import DatabaseManager
                db = DatabaseManager(Config.DATABASE_PATH)
                messages = db.get_messages(channel_id, limit, offset)
                return jsonify({'success': True, 'messages': messages})
            except Exception as e:
                self.logger.error(f'Error getting messages for channel {channel_id}: {e}')
                return jsonify({'success': True, 'messages': []})
        
        @self.app.route('/api/status')
        def get_status():
            """Get Discord client status"""
            try:
                if self.discord_api and self.discord_api.is_ready():
                    user_info = self.discord_api.get_user_info()
                    return jsonify({
                        'success': True,
                        'connected': True,
                        'user': {
                            'id': user_info['id'],
                            'name': user_info['username'],
                            'avatar': user_info.get('avatar')
                        }
                    })
                else:
                    return jsonify({'success': True, 'connected': False})
            except Exception as e:
                self.logger.error(f'Error getting status: {e}')
                return jsonify({'success': True, 'connected': False})

        @self.app.route('/api/setup', methods=['POST'])
        def setup_discord():
            """Setup Discord authentication"""
            try:
                data = request.get_json()

                # Check if using token or email/password
                if 'token' in data:
                    # Token authentication
                    token = data.get('token', '').strip()
                    if not token:
                        return jsonify({'success': False, 'error': 'Token is required'})

                    # Save token to config
                    from config import Config
                    Config.DISCORD_TOKEN = token

                    # Try to restart Discord client with token
                    if hasattr(self, 'main_app') and hasattr(self.main_app, 'restart_discord_client'):
                        success = self.main_app.restart_discord_client(token)
                        if success:
                            return jsonify({'success': True, 'message': 'Discord client connected successfully'})
                        else:
                            return jsonify({'success': False, 'error': 'Failed to connect Discord client'})
                    else:
                        return jsonify({'success': True, 'message': 'Token saved. Please restart the application.'})

                elif 'email' in data and 'password' in data:
                    # Email/password authentication
                    email = data.get('email', '').strip()
                    password = data.get('password', '').strip()

                    if not email or not password:
                        return jsonify({'success': False, 'error': 'Email and password are required'})

                    # Try to login and get token
                    if hasattr(self, 'main_app') and hasattr(self.main_app, 'login_with_credentials'):
                        result = self.main_app.login_with_credentials(email, password)
                        if result['success']:
                            return jsonify({'success': True, 'message': 'Discord client connected successfully'})
                        else:
                            return jsonify({'success': False, 'error': result['error']})
                    else:
                        return jsonify({'success': False, 'error': 'Email/password authentication not supported'})

                else:
                    return jsonify({'success': False, 'error': 'Either token or email/password is required'})

            except Exception as e:
                self.logger.error(f'Error setting up Discord: {e}')
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/refresh', methods=['POST'])
        def refresh_data():
            """Force refresh Discord data"""
            try:
                self.logger.info("Refresh data request received")

                if not self.discord_api:
                    self.logger.error("Discord API not connected")
                    return jsonify({'success': False, 'error': 'Discord API not connected'})

                self.logger.info(f"Discord API status: ready={self.discord_api.is_ready()}")

                # Check if API is ready
                if not self.discord_api.is_ready():
                    return jsonify({'success': False, 'error': 'Discord API not ready. Please reconnect.'})

                # Force refresh data
                success = self.discord_api.refresh_all_data()

                if success:
                    return jsonify({'success': True, 'message': 'Data refreshed successfully'})
                else:
                    return jsonify({'success': False, 'error': 'Failed to refresh data'})

            except Exception as e:
                self.logger.error(f'Error refreshing data: {e}')
                import traceback
                self.logger.error(f'Traceback: {traceback.format_exc()}')
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/debug')
        def debug_info():
            """Get debug information about Discord API"""
            try:
                if not self.discord_api:
                    return jsonify({
                        'api_exists': False,
                        'ready': False,
                        'user': None,
                        'guilds': 0,
                        'error': 'No Discord API client'
                    })

                # Get counts from database
                from database import DatabaseManager
                db = DatabaseManager(Config.DATABASE_PATH)
                guilds = db.get_guilds()
                dms = db.get_dm_channels()

                user_info = self.discord_api.get_user_info()

                return jsonify({
                    'api_exists': True,
                    'ready': self.discord_api.is_ready(),
                    'user': user_info['username'] if user_info else None,
                    'guilds': len(guilds),
                    'dms': len(dms),
                    'token_length': len(self.discord_api.token) if hasattr(self.discord_api, 'token') else 0
                })

            except Exception as e:
                return jsonify({'error': str(e)})

        @self.app.route('/api/test-token', methods=['POST'])
        def test_token():
            """Test if a token works with Discord API"""
            try:
                data = request.get_json()
                token = data.get('token', '').strip()

                if not token:
                    return jsonify({'success': False, 'error': 'Token required'})

                # Test token with Discord API
                import requests
                headers = {
                    'Authorization': f'Bot {token}' if not token.startswith('Bot ') else token,
                    'Content-Type': 'application/json'
                }

                # Try user token first
                if not token.startswith('Bot '):
                    headers['Authorization'] = token

                response = requests.get('https://discord.com/api/v9/users/@me', headers=headers)

                if response.status_code == 200:
                    user_data = response.json()
                    return jsonify({
                        'success': True,
                        'user': {
                            'id': user_data.get('id'),
                            'username': user_data.get('username'),
                            'discriminator': user_data.get('discriminator')
                        }
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': f'Token test failed: {response.status_code} - {response.text[:200]}'
                    })

            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/load-messages/<int:channel_id>', methods=['POST'])
        def load_channel_messages(channel_id):
            """Load messages for a specific channel on demand"""
            try:
                if not self.discord_api:
                    return jsonify({'success': False, 'error': 'Discord API not connected'})

                # Load messages for this channel
                messages = self.discord_api.get_channel_messages(channel_id, 50)

                return jsonify({
                    'success': True,
                    'message': f'Loaded {len(messages)} messages',
                    'count': len(messages)
                })

            except Exception as e:
                self.logger.error(f'Error loading messages for channel {channel_id}: {e}')
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/load-channels/<int:guild_id>', methods=['POST'])
        def load_guild_channels(guild_id):
            """Load channels for a specific guild on demand"""
            try:
                if not self.discord_api:
                    return jsonify({'success': False, 'error': 'Discord API not connected'})

                # Load channels for this guild
                channels = self.discord_api.get_guild_channels(guild_id)

                return jsonify({
                    'success': True,
                    'message': f'Loaded {len(channels)} channels',
                    'count': len(channels)
                })

            except Exception as e:
                self.logger.error(f'Error loading channels for guild {guild_id}: {e}')
                return jsonify({'success': False, 'error': str(e)})
    
    def setup_socketio(self):
        """Setup SocketIO events"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            self.logger.info('Client connected')
            emit('status', {'connected': True})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            self.logger.info('Client disconnected')
        
        @self.socketio.on('join_channel')
        def handle_join_channel(data):
            """Handle joining a channel for real-time updates"""
            channel_id = data.get('channel_id')
            if channel_id:
                # Join room for this channel
                from flask_socketio import join_room
                join_room(f'channel_{channel_id}')
                emit('joined_channel', {'channel_id': channel_id})
        
        @self.socketio.on('leave_channel')
        def handle_leave_channel(data):
            """Handle leaving a channel"""
            channel_id = data.get('channel_id')
            if channel_id:
                from flask_socketio import leave_room
                leave_room(f'channel_{channel_id}')
                emit('left_channel', {'channel_id': channel_id})
    
    def broadcast_new_message(self, message_data):
        """Broadcast new message to connected clients"""
        try:
            channel_id = message_data.get('channel_id')
            if channel_id:
                self.socketio.emit('new_message', message_data, room=f'channel_{channel_id}')
        except Exception as e:
            self.logger.error(f'Error broadcasting message: {e}')
    
    def broadcast_message_update(self, message_data):
        """Broadcast message update to connected clients"""
        try:
            channel_id = message_data.get('channel_id')
            if channel_id:
                self.socketio.emit('message_updated', message_data, room=f'channel_{channel_id}')
        except Exception as e:
            self.logger.error(f'Error broadcasting message update: {e}')
    
    def broadcast_message_delete(self, message_id, channel_id):
        """Broadcast message deletion to connected clients"""
        try:
            self.socketio.emit('message_deleted', {
                'message_id': message_id,
                'channel_id': channel_id
            }, room=f'channel_{channel_id}')
        except Exception as e:
            self.logger.error(f'Error broadcasting message deletion: {e}')
    
    def run(self, host=None, port=None, debug=None):
        """Run the web server"""
        host = host or Config.HOST
        port = port or Config.PORT
        debug = debug if debug is not None else Config.DEBUG

        self.logger.info(f'Starting web server on {host}:{port}')
        self.socketio.run(self.app, host=host, port=port, debug=debug, allow_unsafe_werkzeug=True)
