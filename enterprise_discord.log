{"timestamp": "2025-06-30T14:16:10.801139", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:16:10.805568", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:16:10.805568", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.00442957878112793}
{"timestamp": "2025-06-30T14:16:10.806263", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.648985", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0010030269622802734}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.717505", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.717505", "level": "ERROR", "logger": "main", "message": "Configuration error: Discord token is required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.662757", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:19:41.662757", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.662757", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0005135536193847656}
{"timestamp": "2025-06-30T14:19:41.663763", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.718379", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.719386", "level": "ERROR", "logger": "main", "message": "Configuration error: Discord token is required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.494543", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:20:55.495511", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.495511", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0009679794311523438}
{"timestamp": "2025-06-30T14:20:55.496511", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.543946", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.544894", "level": "ERROR", "logger": "main", "message": "Configuration error: Discord token is required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.299977", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:22:11.299977", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.299977", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.00099945068359375}
{"timestamp": "2025-06-30T14:22:11.300713", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "WARNING", "logger": "main", "message": "Discord token not configured - setup required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "INFO", "logger": "main", "message": "Discord token not provided - web interface available for setup", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "INFO", "logger": "main", "message": "Starting web server on 127.0.0.1:5001", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:58.953956", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:22:58.953956", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:58.953956", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0}
{"timestamp": "2025-06-30T14:22:58.954955", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:59.008567", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:59.008567", "level": "WARNING", "logger": "main", "message": "Discord token not configured - setup required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:59.008567", "level": "INFO", "logger": "main", "message": "Discord token not provided - web interface available for setup", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:59.008567", "level": "INFO", "logger": "main", "message": "Starting web server on 127.0.0.1:5001", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:59.127702", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.00099945068359375, "rows_affected": null}
{"timestamp": "2025-06-30T14:22:59.142399", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005083084106445312, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:01.197628", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0015361309051513672, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:03.223813", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:05.240139", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:07.248820", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010230541229248047, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:09.276204", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:11.348149", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:12.663360", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info"}
{"timestamp": "2025-06-30T14:23:13.372971", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005075931549072266, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:13.912000", "level": "INFO", "logger": "discord.client", "message": "API call: GET https://discord.com/api/v9/users/@me", "module": "", "function": null, "line": 0, "api_method": "GET", "api_url": "https://discord.com/api/v9/users/@me", "status_code": 401, "duration": 1.2486395835876465, "request_type": "get_user", "attempt": 1}
{"timestamp": "2025-06-30T14:23:13.912000", "level": "ERROR", "logger": "discord.client", "message": "Auth error 401: {\"message\": \"401: Unauthorized\", \"code\": 0}", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:13.913007", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info", "duration": 1.2496459484100342}
{"timestamp": "2025-06-30T14:23:15.388765", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:17.415674", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:19.434901", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:21.463480", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:22.356796", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info"}
{"timestamp": "2025-06-30T14:23:22.357797", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:23.358546", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:23.596704", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:25.360393", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:25.360393", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info", "duration": 3.003596782684326}
{"timestamp": "2025-06-30T14:23:25.611767", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:27.623495", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:29.638202", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:31.651770", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:33.664667", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:35.742666", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:37.959142", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:39.819117", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:41.815873", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:43.823145", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:45.838701", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:47.846813", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010328292846679688, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:49.879055", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:51.893752", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:53.093301", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info"}
{"timestamp": "2025-06-30T14:23:53.093301", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:53.923536", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:54.106880", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:55.938836", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:56.121526", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:56.122032", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info", "duration": 3.02974009513855}
{"timestamp": "2025-06-30T14:23:57.947064", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:59.959002", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:24:01.978630", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:24:04.222468", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010056495666503906, "rows_affected": null}
