{"timestamp": "2025-06-30T14:16:10.801139", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:16:10.805568", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:16:10.805568", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.00442957878112793}
{"timestamp": "2025-06-30T14:16:10.806263", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.648985", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0010030269622802734}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.717505", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.717505", "level": "ERROR", "logger": "main", "message": "Configuration error: Discord token is required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.662757", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:19:41.662757", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.662757", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0005135536193847656}
{"timestamp": "2025-06-30T14:19:41.663763", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.718379", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.719386", "level": "ERROR", "logger": "main", "message": "Configuration error: Discord token is required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.494543", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:20:55.495511", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.495511", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0009679794311523438}
{"timestamp": "2025-06-30T14:20:55.496511", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.543946", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.544894", "level": "ERROR", "logger": "main", "message": "Configuration error: Discord token is required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.299977", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:22:11.299977", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.299977", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.00099945068359375}
{"timestamp": "2025-06-30T14:22:11.300713", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "WARNING", "logger": "main", "message": "Discord token not configured - setup required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "INFO", "logger": "main", "message": "Discord token not provided - web interface available for setup", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "INFO", "logger": "main", "message": "Starting web server on 127.0.0.1:5001", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:58.953956", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:22:58.953956", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:58.953956", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0}
{"timestamp": "2025-06-30T14:22:58.954955", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:59.008567", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:59.008567", "level": "WARNING", "logger": "main", "message": "Discord token not configured - setup required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:59.008567", "level": "INFO", "logger": "main", "message": "Discord token not provided - web interface available for setup", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:59.008567", "level": "INFO", "logger": "main", "message": "Starting web server on 127.0.0.1:5001", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:59.127702", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.00099945068359375, "rows_affected": null}
{"timestamp": "2025-06-30T14:22:59.142399", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005083084106445312, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:01.197628", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0015361309051513672, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:03.223813", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:05.240139", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:07.248820", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010230541229248047, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:09.276204", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:11.348149", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:12.663360", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info"}
{"timestamp": "2025-06-30T14:23:13.372971", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005075931549072266, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:13.912000", "level": "INFO", "logger": "discord.client", "message": "API call: GET https://discord.com/api/v9/users/@me", "module": "", "function": null, "line": 0, "api_method": "GET", "api_url": "https://discord.com/api/v9/users/@me", "status_code": 401, "duration": 1.2486395835876465, "request_type": "get_user", "attempt": 1}
{"timestamp": "2025-06-30T14:23:13.912000", "level": "ERROR", "logger": "discord.client", "message": "Auth error 401: {\"message\": \"401: Unauthorized\", \"code\": 0}", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:13.913007", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info", "duration": 1.2496459484100342}
{"timestamp": "2025-06-30T14:23:15.388765", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:17.415674", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:19.434901", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:21.463480", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:22.356796", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info"}
{"timestamp": "2025-06-30T14:23:22.357797", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:23.358546", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:23.596704", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:25.360393", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:25.360393", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info", "duration": 3.003596782684326}
{"timestamp": "2025-06-30T14:23:25.611767", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:27.623495", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:29.638202", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:31.651770", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:33.664667", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:35.742666", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:37.959142", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:39.819117", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:41.815873", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:43.823145", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:45.838701", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:47.846813", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010328292846679688, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:49.879055", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:51.893752", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:53.093301", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info"}
{"timestamp": "2025-06-30T14:23:53.093301", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:53.923536", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:54.106880", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:55.938836", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:56.121526", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:23:56.122032", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info", "duration": 3.02974009513855}
{"timestamp": "2025-06-30T14:23:57.947064", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:23:59.959002", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:24:01.978630", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:24:04.222468", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010056495666503906, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:30.642948", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:26:30.642948", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:26:30.642948", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0}
{"timestamp": "2025-06-30T14:26:30.643951", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:26:30.699914", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:26:30.699914", "level": "WARNING", "logger": "main", "message": "Discord token not configured - setup required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:26:30.700915", "level": "INFO", "logger": "main", "message": "Discord token not provided - web interface available for setup", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:26:30.700915", "level": "INFO", "logger": "main", "message": "Starting web server on 127.0.0.1:5001", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:26:30.749163", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:30.795174", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:32.279153", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:34.283653", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:36.286220", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:38.322034", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010058879852294922, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:40.366122", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:42.459385", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:44.474486", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:46.491946", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0006105899810791016, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:46.817163", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info"}
{"timestamp": "2025-06-30T14:26:47.939305", "level": "INFO", "logger": "discord.client", "message": "API call: GET https://discord.com/api/v9/users/@me", "module": "", "function": null, "line": 0, "api_method": "GET", "api_url": "https://discord.com/api/v9/users/@me", "status_code": 200, "duration": 1.1221423149108887, "request_type": "get_user", "attempt": 1}
{"timestamp": "2025-06-30T14:26:47.939305", "level": "INFO", "logger": "discord.client", "message": "Authenticated as cozynightathome#0", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:26:47.939887", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info", "duration": 1.1221423149108887}
{"timestamp": "2025-06-30T14:26:47.939887", "level": "INFO", "logger": "main", "message": "Successfully authenticated as cozynightathome", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:26:48.500131", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:50.514500", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:51.238227", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0009958744049072266, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:52.548713", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010311603546142578, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:54.561444", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:59.039969", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:59.063120", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:59.115713", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:26:59.944401", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:00.537565", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0009984970092773438, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:00.601899", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:02.613026", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:04.632107", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:06.643439", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:27.119191", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:27:27.120190", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:27:27.120190", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0009992122650146484}
{"timestamp": "2025-06-30T14:27:27.122189", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:27:27.192804", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:27:27.192804", "level": "WARNING", "logger": "main", "message": "Discord token not configured - setup required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:27:27.193804", "level": "INFO", "logger": "main", "message": "Discord token not provided - web interface available for setup", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:27:27.193804", "level": "INFO", "logger": "main", "message": "Starting web server on 127.0.0.1:5001", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:27:27.280333", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:27.287851", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:27.531994", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:28.853184", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:30.893868", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:32.912734", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:34.935925", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:36.525213", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info"}
{"timestamp": "2025-06-30T14:27:36.954086", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:37.821442", "level": "INFO", "logger": "discord.client", "message": "API call: GET https://discord.com/api/v9/users/@me", "module": "", "function": null, "line": 0, "api_method": "GET", "api_url": "https://discord.com/api/v9/users/@me", "status_code": 200, "duration": 1.2952179908752441, "request_type": "get_user", "attempt": 1}
{"timestamp": "2025-06-30T14:27:37.822078", "level": "INFO", "logger": "discord.client", "message": "Authenticated as cozynightathome#0", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:27:37.822078", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info", "duration": 1.2968647480010986}
{"timestamp": "2025-06-30T14:27:37.822078", "level": "INFO", "logger": "main", "message": "Successfully authenticated as cozynightathome", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:27:38.980176", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:39.905623", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:40.994627", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:43.021625", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:27:45.045378", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:04.905282", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:04.909810", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:06.294341", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:06.297854", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010058879852294922, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:06.303859", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:06.307886", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.001008749008178711, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:06.310895", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:06.313895", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:06.318407", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:06.322414", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:08.344679", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:10.356758", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:12.442781", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:14.378862", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:16.403115", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:29.912961", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:29.916470", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005040168762207031, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:29.919478", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:29.926478", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0009996891021728516, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:32.092323", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:32.095324", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:34.122862", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:34.126864", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:37.202710", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:37.205708", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:40.294980", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.00099945068359375, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:43.413507", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:43.417608", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005984306335449219, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:54.919621", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:54.923623", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:54.927128", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:57.099158", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:57.104159", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:57.111316", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:59.126827", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:28:59.129835", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:02.209812", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:05.298499", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:05.301496", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:08.421661", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:19.926498", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:19.929517", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:22.103162", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:22.105168", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:22.110229", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:22.113230", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:22.115228", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:24.181304", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:25.471041", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:27.486632", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:30.333875", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:31.515368", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:33.521506", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:44.933122", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010004043579101562, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:48.143261", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0006191730499267578, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:48.147030", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:51.274135", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:51.284652", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0020139217376708984, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:54.320795", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:54.324025", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:54.326528", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:54.332549", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:54.336546", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:57.380965", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:59.435061", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:29:59.738943", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:03.868564", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:30:03.868564", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:03.868564", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0}
{"timestamp": "2025-06-30T14:30:03.869758", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:03.921503", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:03.921503", "level": "WARNING", "logger": "main", "message": "Discord token not configured - setup required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:03.921503", "level": "INFO", "logger": "main", "message": "Discord token not provided - web interface available for setup", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:03.921503", "level": "INFO", "logger": "main", "message": "Starting web server on 127.0.0.1:5001", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:09.217750", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005056858062744141, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:09.218758", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:09.224758", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:09.230280", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.001001119613647461, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:09.293885", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:10.254807", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:12.198620", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:12.276212", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:14.372784", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:16.381368", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:17.396795", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:36.944823", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:30:36.944823", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:36.944823", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0}
{"timestamp": "2025-06-30T14:30:36.946327", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:37.006408", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:37.006408", "level": "WARNING", "logger": "main", "message": "Discord token not configured - setup required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:37.006408", "level": "INFO", "logger": "main", "message": "Discord token not provided - web interface available for setup", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:37.006408", "level": "INFO", "logger": "main", "message": "Starting web server on 127.0.0.1:5001", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:37.174450", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:37.178700", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005054473876953125, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:37.186574", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0025634765625, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:37.196310", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0015058517456054688, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:37.420508", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0009999275207519531, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:37.422508", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0009999275207519531, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:37.422508", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:37.426508", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:37.428574", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0020012855529785156, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:37.429577", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.002058744430541992, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:38.609148", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:40.628557", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:42.839082", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:44.866753", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0009989738464355469, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:46.891911", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:49.033560", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010311603546142578, "rows_affected": null}
{"timestamp": "2025-06-30T14:30:51.164850", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info"}
{"timestamp": "2025-06-30T14:30:53.311454", "level": "INFO", "logger": "discord.client", "message": "API call: GET https://discord.com/api/v9/users/@me", "module": "", "function": null, "line": 0, "api_method": "GET", "api_url": "https://discord.com/api/v9/users/@me", "status_code": 200, "duration": 2.14660382270813, "request_type": "get_user", "attempt": 1}
{"timestamp": "2025-06-30T14:30:53.312459", "level": "INFO", "logger": "discord.client", "message": "Authenticated as cozynightathome#0", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:53.312459", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info", "duration": 2.148157835006714}
{"timestamp": "2025-06-30T14:30:53.312459", "level": "INFO", "logger": "main", "message": "Successfully authenticated as cozynightathome", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:30:53.313461", "level": "WARNING", "logger": "main", "message": "Failed to load guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:04.234008", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:04.234008", "level": "INFO", "logger": "main", "message": "No guilds in database, attempting to load from Discord...", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:04.235009", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds"}
{"timestamp": "2025-06-30T14:31:04.236010", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:05.248454", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:07.262260", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:07.262260", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds", "duration": 3.027250289916992}
{"timestamp": "2025-06-30T14:31:07.263258", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:07.263258", "level": "INFO", "logger": "main", "message": "Loaded 0 guilds from Discord", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:07.441148", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:08.741745", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0009970664978027344, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:08.752266", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:08.764300", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.004028797149658203, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:08.769820", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:08.772825", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:08.774821", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0009963512420654297, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:08.780346", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:08.784347", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:08.865210", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0010001659393310547, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:08.865210", "level": "INFO", "logger": "main", "message": "No guilds in database, attempting to load from Discord...", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:08.866212", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds"}
{"timestamp": "2025-06-30T14:31:08.866212", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:09.487403", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:09.487403", "level": "INFO", "logger": "main", "message": "No guilds in database, attempting to load from Discord...", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:09.488410", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds"}
{"timestamp": "2025-06-30T14:31:09.488410", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:09.775174", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:09.879178", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:10.063341", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:10.063341", "level": "INFO", "logger": "main", "message": "No guilds in database, attempting to load from Discord...", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:10.064340", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds"}
{"timestamp": "2025-06-30T14:31:10.064340", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:10.493034", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:11.074199", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:11.890126", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:11.890126", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds", "duration": 3.023913860321045}
{"timestamp": "2025-06-30T14:31:11.891127", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.001001119613647461, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:11.891127", "level": "INFO", "logger": "main", "message": "Loaded 0 guilds from Discord", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:12.501542", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:12.501542", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds", "duration": 3.0141398906707764}
{"timestamp": "2025-06-30T14:31:12.502440", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:12.502440", "level": "INFO", "logger": "main", "message": "Loaded 0 guilds from Discord", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:13.083674", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:13.083674", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds", "duration": 3.019334077835083}
{"timestamp": "2025-06-30T14:31:13.084673", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:13.084673", "level": "INFO", "logger": "main", "message": "Loaded 0 guilds from Discord", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:16.150178", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:31:16.151177", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:16.151177", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0009989738464355469}
{"timestamp": "2025-06-30T14:31:16.152179", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:16.211420", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:16.211420", "level": "WARNING", "logger": "main", "message": "Discord token not configured - setup required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:16.211420", "level": "INFO", "logger": "main", "message": "Discord token not provided - web interface available for setup", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:16.211420", "level": "INFO", "logger": "main", "message": "Starting web server on 127.0.0.1:5001", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:16.322266", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:16.685848", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:18.697375", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:20.713526", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010273456573486328, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:21.644809", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info"}
{"timestamp": "2025-06-30T14:31:22.738974", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:22.788622", "level": "INFO", "logger": "discord.client", "message": "API call: GET https://discord.com/api/v9/users/@me", "module": "", "function": null, "line": 0, "api_method": "GET", "api_url": "https://discord.com/api/v9/users/@me", "status_code": 200, "duration": 1.1432101726531982, "request_type": "get_user", "attempt": 1}
{"timestamp": "2025-06-30T14:31:22.788622", "level": "INFO", "logger": "discord.client", "message": "Authenticated as cozynightathome#0", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:22.789212", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_user_info", "module": "", "function": null, "line": 0, "operation": "get_user_info", "duration": 1.1444027423858643}
{"timestamp": "2025-06-30T14:31:22.789735", "level": "INFO", "logger": "main", "message": "Successfully authenticated as cozynightathome", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:22.789735", "level": "WARNING", "logger": "main", "message": "Failed to load guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:24.888343", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:24.888343", "level": "INFO", "logger": "main", "message": "No guilds in database, attempting to load from Discord...", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:24.889343", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds"}
{"timestamp": "2025-06-30T14:31:24.890342", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:24.908506", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010018348693847656, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:25.900403", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:27.904840", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:27.904840", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds", "duration": 3.0154974460601807}
{"timestamp": "2025-06-30T14:31:27.906364", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.001523733139038086, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:27.906364", "level": "INFO", "logger": "main", "message": "Loaded 0 guilds from Discord", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:27.923902", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:30.121110", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:30.121110", "level": "INFO", "logger": "main", "message": "No guilds in database, attempting to load from Discord...", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:30.122109", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds"}
{"timestamp": "2025-06-30T14:31:30.123108", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:31.137891", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:31.155936", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:31.156941", "level": "INFO", "logger": "main", "message": "No guilds in database, attempting to load from Discord...", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:31.156941", "level": "DEBUG", "logger": "discord.client", "message": "Starting operation: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds"}
{"timestamp": "2025-06-30T14:31:31.157945", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:32.161647", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:33.143580", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:33.143580", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds", "duration": 3.0214710235595703}
{"timestamp": "2025-06-30T14:31:33.144578", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:33.144578", "level": "INFO", "logger": "main", "message": "Loaded 0 guilds from Discord", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:34.171682", "level": "ERROR", "logger": "discord.client", "message": "Request error for https://discord.com/api/v9/users/@me/guilds: Event loop is closed", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:34.171682", "level": "INFO", "logger": "discord.client", "message": "Operation completed: get_guilds", "module": "", "function": null, "line": 0, "operation": "get_guilds", "duration": 3.014740228652954}
{"timestamp": "2025-06-30T14:31:34.172680", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on GUILDS", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "GUILDS", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:34.172680", "level": "INFO", "logger": "main", "message": "Loaded 0 guilds from Discord", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:31:49.162199", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:49.166712", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:49.170735", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:49.177252", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:49.198050", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0006468296051025391, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:49.201588", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:50.770867", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010013580322265625, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:50.774220", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005211830139160156, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:50.777843", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:50.781751", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:50.788659", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:52.921729", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:31:53.964098", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:09.931525", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:09.940053", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010018348693847656, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:16.229288", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0009860992431640625, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:18.011589", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:18.014425", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005309581756591797, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:34.937697", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0014119148254394531, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:34.939118", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:34.947378", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.158842", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.164841", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.173369", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0009975433349609375, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.177891", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.180888", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.184888", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0009992122650146484, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.188287", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.192288", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0009982585906982422, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.198805", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.201807", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010039806365966797, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.206318", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0005106925964355469, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.210331", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.213332", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:39.216850", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:44.370996", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:44.374996", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:59.944203", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:32:59.953731", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010008811950683594, "rows_affected": null}
{"timestamp": "2025-06-30T14:33:04.162844", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:33:04.174373", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:33:04.176885", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:33:06.235767", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:33:06.238778", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:33:06.241780", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:33:06.244780", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0010008811950683594, "rows_affected": null}
{"timestamp": "2025-06-30T14:33:06.247850", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
{"timestamp": "2025-06-30T14:33:06.251858", "level": "INFO", "logger": "database", "message": "Database operation: SELECT on MESSAGES", "module": "", "function": null, "line": 0, "db_operation": "SELECT", "db_table": "MESSAGES", "duration": 0.0, "rows_affected": null}
