{"timestamp": "2025-06-30T14:16:10.801139", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:16:10.805568", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:16:10.805568", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.00442957878112793}
{"timestamp": "2025-06-30T14:16:10.806263", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.648985", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0010030269622802734}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.717505", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.717505", "level": "ERROR", "logger": "main", "message": "Configuration error: Discord token is required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.662757", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:19:41.662757", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.662757", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0005135536193847656}
{"timestamp": "2025-06-30T14:19:41.663763", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.718379", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:41.719386", "level": "ERROR", "logger": "main", "message": "Configuration error: Discord token is required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.494543", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:20:55.495511", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.495511", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0009679794311523438}
{"timestamp": "2025-06-30T14:20:55.496511", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.543946", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:20:55.544894", "level": "ERROR", "logger": "main", "message": "Configuration error: Discord token is required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.299977", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:22:11.299977", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.299977", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.00099945068359375}
{"timestamp": "2025-06-30T14:22:11.300713", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "WARNING", "logger": "main", "message": "Discord token not configured - setup required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "INFO", "logger": "main", "message": "Discord token not provided - web interface available for setup", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:22:11.356931", "level": "INFO", "logger": "main", "message": "Starting web server on 127.0.0.1:5001", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
