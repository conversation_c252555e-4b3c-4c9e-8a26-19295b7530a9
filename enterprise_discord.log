{"timestamp": "2025-06-30T14:16:10.801139", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:16:10.805568", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:16:10.805568", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.00442957878112793}
{"timestamp": "2025-06-30T14:16:10.806263", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.648985", "level": "DEBUG", "logger": "database", "message": "Starting operation: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization"}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Database schema initialized successfully", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Operation completed: schema_initialization", "module": "", "function": null, "line": 0, "operation": "schema_initialization", "duration": 0.0010030269622802734}
{"timestamp": "2025-06-30T14:19:00.649988", "level": "INFO", "logger": "database", "message": "Background database tasks started", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.717505", "level": "INFO", "logger": "main", "message": "Starting Enterprise Discord Logger", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
{"timestamp": "2025-06-30T14:19:00.717505", "level": "ERROR", "logger": "main", "message": "Configuration error: Discord token is required", "module": "enterprise_logger", "function": "_log_with_metrics", "line": 101}
