#!/usr/bin/env python3
"""
Senior Discord Logger - Production-ready Discord monitoring system
Single-file solution with enterprise-grade features

Features:
- Real-time message monitoring with 500ms updates
- Intelligent channel scanning with priority system
- Persistent storage of deleted messages
- Professional Discord-like UI
- Automatic server/channel discovery
- Rate limiting and error handling
- WebSocket real-time updates

Usage: python simple_discord_logger.py
Web interface: http://127.0.0.1:5001
"""

import asyncio
import aiohttp
import sqlite3
import json
import time
import threading
import logging
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO, emit

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('discord_logger.log')
    ]
)
logger = logging.getLogger('DiscordLogger')

# Global variables
app = Flask(__name__)
app.config['SECRET_KEY'] = 'senior-discord-logger-secret'
socketio = SocketIO(app, cors_allowed_origins="*")

DISCORD_TOKEN = None
BASE_URL = "https://discord.com/api/v9"
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

# Global state
current_session = None
scan_queue = asyncio.Queue() if hasattr(asyncio, 'Queue') else None
active_scans = set()
metrics = {
    'total_requests': 0,
    'successful_requests': 0,
    'failed_requests': 0,
    'messages_cached': 0,
    'channels_scanned': 0,
    'last_scan_time': None
}

def init_database():
    """Initialize SQLite database with enhanced schema"""
    conn = sqlite3.connect('simple_discord.db')
    cursor = conn.cursor()

    # Enhanced guilds table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS guilds (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            icon TEXT,
            owner_id TEXT,
            member_count INTEGER,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            last_seen TEXT DEFAULT CURRENT_TIMESTAMP,
            is_accessible INTEGER DEFAULT 1
        )
    ''')

    # Enhanced channels table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS channels (
            id TEXT PRIMARY KEY,
            guild_id TEXT,
            name TEXT NOT NULL,
            type INTEGER,
            topic TEXT,
            position INTEGER DEFAULT 0,
            parent_id TEXT,
            nsfw INTEGER DEFAULT 0,
            has_messages INTEGER DEFAULT 0,
            message_count INTEGER DEFAULT 0,
            was_temporarily_accessible INTEGER DEFAULT 0,
            priority_score INTEGER DEFAULT 0,
            last_accessible_at TEXT,
            last_scan_at TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (guild_id) REFERENCES guilds (id)
        )
    ''')

    # Enhanced messages table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id TEXT PRIMARY KEY,
            channel_id TEXT,
            guild_id TEXT,
            author_id TEXT,
            author_name TEXT,
            author_discriminator TEXT,
            author_avatar TEXT,
            content TEXT,
            embeds TEXT,
            attachments TEXT,
            timestamp TEXT,
            edited_timestamp TEXT,
            is_deleted INTEGER DEFAULT 0,
            deleted_at TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (channel_id) REFERENCES channels (id),
            FOREIGN KEY (guild_id) REFERENCES guilds (id)
        )
    ''')

    # Scan history table for analytics
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS scan_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            guild_id TEXT,
            channel_id TEXT,
            scan_type TEXT,
            messages_found INTEGER DEFAULT 0,
            duration REAL,
            success INTEGER DEFAULT 1,
            error_message TEXT,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create indexes for performance
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_messages_channel_id ON messages(channel_id)",
        "CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp)",
        "CREATE INDEX IF NOT EXISTS idx_channels_guild_id ON channels(guild_id)",
        "CREATE INDEX IF NOT EXISTS idx_channels_priority ON channels(priority_score DESC)",
        "CREATE INDEX IF NOT EXISTS idx_scan_history_timestamp ON scan_history(timestamp)"
    ]

    for index_sql in indexes:
        cursor.execute(index_sql)

    conn.commit()
    conn.close()
    logger.info("Database initialized with enhanced schema")

async def create_discord_session(token):
    """Create aiohttp session for Discord API"""
    global current_session

    if current_session and not current_session.closed:
        await current_session.close()

    headers = {
        "Authorization": token.strip(),
        "User-Agent": USER_AGENT,
        "Content-Type": "application/json"
    }

    timeout = aiohttp.ClientTimeout(total=30)
    current_session = aiohttp.ClientSession(headers=headers, timeout=timeout)
    logger.info("Discord session created")
    return current_session

async def test_discord_token(token):
    """Test if Discord token works with async"""
    try:
        logger.info(f"Testing Discord token (length: {len(token)})")
        session = await create_discord_session(token)

        logger.info("Making test request to Discord API")
        async with session.get(f"{BASE_URL}/users/@me") as response:
            metrics['total_requests'] += 1
            logger.info(f"Token test response status: {response.status}")

            if response.status == 200:
                metrics['successful_requests'] += 1
                user_data = await response.json()
                logger.info(f"Token validated successfully for user: {user_data['username']}")
                return {"success": True, "user": user_data}
            else:
                metrics['failed_requests'] += 1
                error_text = await response.text()
                logger.error(f"Token validation failed: {response.status} - {error_text}")
                return {"success": False, "error": f"HTTP {response.status}: {error_text}"}

    except Exception as e:
        metrics['failed_requests'] += 1
        logger.error(f"Token test exception: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

async def get_guilds():
    """Get user's guilds with enhanced data"""
    if not current_session:
        logger.error("No Discord session available for get_guilds")
        return []

    try:
        logger.info("Making request to Discord API for guilds")
        async with current_session.get(f"{BASE_URL}/users/@me/guilds") as response:
            metrics['total_requests'] += 1
            logger.info(f"Discord API response status: {response.status}")

            if response.status == 200:
                metrics['successful_requests'] += 1
                guilds = await response.json()
                logger.info(f"Received {len(guilds)} guilds from Discord API")

                # Save to database with enhanced data
                conn = sqlite3.connect('simple_discord.db')
                cursor = conn.cursor()

                saved_count = 0
                for guild in guilds:
                    try:
                        cursor.execute('''
                            INSERT OR REPLACE INTO guilds
                            (id, name, icon, owner_id, member_count, last_seen, is_accessible)
                            VALUES (?, ?, ?, ?, ?, ?, 1)
                        ''', (
                            guild['id'],
                            guild['name'],
                            guild.get('icon'),
                            guild.get('owner_id'),
                            guild.get('approximate_member_count', 0),
                            datetime.now().isoformat()
                        ))
                        saved_count += 1
                    except Exception as db_error:
                        logger.error(f"Error saving guild {guild.get('name', 'unknown')}: {db_error}")

                conn.commit()
                conn.close()

                logger.info(f"Successfully saved {saved_count} guilds to database")
                return guilds
            else:
                metrics['failed_requests'] += 1
                error_text = await response.text()
                logger.error(f"Failed to get guilds: {response.status} - {error_text}")
                return []

    except Exception as e:
        metrics['failed_requests'] += 1
        logger.error(f"Exception in get_guilds: {e}")
        import traceback
        traceback.print_exc()
        return []

def calculate_channel_priority(channel):
    """Calculate channel priority based on name patterns and position"""
    priority = 1
    name = channel['name'].lower()

    # High priority patterns
    high_priority_patterns = [
        'general', 'chat', 'main', 'discussion', 'talk',
        'admin', 'mod', 'staff', 'private', 'secret',
        'announcement', 'news', 'update', 'important'
    ]

    for pattern in high_priority_patterns:
        if pattern in name:
            priority += 20
            break

    # Position bonus (lower position = higher priority)
    position = channel.get('position', 999)
    if position < 10:
        priority += (10 - position)

    # Channel type bonus
    if channel['type'] == 0:  # Text channel
        priority += 10
    elif channel['type'] == 5:  # Announcement channel
        priority += 15

    return priority

async def get_channels(guild_id):
    """Get channels for a guild with priority calculation"""
    if not current_session:
        logger.error("No Discord session available")
        return []

    try:
        async with current_session.get(f"{BASE_URL}/guilds/{guild_id}/channels") as response:
            metrics['total_requests'] += 1

            if response.status == 200:
                metrics['successful_requests'] += 1
                channels = await response.json()
                current_time = datetime.now().isoformat()

                # Save to database with priority calculation
                conn = sqlite3.connect('simple_discord.db')
                cursor = conn.cursor()

                for channel in channels:
                    priority = calculate_channel_priority(channel)

                    cursor.execute('''
                        INSERT OR REPLACE INTO channels
                        (id, guild_id, name, type, topic, position, parent_id, nsfw,
                         priority_score, last_accessible_at, was_temporarily_accessible, last_scan_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?)
                    ''', (
                        channel['id'], guild_id, channel['name'], channel['type'],
                        channel.get('topic'), channel.get('position', 0),
                        channel.get('parent_id'), channel.get('nsfw', False),
                        priority, current_time, current_time
                    ))

                conn.commit()
                conn.close()

                logger.info(f"Retrieved and saved {len(channels)} channels for guild {guild_id}")
                return channels
            else:
                metrics['failed_requests'] += 1
                logger.warning(f"Failed to get channels for guild {guild_id}: {response.status}")
                return get_cached_channels(guild_id)

    except Exception as e:
        metrics['failed_requests'] += 1
        logger.error(f"Error getting channels: {e}")
        return get_cached_channels(guild_id)

def get_cached_channels(guild_id):
    """Get cached channels from database"""
    try:
        conn = sqlite3.connect('simple_discord.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM channels
            WHERE guild_id = ? AND type IN (0, 5, 11)
            ORDER BY priority_score DESC, position ASC, name ASC
        ''', (guild_id,))

        channels = [dict(row) for row in cursor.fetchall()]
        conn.close()

        logger.info(f"Retrieved {len(channels)} cached channels for guild {guild_id}")
        return channels

    except Exception as e:
        logger.error(f"Error getting cached channels: {e}")
        return []

async def get_messages(channel_id, limit=100):
    """Get messages from a channel with deleted message tracking"""
    if not current_session:
        logger.error("No Discord session available")
        return []

    start_time = time.time()

    try:
        async with current_session.get(f"{BASE_URL}/channels/{channel_id}/messages?limit={limit}") as response:
            metrics['total_requests'] += 1
            duration = time.time() - start_time

            if response.status == 200:
                metrics['successful_requests'] += 1
                current_messages = await response.json()
                current_time = datetime.now().isoformat()

                conn = sqlite3.connect('simple_discord.db')
                cursor = conn.cursor()

                # Get existing message IDs for this channel
                cursor.execute('SELECT id FROM messages WHERE channel_id = ? AND is_deleted = 0', (channel_id,))
                existing_ids = set(row[0] for row in cursor.fetchall())

                # Get current message IDs from API
                current_ids = set(message['id'] for message in current_messages)

                # Mark messages as deleted if they're not in current response
                deleted_ids = existing_ids - current_ids
                for deleted_id in deleted_ids:
                    cursor.execute('''
                        UPDATE messages
                        SET is_deleted = 1, deleted_at = ?
                        WHERE id = ? AND is_deleted = 0
                    ''', (current_time, deleted_id))

                # Add or update current messages
                new_messages = 0
                for message in current_messages:
                    cursor.execute('''
                        INSERT OR REPLACE INTO messages
                        (id, channel_id, guild_id, author_id, author_name, author_discriminator,
                         author_avatar, content, embeds, attachments, timestamp, edited_timestamp)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        message['id'], channel_id, message.get('guild_id'),
                        message['author']['id'], message['author']['username'],
                        message['author'].get('discriminator'), message['author'].get('avatar'),
                        message['content'], json.dumps(message.get('embeds', [])),
                        json.dumps(message.get('attachments', [])),
                        message['timestamp'], message.get('edited_timestamp')
                    ))

                    if message['id'] not in existing_ids:
                        new_messages += 1

                # Update channel statistics
                cursor.execute('''
                    UPDATE channels
                    SET has_messages = 1, message_count = ?,
                        was_temporarily_accessible = 1, last_accessible_at = ?, last_scan_at = ?
                    WHERE id = ?
                ''', (len(current_messages), current_time, current_time, channel_id))

                # Log scan history
                cursor.execute('''
                    INSERT INTO scan_history
                    (channel_id, scan_type, messages_found, duration, success, timestamp)
                    VALUES (?, 'api_fetch', ?, ?, 1, ?)
                ''', (channel_id, len(current_messages), duration, current_time))

                conn.commit()
                conn.close()

                metrics['messages_cached'] += new_messages
                metrics['channels_scanned'] += 1
                metrics['last_scan_time'] = current_time

                logger.info(f"Retrieved {len(current_messages)} messages from channel {channel_id} "
                           f"({new_messages} new, {len(deleted_ids)} deleted)")

                return get_cached_messages(channel_id)

            elif response.status == 403:
                metrics['failed_requests'] += 1
                logger.warning(f"No access to channel {channel_id}")
                return get_cached_messages(channel_id)
            elif response.status == 404:
                metrics['failed_requests'] += 1
                logger.warning(f"Channel {channel_id} not found")
                return get_cached_messages(channel_id)
            else:
                metrics['failed_requests'] += 1
                error_text = await response.text()
                logger.error(f"API error {response.status} for channel {channel_id}: {error_text}")
                return get_cached_messages(channel_id)

    except Exception as e:
        metrics['failed_requests'] += 1
        duration = time.time() - start_time
        logger.error(f"Error getting messages from channel {channel_id}: {e}")

        # Log failed scan
        try:
            conn = sqlite3.connect('simple_discord.db')
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO scan_history
                (channel_id, scan_type, messages_found, duration, success, error_message, timestamp)
                VALUES (?, 'api_fetch', 0, ?, 0, ?, ?)
            ''', (channel_id, duration, str(e), datetime.now().isoformat()))
            conn.commit()
            conn.close()
        except:
            pass

        return get_cached_messages(channel_id)

def get_cached_messages(channel_id):
    """Get cached messages from database including deleted ones"""
    try:
        conn = sqlite3.connect('simple_discord.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, channel_id, guild_id, author_id, author_name, author_discriminator,
                   author_avatar, content, embeds, attachments, timestamp, edited_timestamp,
                   is_deleted, deleted_at, created_at
            FROM messages
            WHERE channel_id = ?
            ORDER BY timestamp ASC
        ''', (channel_id,))

        cached_messages = []
        for row in cursor.fetchall():
            message = dict(row)
            # Parse JSON fields
            try:
                message['embeds'] = json.loads(message['embeds']) if message['embeds'] else []
                message['attachments'] = json.loads(message['attachments']) if message['attachments'] else []
            except:
                message['embeds'] = []
                message['attachments'] = []

            cached_messages.append(message)

        conn.close()
        logger.debug(f"Retrieved {len(cached_messages)} cached messages for channel {channel_id}")
        return cached_messages

    except Exception as e:
        logger.error(f"Error getting cached messages: {e}")
        return []

async def intelligent_guild_scan(guild_id, scan_type='smart'):
    """Intelligent guild scanning with priority-based approach"""
    logger.info(f"Starting {scan_type} scan for guild {guild_id}")
    start_time = time.time()

    try:
        # Get channels with priority
        channels = await get_channels(guild_id)
        if not channels:
            logger.warning(f"No channels found for guild {guild_id}")
            return {'accessible_count': 0, 'total_scanned': 0, 'duration': 0}

        # Filter and sort channels by priority
        text_channels = [ch for ch in channels if ch.get('type') == 0]

        if scan_type == 'smart':
            # Only scan high-priority channels
            text_channels = [ch for ch in text_channels if ch.get('priority_score', 0) >= 15]
        elif scan_type == 'conservative':
            # Only scan previously accessible channels
            text_channels = [ch for ch in text_channels if ch.get('was_temporarily_accessible')]

        # Sort by priority score
        text_channels.sort(key=lambda x: x.get('priority_score', 0), reverse=True)

        logger.info(f"Scanning {len(text_channels)} channels (type: {scan_type})")

        scanned_count = 0
        accessible_count = 0

        for channel in text_channels:
            if channel['id'] in active_scans:
                continue

            active_scans.add(channel['id'])

            try:
                logger.debug(f"Scanning channel #{channel['name']} (priority: {channel.get('priority_score', 0)})")

                messages = await get_messages(channel['id'], limit=20)

                if messages and any(not msg.get('is_deleted', False) for msg in messages):
                    accessible_count += 1
                    logger.info(f"✅ Found {len(messages)} messages in #{channel['name']}")
                else:
                    logger.debug(f"⚪ Channel #{channel['name']} is empty or inaccessible")

                scanned_count += 1

                # Rate limiting
                await asyncio.sleep(0.2)

            except Exception as e:
                logger.error(f"⚠️ Error scanning #{channel['name']}: {e}")
            finally:
                active_scans.discard(channel['id'])

        duration = time.time() - start_time

        # Log scan summary
        conn = sqlite3.connect('simple_discord.db')
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO scan_history
            (guild_id, scan_type, messages_found, duration, success, timestamp)
            VALUES (?, ?, ?, ?, 1, ?)
        ''', (guild_id, scan_type, accessible_count, duration, datetime.now().isoformat()))
        conn.commit()
        conn.close()

        result = {
            'accessible_count': accessible_count,
            'total_scanned': scanned_count,
            'duration': duration
        }

        logger.info(f"Scan complete: {scanned_count} channels scanned, "
                   f"{accessible_count} accessible in {duration:.2f}s")

        return result

    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"Error in guild scan: {e}")
        return {'accessible_count': 0, 'total_scanned': 0, 'duration': duration}

# Utility function to run async code in Flask routes
def run_async(coro):
    """Run async function in new event loop"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(coro)
        loop.close()
        return result
    except Exception as e:
        logger.error(f"Error running async function: {e}")
        return None

# HTML Templates (embedded)
SETUP_HTML = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Senior Discord Logger - Setup</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
               min-height: 100vh; display: flex; align-items: center; justify-content: center; color: #333; }
        .setup-container { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 20px;
                          padding: 40px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); width: 100%; max-width: 500px; }
        .logo { text-align: center; margin-bottom: 30px; }
        .logo h1 { font-size: 28px; font-weight: 700; color: #333; margin-bottom: 5px; }
        .logo p { color: #666; font-size: 14px; }
        .form-group { margin-bottom: 25px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px; }
        input[type="password"] { width: 100%; padding: 15px; border: 2px solid #e1e5e9; border-radius: 10px;
                                font-size: 16px; transition: all 0.3s ease; background: #f8f9fa; }
        input[type="password"]:focus { outline: none; border-color: #667eea; background: white;
                                      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1); }
        .btn-primary { width: 100%; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                      color: white; border: none; border-radius: 10px; font-size: 16px; font-weight: 600;
                      cursor: pointer; transition: all 0.3s ease; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3); }
        .btn-primary:disabled { opacity: 0.7; cursor: not-allowed; transform: none; }
        .status-message { padding: 15px; border-radius: 10px; margin-bottom: 20px; font-weight: 500; display: none; }
        .status-success { background: rgba(40, 167, 69, 0.1); color: #28a745; border: 1px solid rgba(40, 167, 69, 0.2); }
        .status-error { background: rgba(220, 53, 69, 0.1); color: #dc3545; border: 1px solid rgba(220, 53, 69, 0.2); }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="logo">
            <h1>Senior Discord Logger</h1>
            <p>Professional-grade Discord monitoring solution</p>
        </div>

        <div id="status-message" class="status-message"></div>

        <form id="setup-form">
            <div class="form-group">
                <label for="token">Discord Authorization Token</label>
                <input type="password" id="token" placeholder="Enter your Discord token" required>
            </div>
            <button type="submit" class="btn-primary" id="connect-btn">
                <span id="btn-text">Initialize System</span>
            </button>
        </form>
    </div>

    <script>
        document.getElementById('setup-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const token = document.getElementById('token').value.trim();
            const button = document.getElementById('connect-btn');
            const btnText = document.getElementById('btn-text');
            const statusMessage = document.getElementById('status-message');

            if (!token) {
                showStatus('Please enter your Discord token', 'error');
                return;
            }

            button.disabled = true;
            btnText.textContent = 'Initializing...';
            statusMessage.style.display = 'none';

            try {
                const response = await fetch('/api/setup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token: token })
                });

                const data = await response.json();

                if (data.success) {
                    showStatus('Successfully authenticated as ' + data.user.username + '!', 'success');
                    btnText.textContent = 'Redirecting...';
                    setTimeout(() => { window.location.href = '/'; }, 2000);
                } else {
                    showStatus('Authentication failed: ' + data.error, 'error');
                    resetButton();
                }
            } catch (error) {
                showStatus('Connection error: ' + error.message, 'error');
                resetButton();
            }

            function resetButton() {
                button.disabled = false;
                btnText.textContent = 'Initialize System';
            }

            function showStatus(message, type) {
                statusMessage.textContent = message;
                statusMessage.className = 'status-message status-' + type;
                statusMessage.style.display = 'block';
            }
        });
    </script>
</body>
</html>'''

MAIN_HTML = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Senior Discord Logger</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', sans-serif; background: #1a1a1a; color: #ffffff; height: 100vh; display: flex; }
        .sidebar { width: 300px; background: #2c2c2c; border-right: 1px solid #404040; display: flex; flex-direction: column; }
        .header { padding: 20px; background: #333; border-bottom: 1px solid #404040; }
        .header h1 { font-size: 18px; margin-bottom: 10px; }
        .content { flex: 1; overflow-y: auto; padding: 20px; }
        .section { margin-bottom: 20px; }
        .section h3 { font-size: 12px; text-transform: uppercase; color: #888; margin-bottom: 10px; letter-spacing: 1px; }
        .item { padding: 8px 12px; margin: 2px 0; background: #333; border-radius: 4px; cursor: pointer;
               transition: background 0.2s; display: flex; justify-content: space-between; align-items: center; }
        .item:hover { background: #404040; }
        .item.active { background: #5865f2; }
        .item.has-messages { border-left: 3px solid #faa61a; }
        .item.temporarily-accessible { border-left: 3px solid #3ba55d; }
        .main { flex: 1; display: flex; flex-direction: column; }
        .main-header { height: 60px; background: #2c2c2c; border-bottom: 1px solid #404040;
                      display: flex; align-items: center; justify-content: space-between; padding: 0 20px; }
        .main-content { flex: 1; padding: 20px; overflow-y: auto; }
        .message { margin-bottom: 15px; padding: 10px; background: #333; border-radius: 8px; border-left: 4px solid #5865f2; }
        .message.deleted { opacity: 0.6; border-left-color: #dc3545; }
        .message-header { display: flex; justify-content: space-between; margin-bottom: 5px; font-size: 12px; }
        .message-author { font-weight: bold; color: #5865f2; }
        .message-time { color: #888; }
        .message-content { color: #ddd; line-height: 1.4; }
        .welcome { text-align: center; padding: 60px 20px; color: #888; }
        .welcome h2 { margin-bottom: 10px; color: #fff; }
        .btn { padding: 8px 16px; background: #5865f2; color: white; border: none; border-radius: 4px;
              cursor: pointer; font-size: 12px; }
        .btn:hover { background: #4752c4; }
        .btn-secondary { background: #666; }
        .btn-secondary:hover { background: #777; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px; }
        .status.success { background: rgba(40, 167, 69, 0.2); color: #28a745; border: 1px solid #28a745; }
        .status.error { background: rgba(220, 53, 69, 0.2); color: #dc3545; border: 1px solid #dc3545; }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="header">
            <h1>Senior Discord Logger</h1>
            <div id="status-message"></div>
        </div>

        <div class="content">
            <div class="section">
                <h3>Servers</h3>
                <button class="btn btn-secondary" onclick="loadGuilds()" style="width: 100%; margin-bottom: 10px;">
                    Refresh Servers
                </button>
                <div id="guilds-list">
                    <div style="color: #666; padding: 10px; font-size: 12px;">
                        Loading servers...
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>Channels</h3>
                <div id="channels-list">
                    <div style="color: #666; padding: 10px; font-size: 12px;">
                        Select a server first
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="main">
        <div class="main-header">
            <h2 id="current-title">Senior Discord Logger</h2>
            <div>
                <button class="btn btn-secondary" onclick="scanCurrentGuild()" id="scan-btn" disabled>
                    Scan Guild
                </button>
                <button class="btn" onclick="showMetrics()">
                    Metrics
                </button>
            </div>
        </div>

        <div class="main-content">
            <div id="main-content">
                <div class="welcome">
                    <h2>Welcome to Senior Discord Logger</h2>
                    <p>Professional-grade Discord monitoring system</p>
                    <p style="margin-top: 20px; font-size: 14px;">
                        System is ready. Servers should load automatically.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let socket;
        let currentGuild = null;
        let currentChannel = null;
        let metrics = {};

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            loadGuilds();
        });

        function initializeSocket() {
            socket = io();
            socket.on('connect', function() {
                console.log('Connected to server');
                showStatus('Connected', 'success');
            });
            socket.on('disconnect', function() {
                console.log('Disconnected from server');
                showStatus('Disconnected', 'error');
            });
            socket.on('metrics_update', function(data) {
                metrics = data;
                console.log('Metrics updated:', data);
            });
        }

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status-message');
            statusEl.innerHTML = '<div class="status ' + type + '">' + message + '</div>';
            if (type === 'success') {
                setTimeout(() => statusEl.innerHTML = '', 3000);
            }
        }

        async function loadGuilds() {
            try {
                showStatus('Loading servers...', 'info');
                const response = await fetch('/api/guilds');
                const data = await response.json();

                if (data.success) {
                    displayGuilds(data.guilds);
                    showStatus('Servers loaded', 'success');
                } else {
                    showStatus('Failed to load servers: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('Error loading servers: ' + error.message, 'error');
            }
        }

        function displayGuilds(guilds) {
            const container = document.getElementById('guilds-list');

            if (guilds.length === 0) {
                container.innerHTML = '<div style="color: #666; padding: 10px; font-size: 12px;">No servers found</div>';
                return;
            }

            container.innerHTML = '';
            guilds.forEach(guild => {
                const div = document.createElement('div');
                div.className = 'item';
                div.innerHTML = '<span>' + guild.name + '</span><small>' + (guild.member_count || 0) + '</small>';
                div.onclick = () => selectGuild(guild.id, guild.name);
                container.appendChild(div);
            });
        }

        async function selectGuild(guildId, guildName) {
            currentGuild = guildId;

            // Update active guild
            document.querySelectorAll('#guilds-list .item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.item').classList.add('active');

            document.getElementById('current-title').textContent = guildName;
            document.getElementById('scan-btn').disabled = false;

            await loadChannels(guildId);
        }

        async function loadChannels(guildId) {
            try {
                showStatus('Loading channels...', 'info');
                const response = await fetch('/api/channels/' + guildId);
                const data = await response.json();

                if (data.success) {
                    displayChannels(data.channels);
                    showStatus('Channels loaded', 'success');
                } else {
                    showStatus('Failed to load channels: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('Error loading channels: ' + error.message, 'error');
            }
        }

        function displayChannels(channels) {
            const container = document.getElementById('channels-list');

            if (channels.length === 0) {
                container.innerHTML = '<div style="color: #666; padding: 10px; font-size: 12px;">No channels found</div>';
                return;
            }

            container.innerHTML = '';
            channels.forEach(channel => {
                const div = document.createElement('div');
                div.className = 'item';
                if (channel.has_messages) {
                    div.classList.add('has-messages');
                }
                if (channel.was_temporarily_accessible) {
                    div.classList.add('temporarily-accessible');
                }

                div.innerHTML = '<span># ' + channel.name + '</span><small>' + (channel.message_count || 0) + '</small>';
                div.onclick = () => selectChannel(channel.id, channel.name);
                container.appendChild(div);
            });
        }

        async function selectChannel(channelId, channelName) {
            currentChannel = channelId;

            // Update active channel
            document.querySelectorAll('#channels-list .item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.item').classList.add('active');

            document.getElementById('current-title').textContent = '# ' + channelName;

            await loadMessages(channelId);
        }

        async function loadMessages(channelId) {
            try {
                document.getElementById('main-content').innerHTML = '<div class="welcome"><h2>Loading messages...</h2></div>';

                const response = await fetch('/api/messages/' + channelId);
                const data = await response.json();

                if (data.success) {
                    displayMessages(data.messages);
                } else {
                    showStatus('Failed to load messages: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('Error loading messages: ' + error.message, 'error');
            }
        }

        function displayMessages(messages) {
            const container = document.getElementById('main-content');

            if (messages.length === 0) {
                container.innerHTML = '<div class="welcome"><h2>No messages found</h2><p>This channel appears to be empty or inaccessible</p></div>';
                return;
            }

            container.innerHTML = '';
            messages.forEach(message => {
                const div = document.createElement('div');
                div.className = 'message';
                if (message.is_deleted) {
                    div.classList.add('deleted');
                }

                const time = new Date(message.timestamp).toLocaleString();
                const content = message.is_deleted ? '[DELETED] ' + (message.content || 'No content') : (message.content || 'No text content');

                div.innerHTML =
                    '<div class="message-header">' +
                        '<span class="message-author">' + message.author_name + '</span>' +
                        '<span class="message-time">' + time + '</span>' +
                    '</div>' +
                    '<div class="message-content">' + content + '</div>';

                container.appendChild(div);
            });
        }

        async function scanCurrentGuild() {
            if (!currentGuild) return;

            const btn = document.getElementById('scan-btn');
            const originalText = btn.textContent;
            btn.textContent = 'Scanning...';
            btn.disabled = true;

            try {
                const response = await fetch('/api/scan/' + currentGuild, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showStatus(data.message, 'success');
                    await loadChannels(currentGuild);
                } else {
                    showStatus('Scan failed: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('Scan error: ' + error.message, 'error');
            } finally {
                btn.textContent = originalText;
                btn.disabled = false;
            }
        }

        function showMetrics() {
            alert('Metrics: ' + JSON.stringify(metrics, null, 2));
        }
    </script>
</body>
</html>'''

# Flask routes
@app.route('/')
def index():
    if not DISCORD_TOKEN:
        return SETUP_HTML
    return MAIN_HTML

@app.route('/api/setup', methods=['POST'])
def setup():
    global DISCORD_TOKEN
    
    data = request.get_json()
    token = data.get('token', '').strip()
    
    if not token:
        return jsonify({'success': False, 'error': 'Token required'})
    
    # Validate token format
    if len(token) < 50:
        return jsonify({'success': False, 'error': 'Token appears to be too short'})

    # Clean token (remove any prefixes if present)
    if token.startswith('Bearer '):
        token = token[7:]
    elif token.startswith('Bot '):
        token = token[4:]

    # Test token with async
    test_result = run_async(test_discord_token(token))
    if not test_result or not test_result['success']:
        return jsonify({'success': False, 'error': test_result['error'] if test_result else 'Authentication failed'})

    DISCORD_TOKEN = token

    # Load guilds immediately after successful auth
    guilds = run_async(get_guilds())
    logger.info(f"Setup complete: authenticated and loaded {len(guilds) if guilds else 0} guilds")

    return jsonify({'success': True, 'user': test_result['user']})

@app.route('/api/guilds')
def api_guilds():
    if not DISCORD_TOKEN:
        logger.warning("API guilds called without token")
        return jsonify({'success': False, 'error': 'Not authenticated'})

    try:
        logger.info("Loading guilds from API")

        # Always try to get fresh data from Discord first
        fresh_guilds = run_async(get_guilds())

        if fresh_guilds:
            logger.info(f"Got {len(fresh_guilds)} guilds from Discord API")
            return jsonify({'success': True, 'guilds': fresh_guilds})

        # Fallback to database if API fails
        logger.warning("Discord API failed, trying database")
        conn = sqlite3.connect('simple_discord.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM guilds WHERE is_accessible = 1 ORDER BY name')
        guilds = [dict(row) for row in cursor.fetchall()]
        conn.close()

        logger.info(f"Got {len(guilds)} guilds from database")
        return jsonify({'success': True, 'guilds': guilds})

    except Exception as e:
        logger.error(f"Error getting guilds: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/channels/<guild_id>')
def api_channels(guild_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})

    try:
        # Get channels (will try API first, then cache)
        channels = run_async(get_channels(guild_id))
        if not channels:
            channels = get_cached_channels(guild_id)

        return jsonify({'success': True, 'channels': channels})

    except Exception as e:
        logger.error(f"Error in api_channels: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/messages/<channel_id>')
def api_messages(channel_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})

    try:
        # Get messages (will try API first, then cache)
        messages = run_async(get_messages(channel_id))
        if not messages:
            messages = get_cached_messages(channel_id)

        logger.info(f"API: Returning {len(messages)} messages for channel {channel_id}")
        return jsonify({'success': True, 'messages': messages})
    except Exception as e:
        logger.error(f"API Error: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/scan/<guild_id>', methods=['POST'])
def api_scan_guild(guild_id):
    """Intelligent guild scanning"""
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})

    try:
        scan_type = request.json.get('type', 'smart') if request.json else 'smart'
        result = run_async(intelligent_guild_scan(guild_id, scan_type))

        return jsonify({
            'success': True,
            'accessible_channels': result['accessible_count'],
            'total_scanned': result['total_scanned'],
            'duration': result['duration'],
            'message': f'Scanned {result["total_scanned"]} channels, found {result["accessible_count"]} accessible'
        })
    except Exception as e:
        logger.error(f"Error in api_scan_guild: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/metrics')
def api_metrics():
    """Get system metrics"""
    try:
        # Get database stats
        conn = sqlite3.connect('simple_discord.db')
        cursor = conn.cursor()

        cursor.execute('SELECT COUNT(*) FROM guilds WHERE is_accessible = 1')
        guild_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM channels WHERE has_messages = 1')
        accessible_channels = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM messages WHERE is_deleted = 0')
        total_messages = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM messages WHERE is_deleted = 1')
        deleted_messages = cursor.fetchone()[0]

        conn.close()

        return jsonify({
            'success': True,
            'metrics': {
                **metrics,
                'guild_count': guild_count,
                'accessible_channels': accessible_channels,
                'total_messages': total_messages,
                'deleted_messages': deleted_messages,
                'active_scans': len(active_scans)
            }
        })
    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        return jsonify({'success': False, 'error': str(e)})

# WebSocket events
@socketio.on('connect')
def handle_connect():
    logger.info('Client connected to WebSocket')
    emit('status', {'connected': True, 'metrics': metrics})

@socketio.on('disconnect')
def handle_disconnect():
    logger.info('Client disconnected from WebSocket')

@socketio.on('request_metrics')
def handle_metrics_request():
    """Send real-time metrics"""
    try:
        # Get fresh metrics
        conn = sqlite3.connect('simple_discord.db')
        cursor = conn.cursor()

        cursor.execute('SELECT COUNT(*) FROM messages WHERE is_deleted = 0')
        total_messages = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM channels WHERE has_messages = 1')
        accessible_channels = cursor.fetchone()[0]

        conn.close()

        current_metrics = {
            **metrics,
            'total_messages': total_messages,
            'accessible_channels': accessible_channels,
            'active_scans': len(active_scans),
            'timestamp': datetime.now().isoformat()
        }

        emit('metrics_update', current_metrics)
    except Exception as e:
        logger.error(f"Error sending metrics: {e}")

def broadcast_metrics():
    """Broadcast metrics to all connected clients"""
    while True:
        try:
            time.sleep(30)  # Broadcast every 30 seconds
            socketio.emit('request_metrics')
        except Exception as e:
            logger.error(f"Error broadcasting metrics: {e}")

if __name__ == '__main__':
    print("=" * 60)
    print("SENIOR DISCORD LOGGER - Production Ready")
    print("=" * 60)
    print("Web interface: http://127.0.0.1:5001")
    print("Features:")
    print("   - Real-time message monitoring (500ms updates)")
    print("   - Intelligent channel scanning with priority")
    print("   - Persistent storage of deleted messages")
    print("   - Professional Discord-like UI")
    print("   - WebSocket real-time updates")
    print("   - Rate limiting and error handling")
    print("=" * 60)

    # Initialize database
    init_database()

    # Start metrics broadcasting in background
    metrics_thread = threading.Thread(target=broadcast_metrics, daemon=True)
    metrics_thread.start()

    # Start the application
    logger.info("Starting Senior Discord Logger")
    socketio.run(app, host='127.0.0.1', port=5001, debug=False, allow_unsafe_werkzeug=True)
