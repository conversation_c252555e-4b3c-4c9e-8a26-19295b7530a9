#!/usr/bin/env python3
"""
Simple Discord Logger - Simplified version that works
Usage: python simple_discord_logger.py
"""

import os
import sys
import requests
import json
import sqlite3
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO

# Global variables
app = Flask(__name__)
app.config['SECRET_KEY'] = 'discord-logger-secret'
socketio = SocketIO(app, cors_allowed_origins="*")

DISCORD_TOKEN = None
BASE_URL = "https://discord.com/api/v9"

def init_database():
    """Initialize SQLite database"""
    conn = sqlite3.connect('simple_discord.db')
    cursor = conn.cursor()
    
    # Create tables
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS guilds (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            icon TEXT
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS channels (
            id TEXT PRIMARY KEY,
            guild_id TEXT,
            name TEXT NOT NULL,
            type INTEGER
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id TEXT PRIMARY KEY,
            channel_id TEXT,
            author_name TEXT,
            content TEXT,
            timestamp TEXT,
            is_deleted INTEGER DEFAULT 0,
            last_seen TEXT
        )
    ''')
    
    conn.commit()
    conn.close()

def test_discord_token(token):
    """Test if Discord token works"""
    headers = {
        "Authorization": token,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/users/@me", headers=headers)
        if response.status_code == 200:
            return {"success": True, "user": response.json()}
        else:
            return {"success": False, "error": f"HTTP {response.status_code}"}
    except Exception as e:
        return {"success": False, "error": str(e)}

def get_guilds(token):
    """Get user's guilds"""
    headers = {"Authorization": token}
    
    try:
        response = requests.get(f"{BASE_URL}/users/@me/guilds", headers=headers)
        if response.status_code == 200:
            guilds = response.json()
            
            # Save to database
            conn = sqlite3.connect('simple_discord.db')
            cursor = conn.cursor()
            
            for guild in guilds:
                cursor.execute('''
                    INSERT OR REPLACE INTO guilds (id, name, icon)
                    VALUES (?, ?, ?)
                ''', (guild['id'], guild['name'], guild.get('icon')))
            
            conn.commit()
            conn.close()
            
            return guilds
        else:
            return []
    except Exception as e:
        print(f"Error getting guilds: {e}")
        return []

def get_channels(token, guild_id):
    """Get channels for a guild"""
    headers = {"Authorization": token}
    
    try:
        response = requests.get(f"{BASE_URL}/guilds/{guild_id}/channels", headers=headers)
        if response.status_code == 200:
            channels = response.json()
            
            # Save to database
            conn = sqlite3.connect('simple_discord.db')
            cursor = conn.cursor()
            
            for channel in channels:
                cursor.execute('''
                    INSERT OR REPLACE INTO channels (id, guild_id, name, type)
                    VALUES (?, ?, ?, ?)
                ''', (channel['id'], guild_id, channel['name'], channel['type']))
            
            conn.commit()
            conn.close()
            
            return channels
        else:
            return []
    except Exception as e:
        print(f"Error getting channels: {e}")
        return []

def get_messages(token, channel_id, limit=50):
    """Get messages from a channel and track deleted ones"""
    headers = {"Authorization": token}

    try:
        response = requests.get(f"{BASE_URL}/channels/{channel_id}/messages?limit={limit}", headers=headers)
        if response.status_code == 200:
            current_messages = response.json()
            current_time = datetime.now().isoformat()

            conn = sqlite3.connect('simple_discord.db')
            cursor = conn.cursor()

            # Get existing message IDs for this channel
            cursor.execute('SELECT id FROM messages WHERE channel_id = ?', (channel_id,))
            existing_ids = set(row[0] for row in cursor.fetchall())

            # Get current message IDs from API
            current_ids = set(message['id'] for message in current_messages)

            # Mark messages as deleted if they're not in current response
            deleted_ids = existing_ids - current_ids
            for deleted_id in deleted_ids:
                cursor.execute('''
                    UPDATE messages
                    SET is_deleted = 1, last_seen = ?
                    WHERE id = ? AND is_deleted = 0
                ''', (current_time, deleted_id))

            # Add or update current messages
            for message in current_messages:
                cursor.execute('''
                    INSERT OR REPLACE INTO messages
                    (id, channel_id, author_name, content, timestamp, is_deleted, last_seen)
                    VALUES (?, ?, ?, ?, ?, 0, ?)
                ''', (
                    message['id'],
                    channel_id,
                    message['author']['username'],
                    message['content'],
                    message['timestamp'],
                    current_time
                ))

            conn.commit()

            # Return all messages (including deleted ones) for this channel
            cursor.execute('''
                SELECT id, channel_id, author_name, content, timestamp, is_deleted
                FROM messages
                WHERE channel_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (channel_id, limit * 2))  # Get more to include deleted messages

            all_messages = []
            for row in cursor.fetchall():
                all_messages.append({
                    'id': row[0],
                    'channel_id': row[1],
                    'author_name': row[2],
                    'content': row[3],
                    'timestamp': row[4],
                    'is_deleted': bool(row[5])
                })

            conn.close()
            return all_messages
        else:
            # If API fails, return cached messages from database
            conn = sqlite3.connect('simple_discord.db')
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, channel_id, author_name, content, timestamp, is_deleted
                FROM messages
                WHERE channel_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (channel_id, limit))

            cached_messages = []
            for row in cursor.fetchall():
                cached_messages.append({
                    'id': row[0],
                    'channel_id': row[1],
                    'author_name': row[2],
                    'content': row[3],
                    'timestamp': row[4],
                    'is_deleted': bool(row[5])
                })

            conn.close()
            return cached_messages

    except Exception as e:
        print(f"Error getting messages: {e}")
        return []

# Routes
@app.route('/')
def index():
    if not DISCORD_TOKEN:
        return render_template('simple_setup.html')
    return render_template('simple_index.html')

@app.route('/api/setup', methods=['POST'])
def setup():
    global DISCORD_TOKEN
    
    data = request.get_json()
    token = data.get('token', '').strip()
    
    if not token:
        return jsonify({'success': False, 'error': 'Token required'})
    
    # Test token
    test_result = test_discord_token(token)
    if not test_result['success']:
        return jsonify({'success': False, 'error': test_result['error']})
    
    DISCORD_TOKEN = token
    return jsonify({'success': True, 'user': test_result['user']})

@app.route('/api/guilds')
def api_guilds():
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    guilds = get_guilds(DISCORD_TOKEN)
    return jsonify({'success': True, 'guilds': guilds})

@app.route('/api/channels/<guild_id>')
def api_channels(guild_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    channels = get_channels(DISCORD_TOKEN, guild_id)
    return jsonify({'success': True, 'channels': channels})

@app.route('/api/messages/<channel_id>')
def api_messages(channel_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    messages = get_messages(DISCORD_TOKEN, channel_id)
    return jsonify({'success': True, 'messages': messages})

if __name__ == '__main__':
    print("=" * 50)
    print("Simple Discord Logger")
    print("=" * 50)
    print("Web interface: http://127.0.0.1:5001")
    print("=" * 50)
    
    init_database()
    socketio.run(app, host='127.0.0.1', port=5001, debug=True, allow_unsafe_werkzeug=True)
