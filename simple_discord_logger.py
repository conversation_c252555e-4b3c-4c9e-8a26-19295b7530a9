#!/usr/bin/env python3
"""
Simple Discord Logger - Simplified version that works
Usage: python simple_discord_logger.py
"""

import os
import sys
import requests
import json
import sqlite3
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO

# Global variables
app = Flask(__name__)
app.config['SECRET_KEY'] = 'discord-logger-secret'
socketio = SocketIO(app, cors_allowed_origins="*")

DISCORD_TOKEN = None
BASE_URL = "https://discord.com/api/v9"

def init_database():
    """Initialize SQLite database"""
    conn = sqlite3.connect('simple_discord.db')
    cursor = conn.cursor()
    
    # Create tables
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS guilds (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            icon TEXT
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS channels (
            id TEXT PRIMARY KEY,
            guild_id TEXT,
            name TEXT NOT NULL,
            type INTEGER,
            has_messages INTEGER DEFAULT 0,
            last_accessible TEXT,
            is_temporarily_accessible INTEGER DEFAULT 0
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id TEXT PRIMARY KEY,
            channel_id TEXT,
            author_name TEXT,
            content TEXT,
            timestamp TEXT
        )
    ''')
    
    conn.commit()
    conn.close()

def test_discord_token(token):
    """Test if Discord token works"""
    headers = {
        "Authorization": token,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/users/@me", headers=headers)
        if response.status_code == 200:
            return {"success": True, "user": response.json()}
        else:
            return {"success": False, "error": f"HTTP {response.status_code}"}
    except Exception as e:
        return {"success": False, "error": str(e)}

def get_guilds(token):
    """Get user's guilds"""
    headers = {"Authorization": token}
    
    try:
        response = requests.get(f"{BASE_URL}/users/@me/guilds", headers=headers)
        if response.status_code == 200:
            guilds = response.json()
            
            # Save to database
            conn = sqlite3.connect('simple_discord.db')
            cursor = conn.cursor()
            
            for guild in guilds:
                cursor.execute('''
                    INSERT OR REPLACE INTO guilds (id, name, icon)
                    VALUES (?, ?, ?)
                ''', (guild['id'], guild['name'], guild.get('icon')))
            
            conn.commit()
            conn.close()
            
            return guilds
        else:
            return []
    except Exception as e:
        print(f"Error getting guilds: {e}")
        return []

def get_channels(token, guild_id):
    """Get channels for a guild"""
    headers = {"Authorization": token}
    current_time = datetime.now().isoformat()

    try:
        response = requests.get(f"{BASE_URL}/guilds/{guild_id}/channels", headers=headers)
        if response.status_code == 200:
            channels = response.json()

            # Save to database
            conn = sqlite3.connect('simple_discord.db')
            cursor = conn.cursor()

            for channel in channels:
                cursor.execute('''
                    INSERT OR REPLACE INTO channels
                    (id, guild_id, name, type, last_accessible, is_temporarily_accessible)
                    VALUES (?, ?, ?, ?, ?, 1)
                ''', (channel['id'], guild_id, channel['name'], channel['type'], current_time))

            conn.commit()
            conn.close()

            return channels
        else:
            # Return cached channels if API fails
            conn = sqlite3.connect('simple_discord.db')
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, guild_id, name, type, has_messages, is_temporarily_accessible
                FROM channels
                WHERE guild_id = ?
                ORDER BY name
            ''', (guild_id,))

            cached_channels = []
            for row in cursor.fetchall():
                cached_channels.append({
                    'id': row[0],
                    'guild_id': row[1],
                    'name': row[2],
                    'type': row[3],
                    'has_messages': bool(row[4]),
                    'is_temporarily_accessible': bool(row[5])
                })

            conn.close()
            return cached_channels

    except Exception as e:
        print(f"Error getting channels: {e}")
        return []

def get_messages(token, channel_id, limit=100):
    """Get messages from a channel - simplified version"""
    headers = {"Authorization": token}

    print(f"Getting messages for channel {channel_id}")

    try:
        response = requests.get(f"{BASE_URL}/channels/{channel_id}/messages?limit={limit}", headers=headers)
        print(f"Discord API response: {response.status_code}")

        if response.status_code == 200:
            current_messages = response.json()
            print(f"Got {len(current_messages)} messages from Discord API")

            conn = sqlite3.connect('simple_discord.db')
            cursor = conn.cursor()

            # Add or update current messages
            new_messages = 0
            for message in current_messages:
                cursor.execute('''
                    INSERT OR REPLACE INTO messages
                    (id, channel_id, author_name, content, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    message['id'],
                    channel_id,
                    message['author']['username'],
                    message['content'],
                    message['timestamp']
                ))
                new_messages += 1

            print(f"Saved {new_messages} messages to DB")

            # Mark channel as having messages if we got any
            if current_messages:
                cursor.execute('''
                    UPDATE channels
                    SET has_messages = 1, last_accessible = ?
                    WHERE id = ?
                ''', (datetime.now().isoformat(), channel_id))

            conn.commit()

            # Return all messages for this channel (including old cached ones)
            cursor.execute('''
                SELECT id, channel_id, author_name, content, timestamp
                FROM messages
                WHERE channel_id = ?
                ORDER BY timestamp ASC
            ''', (channel_id,))

            all_messages = []
            for row in cursor.fetchall():
                all_messages.append({
                    'id': row[0],
                    'channel_id': row[1],
                    'author_name': row[2],
                    'content': row[3],
                    'timestamp': row[4]
                })

            conn.close()
            print(f"Returning {len(all_messages)} total messages (including cached)")
            return all_messages

        elif response.status_code == 403:
            print(f"No access to channel {channel_id}, returning cached messages")
            return get_cached_messages(channel_id)
        elif response.status_code == 404:
            print(f"Channel {channel_id} not found, returning cached messages")
            return get_cached_messages(channel_id)
        else:
            print(f"API error {response.status_code}: {response.text}, returning cached messages")
            return get_cached_messages(channel_id)

    except Exception as e:
        print(f"Error getting messages: {e}")
        import traceback
        traceback.print_exc()
        return []

def get_cached_messages(channel_id):
    """Get cached messages from database"""
    try:
        conn = sqlite3.connect('simple_discord.db')
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, channel_id, author_name, content, timestamp
            FROM messages
            WHERE channel_id = ?
            ORDER BY timestamp ASC
        ''', (channel_id,))

        cached_messages = []
        for row in cursor.fetchall():
            cached_messages.append({
                'id': row[0],
                'channel_id': row[1],
                'author_name': row[2],
                'content': row[3],
                'timestamp': row[4]
            })

        conn.close()
        print(f"Returning {len(cached_messages)} cached messages for channel {channel_id}")
        return cached_messages
    except Exception as e:
        print(f"Error getting cached messages: {e}")
        return []

def aggressive_channel_scan(token, guild_id):
    """Aggressively scan all channels in a guild for temporary access"""
    print(f"Starting aggressive scan for guild {guild_id}")

    try:
        # First get all channels
        channels = get_channels(token, guild_id)
        print(f"Found {len(channels)} channels to scan")

        scanned_count = 0
        accessible_count = 0

        for channel in channels:
            if channel['type'] == 0:  # Text channels only
                channel_id = channel['id']
                print(f"Scanning channel {channel['name']} ({channel_id})")

                # Try to get messages quickly
                headers = {"Authorization": token}
                try:
                    response = requests.get(f"{BASE_URL}/channels/{channel_id}/messages?limit=10",
                                          headers=headers, timeout=3)

                    if response.status_code == 200:
                        messages = response.json()
                        if messages:
                            print(f"✅ Found {len(messages)} messages in {channel['name']}")

                            # Save messages immediately
                            conn = sqlite3.connect('simple_discord.db')
                            cursor = conn.cursor()

                            for message in messages:
                                cursor.execute('''
                                    INSERT OR REPLACE INTO messages
                                    (id, channel_id, author_name, content, timestamp)
                                    VALUES (?, ?, ?, ?, ?)
                                ''', (
                                    message['id'],
                                    channel_id,
                                    message['author']['username'],
                                    message['content'],
                                    message['timestamp']
                                ))

                            # Mark channel as having messages
                            cursor.execute('''
                                UPDATE channels
                                SET has_messages = 1, last_accessible = ?, is_temporarily_accessible = 1
                                WHERE id = ?
                            ''', (datetime.now().isoformat(), channel_id))

                            conn.commit()
                            conn.close()
                            accessible_count += 1
                        else:
                            print(f"⚪ Channel {channel['name']} is empty")
                    else:
                        print(f"❌ No access to {channel['name']} ({response.status_code})")

                except Exception as e:
                    print(f"⚠️ Error scanning {channel['name']}: {e}")

                scanned_count += 1

                # Small delay to avoid rate limiting
                time.sleep(0.1)

        print(f"Scan complete: {scanned_count} channels scanned, {accessible_count} accessible")
        return accessible_count

    except Exception as e:
        print(f"Error in aggressive scan: {e}")
        return 0

# Routes
@app.route('/')
def index():
    if not DISCORD_TOKEN:
        return render_template('simple_setup.html')

    # Auto-load guilds when accessing main page
    try:
        guilds = get_guilds(DISCORD_TOKEN)
        print(f"Auto-loaded {len(guilds)} guilds")
    except Exception as e:
        print(f"Error auto-loading guilds: {e}")

    return render_template('simple_index.html')

@app.route('/api/setup', methods=['POST'])
def setup():
    global DISCORD_TOKEN
    
    data = request.get_json()
    token = data.get('token', '').strip()
    
    if not token:
        return jsonify({'success': False, 'error': 'Token required'})
    
    # Test token
    test_result = test_discord_token(token)
    if not test_result['success']:
        return jsonify({'success': False, 'error': test_result['error']})
    
    DISCORD_TOKEN = token
    return jsonify({'success': True, 'user': test_result['user']})

@app.route('/api/guilds')
def api_guilds():
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    guilds = get_guilds(DISCORD_TOKEN)
    return jsonify({'success': True, 'guilds': guilds})

@app.route('/api/channels/<guild_id>')
def api_channels(guild_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})

    try:
        channels = get_channels(DISCORD_TOKEN, guild_id)

        # Add information about which channels have cached messages
        conn = sqlite3.connect('simple_discord.db')
        cursor = conn.cursor()

        enhanced_channels = []
        for channel in channels:
            # Check if channel has messages in database
            cursor.execute('SELECT COUNT(*) FROM messages WHERE channel_id = ?', (channel['id'],))
            message_count = cursor.fetchone()[0]

            # Check if channel was temporarily accessible
            cursor.execute('''
                SELECT has_messages, is_temporarily_accessible, last_accessible
                FROM channels WHERE id = ?
            ''', (channel['id'],))
            channel_info = cursor.fetchone()

            enhanced_channel = channel.copy()
            enhanced_channel['message_count'] = message_count
            enhanced_channel['has_cached_messages'] = message_count > 0

            if channel_info:
                enhanced_channel['has_messages'] = bool(channel_info[0])
                enhanced_channel['is_temporarily_accessible'] = bool(channel_info[1])
                enhanced_channel['last_accessible'] = channel_info[2]
            else:
                enhanced_channel['has_messages'] = False
                enhanced_channel['is_temporarily_accessible'] = False
                enhanced_channel['last_accessible'] = None

            enhanced_channels.append(enhanced_channel)

        conn.close()
        return jsonify({'success': True, 'channels': enhanced_channels})

    except Exception as e:
        print(f"Error in api_channels: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/scan-guild/<guild_id>', methods=['POST'])
def api_scan_guild(guild_id):
    """Aggressively scan all channels in a guild"""
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})

    try:
        accessible_count = aggressive_channel_scan(DISCORD_TOKEN, guild_id)
        return jsonify({
            'success': True,
            'accessible_channels': accessible_count,
            'message': f'Scanned guild, found {accessible_count} accessible channels'
        })
    except Exception as e:
        print(f"Error in api_scan_guild: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/messages/<channel_id>')
def api_messages(channel_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})

    try:
        messages = get_messages(DISCORD_TOKEN, channel_id)
        print(f"API: Returning {len(messages)} messages for channel {channel_id}")
        return jsonify({'success': True, 'messages': messages})
    except Exception as e:
        print(f"API Error: {e}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("=" * 50)
    print("Simple Discord Logger")
    print("=" * 50)
    print("Web interface: http://127.0.0.1:5001")
    print("=" * 50)
    
    init_database()
    socketio.run(app, host='127.0.0.1', port=5001, debug=True, allow_unsafe_werkzeug=True)
