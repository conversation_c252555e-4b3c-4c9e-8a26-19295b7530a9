"""
Enterprise Logging System
Professional-grade logging with structured output, metrics, and monitoring
"""

import logging
import sys
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from contextlib import contextmanager
from enterprise_config import config, LogLevel

@dataclass
class LogMetrics:
    """Log metrics for monitoring"""
    total_logs: int = 0
    error_count: int = 0
    warning_count: int = 0
    performance_logs: int = 0
    last_error: Optional[str] = None
    last_error_time: Optional[datetime] = None

class StructuredFormatter(logging.Formatter):
    """Structured JSON formatter for logs"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add extra fields if present
        if hasattr(record, 'extra_data'):
            log_entry.update(record.extra_data)
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, default=str)

class EnterpriseLogger:
    """Enterprise-grade logger with metrics and monitoring"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(name)
        self.metrics = LogMetrics()
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger with appropriate handlers and formatters"""
        self.logger.setLevel(getattr(logging, config.log_level.value))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Console handler with structured format
        console_handler = logging.StreamHandler(sys.stdout)
        if config.monitoring.log_performance:
            console_handler.setFormatter(StructuredFormatter())
        else:
            console_handler.setFormatter(
                logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            )
        self.logger.addHandler(console_handler)
        
        # File handler for persistent logs
        file_handler = logging.FileHandler('enterprise_discord.log')
        file_handler.setFormatter(StructuredFormatter())
        self.logger.addHandler(file_handler)
    
    def _log_with_metrics(self, level: str, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """Log message with metrics tracking"""
        self.metrics.total_logs += 1
        
        if level == 'ERROR':
            self.metrics.error_count += 1
            self.metrics.last_error = message
            self.metrics.last_error_time = datetime.utcnow()
        elif level == 'WARNING':
            self.metrics.warning_count += 1
        
        # Create log record with extra data
        if extra_data:
            record = self.logger.makeRecord(
                self.logger.name, getattr(logging, level), 
                '', 0, message, (), None
            )
            record.extra_data = extra_data
            self.logger.handle(record)
        else:
            getattr(self.logger, level.lower())(message)
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self._log_with_metrics('DEBUG', message, kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self._log_with_metrics('INFO', message, kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self._log_with_metrics('WARNING', message, kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self._log_with_metrics('ERROR', message, kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self._log_with_metrics('CRITICAL', message, kwargs)
    
    @contextmanager
    def performance_timer(self, operation: str, **context):
        """Context manager for performance timing"""
        start_time = time.time()
        self.metrics.performance_logs += 1
        
        try:
            self.debug(f"Starting operation: {operation}", operation=operation, **context)
            yield
            
        except Exception as e:
            duration = time.time() - start_time
            self.error(
                f"Operation failed: {operation}",
                operation=operation,
                duration=duration,
                error=str(e),
                **context
            )
            raise
            
        else:
            duration = time.time() - start_time
            self.info(
                f"Operation completed: {operation}",
                operation=operation,
                duration=duration,
                **context
            )
    
    def log_api_call(self, method: str, url: str, status_code: int, duration: float, **kwargs):
        """Log API call with structured data"""
        self.info(
            f"API call: {method} {url}",
            api_method=method,
            api_url=url,
            status_code=status_code,
            duration=duration,
            **kwargs
        )
    
    def log_database_operation(self, operation: str, table: str, duration: float, rows_affected: int = None, **kwargs):
        """Log database operation with structured data"""
        self.info(
            f"Database operation: {operation} on {table}",
            db_operation=operation,
            db_table=table,
            duration=duration,
            rows_affected=rows_affected,
            **kwargs
        )
    
    def log_scanner_event(self, event: str, guild_id: str = None, channel_id: str = None, **kwargs):
        """Log scanner event with structured data"""
        self.info(
            f"Scanner event: {event}",
            scanner_event=event,
            guild_id=guild_id,
            channel_id=channel_id,
            **kwargs
        )
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics"""
        return asdict(self.metrics)
    
    def reset_metrics(self):
        """Reset metrics counters"""
        self.metrics = LogMetrics()

class LoggerFactory:
    """Factory for creating enterprise loggers"""
    
    _loggers: Dict[str, EnterpriseLogger] = {}
    
    @classmethod
    def get_logger(cls, name: str) -> EnterpriseLogger:
        """Get or create logger instance"""
        if name not in cls._loggers:
            cls._loggers[name] = EnterpriseLogger(name)
        return cls._loggers[name]
    
    @classmethod
    def get_all_metrics(cls) -> Dict[str, Dict[str, Any]]:
        """Get metrics from all loggers"""
        return {name: logger.get_metrics() for name, logger in cls._loggers.items()}

# Convenience function for getting loggers
def get_logger(name: str) -> EnterpriseLogger:
    """Get enterprise logger instance"""
    return LoggerFactory.get_logger(name)
