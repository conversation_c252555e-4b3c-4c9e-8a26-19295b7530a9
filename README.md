# Discord Logger

Логгер сообщений Discord с веб-интерфейсом, похожим на оригинальный Discord. Позволяет отслеживать все сообщения, включая удаленные и измененные, а также показывает скрытые каналы при получении доступа.

## 🚀 Возможности

- **Веб-интерфейс** похожий на Discord (серверы слева, каналы, сообщения)
- **Мониторинг в реальном времени** новых, измененных и удаленных сообщений
- **Отслеживание скрытых каналов** - показывает каналы при получении доступа
- **Сохранение истории** даже при потере доступа к каналам
- **Личные сообщения** - логирование DM каналов
- **Пометки удаленных сообщений** с индикацией
- **База данных SQLite** для хранения всех данных
- **Авторизация по токену** Discord пользователя

## 📋 Требования

- Python 3.8+
- Discord токен пользователя

## 🛠 Установка

1. **Клонируйте или скачайте проект**
```bash
git clone <repository-url>
cd discord-logger
```

2. **Установите зависимости**
```bash
pip install -r requirements.txt
```

3. **Запустите логгер**
```bash
python main.py
```

4. **Откройте веб-интерфейс**
   - Перейдите на http://localhost:5000
   - Если токен не настроен, вас перенаправит на страницу настройки

## 🔑 Получение Discord токена

1. Откройте Discord в браузере (не в приложении)
2. Нажмите F12 для открытия Developer Tools
3. Перейдите на вкладку **Network**
4. Обновите страницу (F5)
5. Найдите любой запрос к API Discord (например, к `/api/v9/users/@me`)
6. В заголовках запроса найдите `Authorization`
7. Скопируйте значение после `Bearer ` (это ваш токен)

## 🚀 Запуск

### Способ 1: Через аргумент командной строки
```bash
python main.py YOUR_DISCORD_TOKEN
```

### Способ 2: Через переменную окружения
```bash
# Windows
set DISCORD_TOKEN=YOUR_DISCORD_TOKEN
python main.py

# Linux/Mac
export DISCORD_TOKEN=YOUR_DISCORD_TOKEN
python main.py
```

### Способ 3: Через веб-интерфейс
```bash
python main.py
# Откройте http://localhost:5000/setup и введите токен
```

## 🖥 Использование

1. **Запустите логгер** одним из способов выше
2. **Откройте браузер** и перейдите на http://localhost:5000
3. **Выберите сервер** в левой панели
4. **Выберите канал** для просмотра сообщений
5. **Просматривайте сообщения** в реальном времени

### Интерфейс

- **Левая панель**: Кнопка "DM" для личных сообщений и список серверов
- **Средняя панель**: Список каналов выбранного сервера
- **Правая панель**: Сообщения выбранного канала
- **Статус**: Индикатор подключения в правом верхнем углу

### Особенности

- **Удаленные сообщения** отображаются с пометкой "Сообщение удалено"
- **Измененные сообщения** помечаются как "(изменено)"
- **Недоступные каналы** отображаются серым цветом и курсивом
- **Скрытые каналы** автоматически добавляются при получении доступа

## 📁 Структура файлов

```
discord-logger/
├── main.py              # Главный файл запуска
├── config.py            # Конфигурация
├── database.py          # Работа с базой данных
├── discord_client.py    # Discord клиент
├── web_server.py        # Flask веб-сервер
├── requirements.txt     # Зависимости
├── templates/           # HTML шаблоны
│   ├── base.html
│   ├── index.html
│   └── setup.html
├── static/              # CSS/JS файлы
│   ├── style.css
│   └── app.js
├── discord_logger.db    # База данных (создается автоматически)
└── discord_logger.log   # Файл логов
```

## ⚙️ Конфигурация

Настройки можно изменить в файле `config.py`:

```python
class Config:
    HOST = '127.0.0.1'              # Хост веб-сервера
    PORT = 5000                     # Порт веб-сервера
    MESSAGE_CHECK_INTERVAL = 2      # Интервал проверки сообщений (сек)
    CHANNEL_CHECK_INTERVAL = 30     # Интервал проверки каналов (сек)
    MESSAGES_PER_PAGE = 50          # Сообщений на страницу
    MAX_MESSAGE_HISTORY = 1000      # Максимум сообщений на канал
```

## 🔒 Безопасность

- **Токен хранится локально** и не передается третьим лицам
- **Только чтение** - логгер не может отправлять сообщения
- **Локальный веб-сервер** - доступ только с вашего компьютера
- **База данных SQLite** - все данные хранятся локально

## ⚠️ Важные замечания

1. **Соблюдайте ToS Discord** - использование токенов пользователей может нарушать условия использования
2. **Не делитесь токеном** - это ваши учетные данные Discord
3. **Используйте ответственно** - не злоупотребляйте API Discord
4. **Резервные копии** - регулярно создавайте копии базы данных

## 🐛 Устранение неполадок

### Ошибка "Discord токен не найден"
- Убедитесь, что токен правильно скопирован
- Проверьте, что токен не содержит лишних символов

### Ошибка "403 Forbidden"
- Токен может быть недействительным
- Попробуйте получить новый токен

### Веб-интерфейс не загружается
- Проверьте, что порт 5000 не занят
- Попробуйте изменить порт в config.py

### Сообщения не загружаются
- Проверьте подключение к интернету
- Убедитесь, что у вас есть доступ к каналу

## 📝 Логи

Все события записываются в файл `discord_logger.log`:
- Подключения/отключения
- Ошибки API
- Новые сообщения
- Изменения каналов

## 🤝 Поддержка

При возникновении проблем:
1. Проверьте логи в `discord_logger.log`
2. Убедитесь, что все зависимости установлены
3. Проверьте правильность токена Discord

## 📄 Лицензия

Этот проект предназначен только для образовательных целей. Используйте ответственно и в соответствии с условиями использования Discord.
