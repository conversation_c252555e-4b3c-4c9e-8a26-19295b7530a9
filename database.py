import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Optional
import threading

class DatabaseManager:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.lock = threading.Lock()
        self.init_database()
    
    def init_database(self):
        """Initialize database tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Guilds table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS guilds (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    icon TEXT,
                    owner_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Channels table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS channels (
                    id INTEGER PRIMARY KEY,
                    guild_id INTEGER,
                    name TEXT NOT NULL,
                    type INTEGER NOT NULL,
                    topic TEXT,
                    position INTEGER,
                    is_accessible BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (guild_id) REFERENCES guilds (id)
                )
            ''')
            
            # Messages table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY,
                    channel_id INTEGER NOT NULL,
                    author_id INTEGER NOT NULL,
                    author_name TEXT NOT NULL,
                    content TEXT,
                    embeds TEXT,
                    attachments TEXT,
                    created_at TIMESTAMP NOT NULL,
                    edited_at TIMESTAMP,
                    deleted_at TIMESTAMP,
                    is_deleted BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (channel_id) REFERENCES channels (id)
                )
            ''')
            
            # DM channels table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dm_channels (
                    id INTEGER PRIMARY KEY,
                    recipient_id INTEGER,
                    recipient_name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def save_guild(self, guild_data: Dict):
        """Save or update guild information"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO guilds
                    (id, name, icon, owner_id, last_seen)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    str(guild_data['id']),  # Convert to string to avoid JS precision issues
                    guild_data['name'],
                    guild_data.get('icon'),
                    str(guild_data.get('owner_id')) if guild_data.get('owner_id') else None,
                    datetime.now()
                ))
                conn.commit()
    
    def save_channel(self, channel_data: Dict):
        """Save or update channel information"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO channels
                    (id, guild_id, name, type, topic, position, is_accessible, last_seen)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    str(channel_data['id']),  # Convert to string
                    str(channel_data.get('guild_id')) if channel_data.get('guild_id') else None,
                    channel_data['name'],
                    channel_data['type'],
                    channel_data.get('topic'),
                    channel_data.get('position', 0),
                    channel_data.get('is_accessible', True),
                    datetime.now()
                ))
                conn.commit()
    
    def save_message(self, message_data: Dict):
        """Save or update message"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO messages 
                    (id, channel_id, author_id, author_name, content, embeds, attachments, 
                     created_at, edited_at, deleted_at, is_deleted)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    message_data['id'],
                    message_data['channel_id'],
                    message_data['author_id'],
                    message_data['author_name'],
                    message_data.get('content'),
                    json.dumps(message_data.get('embeds', [])),
                    json.dumps(message_data.get('attachments', [])),
                    message_data['created_at'],
                    message_data.get('edited_at'),
                    message_data.get('deleted_at'),
                    message_data.get('is_deleted', False)
                ))
                conn.commit()
    
    def mark_message_deleted(self, message_id: int):
        """Mark message as deleted"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE messages 
                    SET is_deleted = TRUE, deleted_at = ?
                    WHERE id = ?
                ''', (datetime.now(), message_id))
                conn.commit()
    
    def get_guilds(self) -> List[Dict]:
        """Get all guilds"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM guilds ORDER BY name')
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    def get_channels(self, guild_id: Optional[int] = None) -> List[Dict]:
        """Get channels for a guild or all channels"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            if guild_id:
                cursor.execute('''
                    SELECT * FROM channels 
                    WHERE guild_id = ? 
                    ORDER BY position, name
                ''', (guild_id,))
            else:
                cursor.execute('SELECT * FROM channels ORDER BY guild_id, position, name')
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    def get_messages(self, channel_id: int, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Get messages for a channel"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM messages 
                WHERE channel_id = ? 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ''', (channel_id, limit, offset))
            columns = [desc[0] for desc in cursor.description]
            messages = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            # Parse JSON fields
            for msg in messages:
                msg['embeds'] = json.loads(msg['embeds']) if msg['embeds'] else []
                msg['attachments'] = json.loads(msg['attachments']) if msg['attachments'] else []
            
            return messages
    
    def save_dm_channel(self, dm_data: Dict):
        """Save DM channel information"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO dm_channels 
                    (id, recipient_id, recipient_name, last_seen)
                    VALUES (?, ?, ?, ?)
                ''', (
                    dm_data['id'],
                    dm_data.get('recipient_id'),
                    dm_data.get('recipient_name'),
                    datetime.now()
                ))
                conn.commit()
    
    def get_dm_channels(self) -> List[Dict]:
        """Get all DM channels"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM dm_channels ORDER BY last_seen DESC')
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
