2025-06-30 22:09:24,582 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:09:24,582 - __main__ - INFO - Initializing database...
2025-06-30 22:09:24,583 - __main__ - INFO - Database initialized
2025-06-30 22:09:24,584 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:09:24,584 - __main__ - INFO - Initializing web server...
2025-06-30 22:09:24,624 - __main__ - INFO - Web server initialized
2025-06-30 22:09:24,625 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:09:24,625 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:09:24,625 - __main__ - ERROR - Critical error: The Werkzeug web server is not designed to run in production. Pass allow_unsafe_werkzeug=True to the run() method to disable this error.
2025-06-30 22:09:24,625 - __main__ - INFO - Stopping Discord Logger...
2025-06-30 22:09:24,625 - __main__ - INFO - Discord Logger stopped
2025-06-30 22:09:48,169 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:09:48,169 - __main__ - INFO - Initializing database...
2025-06-30 22:09:48,171 - __main__ - INFO - Database initialized
2025-06-30 22:09:48,171 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:09:48,171 - __main__ - INFO - Initializing web server...
2025-06-30 22:09:48,212 - __main__ - INFO - Web server initialized
2025-06-30 22:09:48,212 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:09:48,212 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:09:48,212 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:09:48,245 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:09:48,245 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:09:48,245 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:09:49,243 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:09:49,243 - __main__ - INFO - Initializing database...
2025-06-30 22:09:49,244 - __main__ - INFO - Database initialized
2025-06-30 22:09:49,244 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:09:49,244 - __main__ - INFO - Initializing web server...
2025-06-30 22:09:49,284 - __main__ - INFO - Web server initialized
2025-06-30 22:09:49,284 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:09:49,284 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:09:49,284 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:09:49,294 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:09:49,309 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:09:52,300 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:09:52,318 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:09:52,428 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 22:09:52,429 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:09:57,088 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:09:57,096 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:09:57,155 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:09:57,156 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:06,370 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:06,388 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:06,442 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:06,443 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:10,765 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:10,774 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:10,819 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:10,821 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:14,687 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:14,693 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:14,726 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:14,727 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:17,619 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:17,638 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:17,686 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:17,686 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:23,087 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:23,093 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:23,137 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:23,138 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:41,924 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:11:42,013 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:11:43,135 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:11:43,135 - __main__ - INFO - Initializing database...
2025-06-30 22:11:43,136 - __main__ - INFO - Database initialized
2025-06-30 22:11:43,136 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:11:43,137 - __main__ - INFO - Initializing web server...
2025-06-30 22:11:43,178 - __main__ - INFO - Web server initialized
2025-06-30 22:11:43,178 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:11:43,178 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:11:43,178 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:11:43,186 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:11:43,206 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:11:55,084 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:55] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:11:55,199 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:55] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:11:55,208 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:55] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:56,444 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:56] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:11:56,476 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:56] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:11:56,478 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:56] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:57,224 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:57] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:11:58,411 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET / HTTP/1.1" 200 -
2025-06-30 22:11:58,446 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:58,448 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:11:58,465 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0m51y HTTP/1.1" 200 -
2025-06-30 22:11:58,468 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:11:58,480 - web_server - INFO - Client connected
2025-06-30 22:11:58,480 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "POST /socket.io/?EIO=4&transport=polling&t=PV0m52B&sid=xHSIDXUhl3muGZ6LAAAA HTTP/1.1" 200 -
2025-06-30 22:11:58,481 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0m52D&sid=xHSIDXUhl3muGZ6LAAAA HTTP/1.1" 200 -
2025-06-30 22:11:58,489 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0m52N&sid=xHSIDXUhl3muGZ6LAAAA HTTP/1.1" 200 -
2025-06-30 22:12:04,330 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:12:04,419 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:12:05,452 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:12:05,452 - __main__ - INFO - Initializing database...
2025-06-30 22:12:05,453 - __main__ - INFO - Database initialized
2025-06-30 22:12:05,453 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:12:05,453 - __main__ - INFO - Initializing web server...
2025-06-30 22:12:05,494 - __main__ - INFO - Web server initialized
2025-06-30 22:12:05,494 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:12:05,494 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:12:05,494 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:12:05,502 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:12:05,519 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:12:05,588 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0m6nH HTTP/1.1" 200 -
2025-06-30 22:12:05,595 - web_server - INFO - Client connected
2025-06-30 22:12:05,595 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "POST /socket.io/?EIO=4&transport=polling&t=PV0m6nN&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:05,596 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0m6nO&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:05,603 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0m6nW&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:07,094 - web_server - INFO - Client disconnected
2025-06-30 22:12:07,094 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "GET /socket.io/?EIO=4&transport=websocket&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:07,106 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:12:07,119 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:12:07,220 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:12:07,220 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:12:08,571 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:08] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:12:09,342 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET / HTTP/1.1" 200 -
2025-06-30 22:12:09,383 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:12:09,384 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:12:09,433 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0m7j6 HTTP/1.1" 200 -
2025-06-30 22:12:09,443 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:12:09,455 - web_server - INFO - Client connected
2025-06-30 22:12:09,456 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "POST /socket.io/?EIO=4&transport=polling&t=PV0m7jb&sid=y64OoznonzDfoMKGAAAC HTTP/1.1" 200 -
2025-06-30 22:12:09,461 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0m7jc&sid=y64OoznonzDfoMKGAAAC HTTP/1.1" 200 -
2025-06-30 22:12:09,482 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0m7k4&sid=y64OoznonzDfoMKGAAAC HTTP/1.1" 200 -
2025-06-30 22:12:54,075 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:12:54,076 - __main__ - INFO - Initializing database...
2025-06-30 22:12:54,077 - __main__ - INFO - Database initialized
2025-06-30 22:12:54,077 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:12:54,077 - __main__ - INFO - Initializing web server...
2025-06-30 22:12:54,118 - __main__ - INFO - Web server initialized
2025-06-30 22:12:54,118 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:12:54,118 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:12:54,118 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:12:54,136 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:12:54,136 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:12:54,136 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:12:55,228 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:12:55,228 - __main__ - INFO - Initializing database...
2025-06-30 22:12:55,230 - __main__ - INFO - Database initialized
2025-06-30 22:12:55,230 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:12:55,230 - __main__ - INFO - Initializing web server...
2025-06-30 22:12:55,279 - __main__ - INFO - Web server initialized
2025-06-30 22:12:55,279 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:12:55,279 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:12:55,279 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:12:55,288 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:12:55,310 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:12:58,286 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:58] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:12:58,319 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:58] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:12:58,434 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:58] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:12:58,435 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:58] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:12:59,101 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:12:59,101 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:12:59,102 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:12:59,102 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:12:59,102 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:59] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:12:59,103 - discord.client - INFO - logging in using static token
2025-06-30 22:13:00,218 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "GET / HTTP/1.1" 200 -
2025-06-30 22:13:00,250 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:13:00,252 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:13:00,268 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "GET /socket.io/?EIO=4&transport=polling&t=PV0mK7c HTTP/1.1" 200 -
2025-06-30 22:13:00,269 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:13:00,282 - web_server - INFO - Client connected
2025-06-30 22:13:00,283 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "POST /socket.io/?EIO=4&transport=polling&t=PV0mK7s&sid=yJfgQIvvFTQB1n4eAAAA HTTP/1.1" 200 -
2025-06-30 22:13:00,285 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "GET /socket.io/?EIO=4&transport=polling&t=PV0mK7t&sid=yJfgQIvvFTQB1n4eAAAA HTTP/1.1" 200 -
2025-06-30 22:13:00,291 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "GET /socket.io/?EIO=4&transport=polling&t=PV0mK82&sid=yJfgQIvvFTQB1n4eAAAA HTTP/1.1" 200 -
2025-06-30 22:13:01,165 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:13:12,246 - web_server - INFO - Client disconnected
2025-06-30 22:13:12,246 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET /socket.io/?EIO=4&transport=websocket&sid=yJfgQIvvFTQB1n4eAAAA HTTP/1.1" 200 -
2025-06-30 22:13:12,257 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET / HTTP/1.1" 200 -
2025-06-30 22:13:12,298 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:13:12,301 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:13:12,316 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mN3v HTTP/1.1" 200 -
2025-06-30 22:13:12,325 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:13:12,333 - web_server - INFO - Client connected
2025-06-30 22:13:12,334 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "POST /socket.io/?EIO=4&transport=polling&t=PV0mN46&sid=I3grbkGuPFdmVpuQAAAC HTTP/1.1" 200 -
2025-06-30 22:13:12,334 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mN46.0&sid=I3grbkGuPFdmVpuQAAAC HTTP/1.1" 200 -
2025-06-30 22:13:12,344 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mN4L&sid=I3grbkGuPFdmVpuQAAAC HTTP/1.1" 200 -
2025-06-30 22:13:15,116 - web_server - INFO - Client disconnected
2025-06-30 22:13:15,117 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET /socket.io/?EIO=4&transport=websocket&sid=I3grbkGuPFdmVpuQAAAC HTTP/1.1" 200 -
2025-06-30 22:13:15,120 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET / HTTP/1.1" 200 -
2025-06-30 22:13:15,154 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:13:15,156 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:13:15,172 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET /socket.io/?EIO=4&transport=polling&t=PV0mNmX HTTP/1.1" 200 -
2025-06-30 22:13:15,181 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:13:15,190 - web_server - INFO - Client connected
2025-06-30 22:13:15,190 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "POST /socket.io/?EIO=4&transport=polling&t=PV0mNmm&sid=M1aCD6JvcJH_c7CKAAAE HTTP/1.1" 200 -
2025-06-30 22:13:15,191 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET /socket.io/?EIO=4&transport=polling&t=PV0mNmn&sid=M1aCD6JvcJH_c7CKAAAE HTTP/1.1" 200 -
2025-06-30 22:13:15,199 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET /socket.io/?EIO=4&transport=polling&t=PV0mNmy&sid=M1aCD6JvcJH_c7CKAAAE HTTP/1.1" 200 -
2025-06-30 22:14:03,738 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:14:03,828 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:14:04,924 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:14:04,924 - __main__ - INFO - Initializing database...
2025-06-30 22:14:04,925 - __main__ - INFO - Database initialized
2025-06-30 22:14:04,925 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:14:04,925 - __main__ - INFO - Initializing web server...
2025-06-30 22:14:04,966 - __main__ - INFO - Web server initialized
2025-06-30 22:14:04,966 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:14:04,966 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:14:04,966 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:14:04,975 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:14:04,989 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:14:05,000 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0mZtn HTTP/1.1" 200 -
2025-06-30 22:14:05,009 - web_server - INFO - Client connected
2025-06-30 22:14:05,010 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:05] "POST /socket.io/?EIO=4&transport=polling&t=PV0mZxC&sid=WZVkoTRmn7aULX5aAAAA HTTP/1.1" 200 -
2025-06-30 22:14:05,010 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0mZxD&sid=WZVkoTRmn7aULX5aAAAA HTTP/1.1" 200 -
2025-06-30 22:14:05,017 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0mZxN&sid=WZVkoTRmn7aULX5aAAAA HTTP/1.1" 200 -
2025-06-30 22:14:19,080 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:14:19,171 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:14:20,213 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:14:20,213 - __main__ - INFO - Initializing database...
2025-06-30 22:14:20,214 - __main__ - INFO - Database initialized
2025-06-30 22:14:20,214 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:14:20,215 - __main__ - INFO - Initializing web server...
2025-06-30 22:14:20,257 - __main__ - INFO - Web server initialized
2025-06-30 22:14:20,258 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:14:20,258 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:14:20,258 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:14:20,268 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:14:20,283 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:14:20,437 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:20] "GET /socket.io/?EIO=4&transport=polling&t=PV0mdiG HTTP/1.1" 200 -
2025-06-30 22:14:20,449 - web_server - INFO - Client connected
2025-06-30 22:14:20,450 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:20] "POST /socket.io/?EIO=4&transport=polling&t=PV0mdiQ&sid=BOeqK-bq0eXvoG7vAAAA HTTP/1.1" 200 -
2025-06-30 22:14:20,452 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:20] "GET /socket.io/?EIO=4&transport=polling&t=PV0mdiS&sid=BOeqK-bq0eXvoG7vAAAA HTTP/1.1" 200 -
2025-06-30 22:14:20,464 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:20] "GET /socket.io/?EIO=4&transport=polling&t=PV0mdih&sid=BOeqK-bq0eXvoG7vAAAA HTTP/1.1" 200 -
2025-06-30 22:14:22,200 - web_server - INFO - Client disconnected
2025-06-30 22:14:22,201 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:22] "GET /socket.io/?EIO=4&transport=websocket&sid=BOeqK-bq0eXvoG7vAAAA HTTP/1.1" 200 -
2025-06-30 22:14:22,217 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:22] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:14:22,252 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:22] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:14:22,384 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:22] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:14:22,384 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:22] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:14:23,370 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:14:23,371 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:14:23,372 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:14:23,372 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:14:23,372 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:23] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:14:23,373 - discord.client - INFO - logging in using static token
2025-06-30 22:14:24,270 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:14:24,503 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "GET / HTTP/1.1" 200 -
2025-06-30 22:14:24,540 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:14:24,541 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:14:24,554 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "GET /socket.io/?EIO=4&transport=polling&t=PV0meic HTTP/1.1" 200 -
2025-06-30 22:14:24,555 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:14:24,570 - web_server - INFO - Client connected
2025-06-30 22:14:24,570 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "POST /socket.io/?EIO=4&transport=polling&t=PV0meip&sid=NZPAetIgF3NMBtvRAAAC HTTP/1.1" 200 -
2025-06-30 22:14:24,570 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "GET /socket.io/?EIO=4&transport=polling&t=PV0meir&sid=NZPAetIgF3NMBtvRAAAC HTTP/1.1" 200 -
2025-06-30 22:14:24,579 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "GET /socket.io/?EIO=4&transport=polling&t=PV0mej1&sid=NZPAetIgF3NMBtvRAAAC HTTP/1.1" 200 -
2025-06-30 22:14:31,367 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:14:31,478 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:14:32,541 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:14:32,541 - __main__ - INFO - Initializing database...
2025-06-30 22:14:32,542 - __main__ - INFO - Database initialized
2025-06-30 22:14:32,542 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:14:32,543 - __main__ - INFO - Initializing web server...
2025-06-30 22:14:32,584 - __main__ - INFO - Web server initialized
2025-06-30 22:14:32,584 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:14:32,584 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:14:32,585 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:14:32,594 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:14:32,610 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:14:32,620 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:32] "GET /socket.io/?EIO=4&transport=polling&t=PV0mgZQ HTTP/1.1" 200 -
2025-06-30 22:14:32,628 - web_server - INFO - Client connected
2025-06-30 22:14:32,629 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:32] "POST /socket.io/?EIO=4&transport=polling&t=PV0mggm&sid=89xVfJdV4luDL4A5AAAA HTTP/1.1" 200 -
2025-06-30 22:14:32,630 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:32] "GET /socket.io/?EIO=4&transport=polling&t=PV0mggn&sid=89xVfJdV4luDL4A5AAAA HTTP/1.1" 200 -
2025-06-30 22:14:32,637 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:32] "GET /socket.io/?EIO=4&transport=polling&t=PV0mggx&sid=89xVfJdV4luDL4A5AAAA HTTP/1.1" 200 -
2025-06-30 22:14:37,715 - web_server - INFO - Client disconnected
2025-06-30 22:14:37,715 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:37] "GET /socket.io/?EIO=4&transport=websocket&sid=89xVfJdV4luDL4A5AAAA HTTP/1.1" 200 -
2025-06-30 22:14:37,723 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:37] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:14:37,741 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:37] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:14:37,838 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:37] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:14:37,839 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:37] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:14:38,881 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:14:38,881 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:14:38,882 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:14:38,882 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:14:38,882 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:38] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:14:38,883 - discord.client - INFO - logging in using static token
2025-06-30 22:14:39,806 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:14:39,991 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:39] "GET / HTTP/1.1" 200 -
2025-06-30 22:14:40,035 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:14:40,040 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:14:40,056 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "GET /socket.io/?EIO=4&transport=polling&t=PV0miUm HTTP/1.1" 200 -
2025-06-30 22:14:40,062 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:14:40,067 - web_server - INFO - Client connected
2025-06-30 22:14:40,068 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "POST /socket.io/?EIO=4&transport=polling&t=PV0miU-&sid=ARu5LBcOs2frUi4SAAAC HTTP/1.1" 200 -
2025-06-30 22:14:40,071 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "GET /socket.io/?EIO=4&transport=polling&t=PV0miV2&sid=ARu5LBcOs2frUi4SAAAC HTTP/1.1" 200 -
2025-06-30 22:14:40,077 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "GET /socket.io/?EIO=4&transport=polling&t=PV0miVD&sid=ARu5LBcOs2frUi4SAAAC HTTP/1.1" 200 -
2025-06-30 22:15:06,951 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:15:06,952 - __main__ - INFO - Initializing database...
2025-06-30 22:15:06,953 - __main__ - INFO - Database initialized
2025-06-30 22:15:06,953 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:15:06,953 - __main__ - INFO - Initializing web server...
2025-06-30 22:15:07,001 - __main__ - INFO - Web server initialized
2025-06-30 22:15:07,001 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:15:07,001 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:15:07,001 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:15:07,021 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:15:07,021 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:15:07,021 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:15:08,024 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:15:08,024 - __main__ - INFO - Initializing database...
2025-06-30 22:15:08,025 - __main__ - INFO - Database initialized
2025-06-30 22:15:08,025 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:15:08,025 - __main__ - INFO - Initializing web server...
2025-06-30 22:15:08,066 - __main__ - INFO - Web server initialized
2025-06-30 22:15:08,066 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:15:08,066 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:15:08,066 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:15:08,076 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:15:08,088 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:15:09,828 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:09] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:15:09,848 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:09] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:15:09,957 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:09] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:15:09,958 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:09] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:15:10,980 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:15:10,980 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:15:10,980 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:15:10,981 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:15:10,981 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:10] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:15:10,982 - discord.client - INFO - logging in using static token
2025-06-30 22:15:12,061 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:15:12,225 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "GET / HTTP/1.1" 200 -
2025-06-30 22:15:12,263 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:15:12,264 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:15:12,282 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mqML HTTP/1.1" 200 -
2025-06-30 22:15:12,284 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:15:12,302 - web_server - INFO - Client connected
2025-06-30 22:15:12,303 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "POST /socket.io/?EIO=4&transport=polling&t=PV0mqMY&sid=fP6RjOusIQAaRlLoAAAA HTTP/1.1" 200 -
2025-06-30 22:15:12,304 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mqMa&sid=fP6RjOusIQAaRlLoAAAA HTTP/1.1" 200 -
2025-06-30 22:15:12,315 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mqMs&sid=fP6RjOusIQAaRlLoAAAA HTTP/1.1" 200 -
2025-06-30 22:16:40,620 - web_server - INFO - Client disconnected
2025-06-30 22:16:40,620 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:40] "GET /socket.io/?EIO=4&transport=websocket&sid=fP6RjOusIQAaRlLoAAAA HTTP/1.1" 200 -
2025-06-30 22:16:40,632 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:40] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:16:40,675 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:40] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:16:40,677 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:40] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:16:47,695 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:16:47,695 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:16:47,695 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:16:47,696 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:47] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:16:47,696 - discord.client - INFO - logging in using static token
2025-06-30 22:16:48,953 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:48] "GET / HTTP/1.1" 200 -
2025-06-30 22:16:48,980 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:16:48,989 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:48] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:16:48,992 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:48] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:16:49,006 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0nBzg HTTP/1.1" 200 -
2025-06-30 22:16:49,007 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:49] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:16:49,022 - web_server - INFO - Client connected
2025-06-30 22:16:49,022 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:49] "POST /socket.io/?EIO=4&transport=polling&t=PV0nBzu&sid=C2mQoBh4ommG4p_gAAAC HTTP/1.1" 200 -
2025-06-30 22:16:49,023 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0nBzv&sid=C2mQoBh4ommG4p_gAAAC HTTP/1.1" 200 -
2025-06-30 22:16:49,030 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0nB-4&sid=C2mQoBh4ommG4p_gAAAC HTTP/1.1" 200 -
2025-06-30 22:17:14,845 - web_server - INFO - Client disconnected
2025-06-30 22:17:14,845 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET /socket.io/?EIO=4&transport=websocket&sid=C2mQoBh4ommG4p_gAAAC HTTP/1.1" 200 -
2025-06-30 22:17:14,854 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET / HTTP/1.1" 200 -
2025-06-30 22:17:14,889 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:17:14,890 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:17:14,906 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET /socket.io/?EIO=4&transport=polling&t=PV0nIIM HTTP/1.1" 200 -
2025-06-30 22:17:14,913 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:17:14,921 - web_server - INFO - Client connected
2025-06-30 22:17:14,922 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "POST /socket.io/?EIO=4&transport=polling&t=PV0nIIc&sid=I3YuQKlr4v_uOq3-AAAE HTTP/1.1" 200 -
2025-06-30 22:17:14,922 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET /socket.io/?EIO=4&transport=polling&t=PV0nIIc.0&sid=I3YuQKlr4v_uOq3-AAAE HTTP/1.1" 200 -
2025-06-30 22:17:14,931 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET /socket.io/?EIO=4&transport=polling&t=PV0nIIn&sid=I3YuQKlr4v_uOq3-AAAE HTTP/1.1" 200 -
2025-06-30 22:20:49,823 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:20:49,823 - __main__ - INFO - Initializing database...
2025-06-30 22:20:49,824 - __main__ - INFO - Database initialized
2025-06-30 22:20:49,824 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:20:49,825 - __main__ - INFO - Initializing web server...
2025-06-30 22:20:49,868 - __main__ - INFO - Web server initialized
2025-06-30 22:20:49,868 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:20:49,868 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:20:49,868 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:20:49,885 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:20:49,885 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:20:49,885 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:20:50,893 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:20:50,894 - __main__ - INFO - Initializing database...
2025-06-30 22:20:50,895 - __main__ - INFO - Database initialized
2025-06-30 22:20:50,895 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:20:50,895 - __main__ - INFO - Initializing web server...
2025-06-30 22:20:50,936 - __main__ - INFO - Web server initialized
2025-06-30 22:20:50,936 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:20:50,936 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:20:50,937 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:20:50,946 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:20:50,961 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:20:52,895 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:20:52] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:20:53,009 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:20:53] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:20:53,010 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:20:53] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:21:09,657 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:21:10,694 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:10] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:21:14,292 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:21:15,551 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:15] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:21:24,732 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:21:25,613 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:25] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:21:29,206 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:21:30,285 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:30] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:21:38,726 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:21:38,726 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:21:38,726 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:21:38,727 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:21:38,727 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:38] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:21:38,727 - discord.client - INFO - logging in using static token
2025-06-30 22:21:39,465 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "GET / HTTP/1.1" 200 -
2025-06-30 22:21:39,547 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:21:39,547 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:21:39,566 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "GET /socket.io/?EIO=4&transport=polling&t=PV0oIvd HTTP/1.1" 200 -
2025-06-30 22:21:39,571 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:21:39,579 - web_server - INFO - Client connected
2025-06-30 22:21:39,579 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "POST /socket.io/?EIO=4&transport=polling&t=PV0oIvp&sid=Vsvu2Tsz6zzqHJ0JAAAA HTTP/1.1" 200 -
2025-06-30 22:21:39,583 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "GET /socket.io/?EIO=4&transport=polling&t=PV0oIvq&sid=Vsvu2Tsz6zzqHJ0JAAAA HTTP/1.1" 200 -
2025-06-30 22:21:39,592 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "GET /socket.io/?EIO=4&transport=polling&t=PV0oIw3&sid=Vsvu2Tsz6zzqHJ0JAAAA HTTP/1.1" 200 -
2025-06-30 22:21:40,125 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:21:59,710 - web_server - INFO - Client disconnected
2025-06-30 22:21:59,711 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:59] "GET /socket.io/?EIO=4&transport=websocket&sid=Vsvu2Tsz6zzqHJ0JAAAA HTTP/1.1" 200 -
2025-06-30 22:22:15,245 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:22:15] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:22:15,281 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:22:15] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:22:15,282 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:22:15] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:22:21,927 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:22:22,804 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:22:22] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:22:53,147 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:22:53,992 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:22:53] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:24:50,244 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:24:50,244 - __main__ - INFO - Initializing database...
2025-06-30 22:24:50,245 - __main__ - INFO - Database initialized
2025-06-30 22:24:50,246 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:24:50,246 - __main__ - INFO - Initializing web server...
2025-06-30 22:24:50,286 - __main__ - INFO - Web server initialized
2025-06-30 22:24:50,288 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:24:50,288 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:24:50,309 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:24:50,309 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:24:50,309 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:24:51,262 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:24:51,262 - __main__ - INFO - Initializing database...
2025-06-30 22:24:51,264 - __main__ - INFO - Database initialized
2025-06-30 22:24:51,264 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:24:51,264 - __main__ - INFO - Initializing web server...
2025-06-30 22:24:51,304 - __main__ - INFO - Web server initialized
2025-06-30 22:24:51,304 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:24:51,304 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:24:51,315 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:24:51,328 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:24:59,071 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:24:59] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:24:59,101 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:24:59] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:24:59,212 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:24:59] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:24:59,212 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:24:59] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:25:17,830 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:25:19,353 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:25:19] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:26:21,818 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:26:21,939 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:26:23,174 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:26:23,174 - __main__ - INFO - Initializing database...
2025-06-30 22:26:23,176 - __main__ - INFO - Database initialized
2025-06-30 22:26:23,176 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:26:23,176 - __main__ - INFO - Initializing web server...
2025-06-30 22:26:23,222 - __main__ - INFO - Web server initialized
2025-06-30 22:26:23,223 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:26:23,223 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:26:23,235 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:26:23,258 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:27:38,119 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:27:38,119 - __main__ - INFO - Initializing database...
2025-06-30 22:27:38,121 - __main__ - INFO - Database initialized
2025-06-30 22:27:38,121 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:27:38,121 - __main__ - INFO - Initializing web server...
2025-06-30 22:27:38,163 - __main__ - INFO - Web server initialized
2025-06-30 22:27:38,163 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:27:38,163 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:27:38,163 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:27:38,180 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:27:38,180 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:27:38,181 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:27:39,255 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:27:39,255 - __main__ - INFO - Initializing database...
2025-06-30 22:27:39,257 - __main__ - INFO - Database initialized
2025-06-30 22:27:39,257 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:27:39,257 - __main__ - INFO - Initializing web server...
2025-06-30 22:27:39,323 - __main__ - INFO - Web server initialized
2025-06-30 22:27:39,323 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:27:39,323 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:27:39,324 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:27:39,341 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:27:39,365 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:27:41,612 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:27:41] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:27:41,645 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:27:41] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:27:41,772 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:27:41] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:27:41,773 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:27:41] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:28:03,043 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:28:04,147 - __main__ - INFO - Login response status: 400
2025-06-30 22:28:04,148 - __main__ - INFO - Login response: {"message": "Invalid Form Body", "code": 50035, "errors": {"login": {"_errors": [{"code": "BASE_TYPE_REQUIRED", "message": "This field is required"}]}}}...
2025-06-30 22:28:04,148 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:04] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:28:09,478 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:28:09,479 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:28:09,479 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:28:09,480 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:28:09,480 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:09] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:28:09,481 - discord.client - INFO - logging in using static token
2025-06-30 22:28:11,338 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:28:11,721 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "GET / HTTP/1.1" 200 -
2025-06-30 22:28:11,778 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:28:11,780 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:28:11,810 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "GET /socket.io/?EIO=4&transport=polling&t=PV0pogK HTTP/1.1" 200 -
2025-06-30 22:28:11,815 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:28:11,820 - web_server - INFO - Client connected
2025-06-30 22:28:11,821 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "POST /socket.io/?EIO=4&transport=polling&t=PV0pogd&sid=NT36N18AFZ0eRZhTAAAA HTTP/1.1" 200 -
2025-06-30 22:28:11,828 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "GET /socket.io/?EIO=4&transport=polling&t=PV0poge&sid=NT36N18AFZ0eRZhTAAAA HTTP/1.1" 200 -
2025-06-30 22:28:14,931 - web_server - INFO - Client disconnected
2025-06-30 22:28:14,931 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:14] "GET /socket.io/?EIO=4&transport=websocket&sid=NT36N18AFZ0eRZhTAAAA HTTP/1.1" 200 -
2025-06-30 22:28:19,462 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:19] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:28:19,492 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:19] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:28:19,495 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:19] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:28:21,364 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:28:21,365 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:28:21,365 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:28:21,365 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:21] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:28:21,365 - discord.client - INFO - logging in using static token
2025-06-30 22:28:22,240 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:28:22,409 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "GET / HTTP/1.1" 200 -
2025-06-30 22:28:22,487 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:28:22,489 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:28:22,505 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "GET /socket.io/?EIO=4&transport=polling&t=PV0prHc HTTP/1.1" 200 -
2025-06-30 22:28:22,518 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:28:22,529 - web_server - INFO - Client connected
2025-06-30 22:28:22,532 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "POST /socket.io/?EIO=4&transport=polling&t=PV0prHp&sid=nIFlgbHyuS26BwdiAAAC HTTP/1.1" 200 -
2025-06-30 22:28:22,534 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "GET /socket.io/?EIO=4&transport=polling&t=PV0prHw&sid=nIFlgbHyuS26BwdiAAAC HTTP/1.1" 200 -
2025-06-30 22:28:22,559 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "GET /socket.io/?EIO=4&transport=polling&t=PV0prIN&sid=nIFlgbHyuS26BwdiAAAC HTTP/1.1" 200 -
2025-06-30 22:28:24,368 - web_server - INFO - Client disconnected
2025-06-30 22:28:24,369 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:24] "GET /socket.io/?EIO=4&transport=websocket&sid=nIFlgbHyuS26BwdiAAAC HTTP/1.1" 200 -
2025-06-30 22:28:36,861 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:36] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:28:36,897 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:36] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:28:36,900 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:36] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:28:42,173 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:28:43,284 - __main__ - INFO - Login response status: 400
2025-06-30 22:28:43,284 - __main__ - INFO - Login response: {"message": "Invalid Form Body", "code": 50035, "errors": {"login": {"_errors": [{"code": "BASE_TYPE_REQUIRED", "message": "This field is required"}]}}}...
2025-06-30 22:28:43,285 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:43] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:29:11,931 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:29:11,955 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<_wait_for_close() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\aiohttp\connector.py:136>>
2025-06-30 22:29:12,074 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:29:13,295 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:29:13,297 - __main__ - INFO - Initializing database...
2025-06-30 22:29:13,298 - __main__ - INFO - Database initialized
2025-06-30 22:29:13,298 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:29:13,298 - __main__ - INFO - Initializing web server...
2025-06-30 22:29:13,342 - __main__ - INFO - Web server initialized
2025-06-30 22:29:13,342 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:29:13,342 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:29:13,342 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:29:13,354 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:29:13,373 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:29:29,475 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:29:29,569 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:29:30,660 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:29:30,660 - __main__ - INFO - Initializing database...
2025-06-30 22:29:30,662 - __main__ - INFO - Database initialized
2025-06-30 22:29:30,662 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:29:30,662 - __main__ - INFO - Initializing web server...
2025-06-30 22:29:30,707 - __main__ - INFO - Web server initialized
2025-06-30 22:29:30,707 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:29:30,707 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:29:30,707 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:29:30,716 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:29:30,732 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:29:55,892 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:29:56,071 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:29:57,323 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:29:57,323 - __main__ - INFO - Initializing database...
2025-06-30 22:29:57,324 - __main__ - INFO - Database initialized
2025-06-30 22:29:57,324 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:29:57,324 - __main__ - INFO - Initializing web server...
2025-06-30 22:29:57,372 - __main__ - INFO - Web server initialized
2025-06-30 22:29:57,372 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:29:57,372 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:29:57,372 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:29:57,383 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:29:57,397 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:30:18,135 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:30:18,135 - __main__ - INFO - Initializing database...
2025-06-30 22:30:18,136 - __main__ - INFO - Database initialized
2025-06-30 22:30:18,136 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:30:18,136 - __main__ - INFO - Initializing web server...
2025-06-30 22:30:18,179 - __main__ - INFO - Web server initialized
2025-06-30 22:30:18,179 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:30:18,179 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:30:18,180 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:30:18,198 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:30:18,199 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:30:18,199 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:30:19,645 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:30:19,645 - __main__ - INFO - Initializing database...
2025-06-30 22:30:19,646 - __main__ - INFO - Database initialized
2025-06-30 22:30:19,646 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:30:19,646 - __main__ - INFO - Initializing web server...
2025-06-30 22:30:19,690 - __main__ - INFO - Web server initialized
2025-06-30 22:30:19,690 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:30:19,690 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:30:19,690 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:30:19,700 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:30:19,717 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:31:01,309 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:31:01,309 - __main__ - INFO - Initializing database...
2025-06-30 22:31:01,310 - __main__ - INFO - Database initialized
2025-06-30 22:31:01,311 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:31:01,311 - __main__ - INFO - Initializing web server...
2025-06-30 22:31:01,353 - __main__ - INFO - Web server initialized
2025-06-30 22:31:01,353 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:31:01,353 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:31:01,374 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:31:01,374 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:31:01,375 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:31:02,535 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:31:02,535 - __main__ - INFO - Initializing database...
2025-06-30 22:31:02,537 - __main__ - INFO - Database initialized
2025-06-30 22:31:02,537 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:31:02,537 - __main__ - INFO - Initializing web server...
2025-06-30 22:31:02,591 - __main__ - INFO - Web server initialized
2025-06-30 22:31:02,591 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:31:02,592 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:31:02,605 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:31:02,631 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:31:06,508 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:06] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:31:06,627 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:06] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:31:06,628 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:06] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:31:06,663 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:06] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-30 22:31:14,782 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:31:16,610 - __main__ - INFO - Login response status: 200
2025-06-30 22:31:16,610 - __main__ - INFO - Login response: {"user_id":"1349550601117106176","token":"MTM0OTU1MDYwMTExNzEwNjE3Ng.GbwiFO.cRwtN5QvUgBduulAmh9-4NMeL7mu34Wuhp0hKY","user_settings":{"locale":"en-US","theme":"dark"}}
...
2025-06-30 22:31:16,610 - __main__ - INFO - Successfully obtained token from email/password login
2025-06-30 22:31:16,611 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:31:16,611 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:31:16,612 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:31:16,612 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:31:16,613 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:16] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:31:16,614 - discord.client - INFO - logging in using static token
2025-06-30 22:31:17,690 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:17] "GET / HTTP/1.1" 200 -
2025-06-30 22:31:17,763 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:17] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:31:17,765 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:17] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:31:17,791 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:17] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:31:17,791 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:17] "GET /socket.io/?EIO=4&transport=polling&t=PV0qW4F HTTP/1.1" 200 -
2025-06-30 22:31:17,805 - web_server - INFO - Client connected
2025-06-30 22:31:17,806 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:17] "POST /socket.io/?EIO=4&transport=polling&t=PV0qW4a&sid=iQ4HzsZ4QQ59-MkrAAAA HTTP/1.1" 200 -
2025-06-30 22:31:17,811 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:17] "GET /socket.io/?EIO=4&transport=polling&t=PV0qW4d&sid=iQ4HzsZ4QQ59-MkrAAAA HTTP/1.1" 200 -
2025-06-30 22:31:17,820 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:17] "GET /socket.io/?EIO=4&transport=polling&t=PV0qW4v&sid=iQ4HzsZ4QQ59-MkrAAAA HTTP/1.1" 200 -
2025-06-30 22:31:17,878 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:31:21,275 - web_server - INFO - Client disconnected
2025-06-30 22:31:21,275 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:21] "GET /socket.io/?EIO=4&transport=websocket&sid=iQ4HzsZ4QQ59-MkrAAAA HTTP/1.1" 200 -
2025-06-30 22:31:24,731 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:24] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:31:24,775 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:24] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:31:24,775 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:24] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:31:28,040 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:31:29,530 - __main__ - INFO - Login response status: 400
2025-06-30 22:31:29,530 - __main__ - INFO - Login response: {"message": "Invalid Form Body", "code": 50035, "errors": {"login": {"_errors": [{"code": "INVALID_LOGIN", "message": "Login or password is invalid."}]}, "password": {"_errors": [{"code": "INVALID_LOGIN", "message": "Login or password is invalid."}]}}}...
2025-06-30 22:31:29,532 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:31:29] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:32:06,780 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:32:06,780 - __main__ - INFO - Initializing database...
2025-06-30 22:32:06,782 - __main__ - INFO - Database initialized
2025-06-30 22:32:06,782 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:32:06,782 - __main__ - INFO - Initializing web server...
2025-06-30 22:32:06,824 - __main__ - INFO - Web server initialized
2025-06-30 22:32:06,824 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:32:06,824 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:32:06,825 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:32:06,842 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:32:06,842 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:32:06,842 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:32:07,875 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:32:07,875 - __main__ - INFO - Initializing database...
2025-06-30 22:32:07,876 - __main__ - INFO - Database initialized
2025-06-30 22:32:07,876 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:32:07,876 - __main__ - INFO - Initializing web server...
2025-06-30 22:32:07,919 - __main__ - INFO - Web server initialized
2025-06-30 22:32:07,919 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:32:07,919 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:32:07,919 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:32:07,929 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:32:07,944 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:32:35,112 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\discord_client.py', reloading
2025-06-30 22:32:35,204 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:32:36,304 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:32:36,304 - __main__ - INFO - Initializing database...
2025-06-30 22:32:36,306 - __main__ - INFO - Database initialized
2025-06-30 22:32:36,306 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:32:36,306 - __main__ - INFO - Initializing web server...
2025-06-30 22:32:36,348 - __main__ - INFO - Web server initialized
2025-06-30 22:32:36,348 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:32:36,348 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:32:36,348 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:32:36,359 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:32:36,378 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:32:47,470 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\discord_client.py', reloading
2025-06-30 22:32:47,610 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:32:48,781 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:32:48,781 - __main__ - INFO - Initializing database...
2025-06-30 22:32:48,782 - __main__ - INFO - Database initialized
2025-06-30 22:32:48,782 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:32:48,782 - __main__ - INFO - Initializing web server...
2025-06-30 22:32:48,827 - __main__ - INFO - Web server initialized
2025-06-30 22:32:48,827 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:32:48,827 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:32:48,827 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:32:48,837 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:32:48,854 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:32:55,220 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:32:55] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:32:55,281 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:32:55] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:32:55,434 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:32:55] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:32:55,437 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:32:55] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:33:00,940 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:33:01,051 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:33:02,194 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:33:02,194 - __main__ - INFO - Initializing database...
2025-06-30 22:33:02,196 - __main__ - INFO - Database initialized
2025-06-30 22:33:02,197 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:33:02,197 - __main__ - INFO - Initializing web server...
2025-06-30 22:33:02,241 - __main__ - INFO - Web server initialized
2025-06-30 22:33:02,241 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:33:02,241 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:33:02,241 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:33:02,252 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:33:02,270 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:34:31,004 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:34:31,004 - __main__ - INFO - Initializing database...
2025-06-30 22:34:31,006 - __main__ - INFO - Database initialized
2025-06-30 22:34:31,006 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:34:31,006 - __main__ - INFO - Initializing web server...
2025-06-30 22:34:31,048 - __main__ - INFO - Web server initialized
2025-06-30 22:34:31,048 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:34:31,048 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:34:31,048 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:34:31,068 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:34:31,068 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:34:31,068 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:34:32,164 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:34:32,164 - __main__ - INFO - Initializing database...
2025-06-30 22:34:32,166 - __main__ - INFO - Database initialized
2025-06-30 22:34:32,166 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:34:32,166 - __main__ - INFO - Initializing web server...
2025-06-30 22:34:32,212 - __main__ - INFO - Web server initialized
2025-06-30 22:34:32,212 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:34:32,213 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:34:32,213 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:34:32,224 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:34:32,240 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:34:53,072 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:34:53] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:34:53,189 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:34:53] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:34:53,196 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:34:53] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 22:35:04,158 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:35:05,882 - __main__ - INFO - Login response status: 200
2025-06-30 22:35:05,882 - __main__ - INFO - Login response: {"user_id":"1349550601117106176","token":"MTM0OTU1MDYwMTExNzEwNjE3Ng.G90X5b.tJbVus1dx_pzlBfsdZMFNOsdrCP7snlsI74UG8","user_settings":{"locale":"en-US","theme":"dark"}}
...
2025-06-30 22:35:05,882 - __main__ - INFO - Successfully obtained token from email/password login
2025-06-30 22:35:05,883 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:35:05,883 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:35:05,883 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:35:05,883 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:35:05,884 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:05] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:35:05,885 - discord.client - INFO - logging in using static token
2025-06-30 22:35:06,965 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:35:06,983 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:06] "GET / HTTP/1.1" 200 -
2025-06-30 22:35:07,055 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:07] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:35:07,056 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:07] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:35:07,073 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:07] "GET /socket.io/?EIO=4&transport=polling&t=PV0rO2y HTTP/1.1" 200 -
2025-06-30 22:35:07,077 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:07] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:35:07,094 - web_server - INFO - Client connected
2025-06-30 22:35:07,095 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:07] "POST /socket.io/?EIO=4&transport=polling&t=PV0rO3D&sid=qCvJT-OYznWMlqOcAAAA HTTP/1.1" 200 -
2025-06-30 22:35:07,096 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:07] "GET /socket.io/?EIO=4&transport=polling&t=PV0rO3E&sid=qCvJT-OYznWMlqOcAAAA HTTP/1.1" 200 -
2025-06-30 22:35:07,110 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:07] "GET /socket.io/?EIO=4&transport=polling&t=PV0rO3W&sid=qCvJT-OYznWMlqOcAAAA HTTP/1.1" 200 -
2025-06-30 22:35:10,498 - discord_client - WARNING - Client not ready, cannot refresh data
2025-06-30 22:35:10,499 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:10] "POST /api/refresh HTTP/1.1" 200 -
2025-06-30 22:35:12,985 - discord_client - WARNING - Client not ready, cannot refresh data
2025-06-30 22:35:12,986 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:12] "POST /api/refresh HTTP/1.1" 200 -
2025-06-30 22:35:54,753 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\discord_client.py', reloading
2025-06-30 22:35:54,911 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:35:57,034 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:35:57,034 - __main__ - INFO - Initializing database...
2025-06-30 22:35:57,036 - __main__ - INFO - Database initialized
2025-06-30 22:35:57,036 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:35:57,037 - __main__ - INFO - Initializing web server...
2025-06-30 22:35:57,100 - __main__ - INFO - Web server initialized
2025-06-30 22:35:57,100 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:35:57,100 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:35:57,100 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:35:57,113 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:35:57,132 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:35:57,149 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:57] "GET /socket.io/?EIO=4&transport=polling&t=PV0rZvG HTTP/1.1" 200 -
2025-06-30 22:35:57,162 - web_server - INFO - Client connected
2025-06-30 22:35:57,162 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:57] "POST /socket.io/?EIO=4&transport=polling&t=PV0raHb&sid=azNZq62PY0tApVkSAAAA HTTP/1.1" 200 -
2025-06-30 22:35:57,166 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:57] "GET /socket.io/?EIO=4&transport=polling&t=PV0raHd&sid=azNZq62PY0tApVkSAAAA HTTP/1.1" 200 -
2025-06-30 22:35:57,176 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:57] "GET /socket.io/?EIO=4&transport=polling&t=PV0raHr&sid=azNZq62PY0tApVkSAAAA HTTP/1.1" 200 -
2025-06-30 22:35:57,190 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:35:57] "GET /socket.io/?EIO=4&transport=polling&t=PV0raH_&sid=azNZq62PY0tApVkSAAAA HTTP/1.1" 200 -
2025-06-30 22:36:15,293 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:36:15,470 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:36:16,876 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:36:16,876 - __main__ - INFO - Initializing database...
2025-06-30 22:36:16,877 - __main__ - INFO - Database initialized
2025-06-30 22:36:16,877 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:36:16,877 - __main__ - INFO - Initializing web server...
2025-06-30 22:36:16,934 - __main__ - INFO - Web server initialized
2025-06-30 22:36:16,934 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:36:16,934 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:36:16,934 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:36:16,946 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:36:16,962 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:36:16,975 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:36:16] "GET /socket.io/?EIO=4&transport=polling&t=PV0re_F HTTP/1.1" 200 -
2025-06-30 22:36:16,990 - web_server - INFO - Client connected
2025-06-30 22:36:16,991 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:36:16] "POST /socket.io/?EIO=4&transport=polling&t=PV0rf7I&sid=g97wi3ctRWa1JOPTAAAA HTTP/1.1" 200 -
2025-06-30 22:36:16,991 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:36:16] "GET /socket.io/?EIO=4&transport=polling&t=PV0rf7J&sid=g97wi3ctRWa1JOPTAAAA HTTP/1.1" 200 -
2025-06-30 22:37:14,545 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:37:14,545 - __main__ - INFO - Initializing database...
2025-06-30 22:37:14,546 - __main__ - INFO - Database initialized
2025-06-30 22:37:14,546 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:37:14,546 - __main__ - INFO - Initializing web server...
2025-06-30 22:37:14,590 - __main__ - INFO - Web server initialized
2025-06-30 22:37:14,590 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:37:14,590 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:37:14,590 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:37:14,609 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:37:14,609 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:37:14,609 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:37:15,683 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:37:15,684 - __main__ - INFO - Initializing database...
2025-06-30 22:37:15,684 - __main__ - INFO - Database initialized
2025-06-30 22:37:15,685 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:37:15,685 - __main__ - INFO - Initializing web server...
2025-06-30 22:37:15,727 - __main__ - INFO - Web server initialized
2025-06-30 22:37:15,727 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:37:15,727 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:37:15,728 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:37:15,739 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:37:15,753 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:37:16,598 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:16] "GET /socket.io/?EIO=4&transport=polling&t=PV0rtgp HTTP/1.1" 200 -
2025-06-30 22:37:16,605 - web_server - INFO - Client connected
2025-06-30 22:37:16,606 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:16] "POST /socket.io/?EIO=4&transport=polling&t=PV0rtgw&sid=kgRrobqcr9q_Wba8AAAA HTTP/1.1" 200 -
2025-06-30 22:37:16,606 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:16] "GET /socket.io/?EIO=4&transport=polling&t=PV0rtgx&sid=kgRrobqcr9q_Wba8AAAA HTTP/1.1" 200 -
2025-06-30 22:37:16,613 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:16] "GET /socket.io/?EIO=4&transport=polling&t=PV0rth4&sid=kgRrobqcr9q_Wba8AAAA HTTP/1.1" 200 -
2025-06-30 22:37:21,104 - web_server - INFO - Client disconnected
2025-06-30 22:37:21,104 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:21] "GET /socket.io/?EIO=4&transport=websocket&sid=kgRrobqcr9q_Wba8AAAA HTTP/1.1" 200 -
2025-06-30 22:37:21,116 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:21] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:37:21,139 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:21] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:37:21,249 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:21] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 22:37:21,250 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:21] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:37:43,901 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:37:45,519 - __main__ - INFO - Login response status: 200
2025-06-30 22:37:45,519 - __main__ - INFO - Login response: {"user_id":"1349550601117106176","token":"MTM0OTU1MDYwMTExNzEwNjE3Ng.GiJFR7.t9A8aXfUEFsq_YdEx8DEWZVJhWZFjz-QvfLVYA","user_settings":{"locale":"en-US","theme":"dark"}}
...
2025-06-30 22:37:45,519 - __main__ - INFO - Successfully obtained token from email/password login
2025-06-30 22:37:45,519 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:37:45,520 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:37:45,520 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:37:45,520 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:37:45,521 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:45] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:37:45,521 - discord.client - INFO - logging in using static token
2025-06-30 22:37:45,579 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:45] "GET / HTTP/1.1" 200 -
2025-06-30 22:37:45,700 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:45] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:37:45,701 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:45] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:37:45,821 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:45] "GET /socket.io/?EIO=4&transport=polling&t=PV0r-oV HTTP/1.1" 200 -
2025-06-30 22:37:45,827 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:45] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:37:45,844 - web_server - INFO - Client connected
2025-06-30 22:37:45,844 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:45] "POST /socket.io/?EIO=4&transport=polling&t=PV0r-pd&sid=7026Oy_S0BqY0qjZAAAC HTTP/1.1" 200 -
2025-06-30 22:37:45,844 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:45] "GET /socket.io/?EIO=4&transport=polling&t=PV0r-pe&sid=7026Oy_S0BqY0qjZAAAC HTTP/1.1" 200 -
2025-06-30 22:37:45,869 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:45] "GET /socket.io/?EIO=4&transport=polling&t=PV0r-q3&sid=7026Oy_S0BqY0qjZAAAC HTTP/1.1" 200 -
2025-06-30 22:37:46,713 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:37:48,708 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:37:48] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 22:38:02,631 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:02] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 22:38:05,438 - web_server - INFO - Client disconnected
2025-06-30 22:38:05,438 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:05] "GET /socket.io/?EIO=4&transport=websocket&sid=7026Oy_S0BqY0qjZAAAC HTTP/1.1" 200 -
2025-06-30 22:38:05,449 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:05] "GET / HTTP/1.1" 200 -
2025-06-30 22:38:05,488 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:05] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:38:05,489 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:05] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:38:05,508 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0s3c- HTTP/1.1" 200 -
2025-06-30 22:38:05,517 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:05] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:38:05,520 - web_server - INFO - Client connected
2025-06-30 22:38:05,521 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:05] "POST /socket.io/?EIO=4&transport=polling&t=PV0s3dB&sid=IuqCQrHZT-2JkQ05AAAE HTTP/1.1" 200 -
2025-06-30 22:38:05,523 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0s3dC&sid=IuqCQrHZT-2JkQ05AAAE HTTP/1.1" 200 -
2025-06-30 22:38:05,531 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0s3dP&sid=IuqCQrHZT-2JkQ05AAAE HTTP/1.1" 200 -
2025-06-30 22:38:07,065 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:07] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 22:38:44,331 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:38:44,471 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:38:45,865 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:38:45,865 - __main__ - INFO - Initializing database...
2025-06-30 22:38:45,867 - __main__ - INFO - Database initialized
2025-06-30 22:38:45,867 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:38:45,867 - __main__ - INFO - Initializing web server...
2025-06-30 22:38:45,923 - __main__ - INFO - Web server initialized
2025-06-30 22:38:45,923 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:38:45,924 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:38:45,924 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:38:45,935 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:38:45,951 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:38:46,518 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:46] "GET /socket.io/?EIO=4&transport=polling&t=PV0sDdo HTTP/1.1" 200 -
2025-06-30 22:38:46,525 - web_server - INFO - Client connected
2025-06-30 22:38:46,525 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:46] "POST /socket.io/?EIO=4&transport=polling&t=PV0sDdw&sid=I2tacPcMSZjPPOrgAAAA HTTP/1.1" 200 -
2025-06-30 22:38:46,526 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:46] "GET /socket.io/?EIO=4&transport=polling&t=PV0sDdx&sid=I2tacPcMSZjPPOrgAAAA HTTP/1.1" 200 -
2025-06-30 22:38:46,533 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:38:46] "GET /socket.io/?EIO=4&transport=polling&t=PV0sDe4&sid=I2tacPcMSZjPPOrgAAAA HTTP/1.1" 200 -
2025-06-30 22:39:06,092 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:39:06,280 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:39:07,482 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:39:07,482 - __main__ - INFO - Initializing database...
2025-06-30 22:39:07,483 - __main__ - INFO - Database initialized
2025-06-30 22:39:07,483 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:39:07,483 - __main__ - INFO - Initializing web server...
2025-06-30 22:39:07,539 - __main__ - INFO - Web server initialized
2025-06-30 22:39:07,539 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:39:07,539 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:39:07,539 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:39:07,555 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:39:07,573 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:39:07,587 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:39:07] "GET /socket.io/?EIO=4&transport=polling&t=PV0sIj6 HTTP/1.1" 200 -
2025-06-30 22:39:07,593 - web_server - INFO - Client connected
2025-06-30 22:39:07,593 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:39:07] "POST /socket.io/?EIO=4&transport=polling&t=PV0sIn6&sid=9y5jNWOJh-tBdI0OAAAA HTTP/1.1" 200 -
2025-06-30 22:39:07,597 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:39:07] "GET /socket.io/?EIO=4&transport=polling&t=PV0sIn7&sid=9y5jNWOJh-tBdI0OAAAA HTTP/1.1" 200 -
2025-06-30 22:39:07,603 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:39:07] "GET /socket.io/?EIO=4&transport=polling&t=PV0sInG&sid=9y5jNWOJh-tBdI0OAAAA HTTP/1.1" 200 -
2025-06-30 22:40:07,105 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:40:07,106 - __main__ - INFO - Initializing database...
2025-06-30 22:40:07,106 - __main__ - INFO - Database initialized
2025-06-30 22:40:07,106 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:40:07,106 - __main__ - INFO - Initializing web server...
2025-06-30 22:40:07,152 - __main__ - INFO - Web server initialized
2025-06-30 22:40:07,152 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:40:07,152 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:40:07,152 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:40:07,169 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:40:07,169 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:40:07,169 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:40:08,217 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:40:08,217 - __main__ - INFO - Initializing database...
2025-06-30 22:40:08,219 - __main__ - INFO - Database initialized
2025-06-30 22:40:08,219 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:40:08,219 - __main__ - INFO - Initializing web server...
2025-06-30 22:40:08,262 - __main__ - INFO - Web server initialized
2025-06-30 22:40:08,262 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:40:08,262 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:40:08,262 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:40:08,271 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:40:08,285 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:40:09,055 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0sXnS HTTP/1.1" 200 -
2025-06-30 22:40:09,062 - web_server - INFO - Client connected
2025-06-30 22:40:09,063 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:09] "POST /socket.io/?EIO=4&transport=polling&t=PV0sXnY&sid=9P90rl570vKyT-ZkAAAA HTTP/1.1" 200 -
2025-06-30 22:40:09,063 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0sXnZ&sid=9P90rl570vKyT-ZkAAAA HTTP/1.1" 200 -
2025-06-30 22:40:09,071 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0sXnh&sid=9P90rl570vKyT-ZkAAAA HTTP/1.1" 200 -
2025-06-30 22:40:29,588 - web_server - INFO - Client disconnected
2025-06-30 22:40:29,589 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:29] "GET /socket.io/?EIO=4&transport=websocket&sid=9P90rl570vKyT-ZkAAAA HTTP/1.1" 200 -
2025-06-30 22:40:29,601 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:29] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:40:29,620 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:29] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:40:29,731 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:29] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:40:29,733 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:29] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 22:40:53,784 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:40:56,463 - __main__ - INFO - Login response status: 200
2025-06-30 22:40:56,463 - __main__ - INFO - Login response: {"user_id":"1349550601117106176","token":"MTM0OTU1MDYwMTExNzEwNjE3Ng.GG7ihw.TWTToTJcevZUdvsGvZADygzXZ1SLP-pSixg0a0","user_settings":{"locale":"en-US","theme":"dark"}}
...
2025-06-30 22:40:56,463 - __main__ - INFO - Successfully obtained token from email/password login: MTM0OTU1MDYwMTExNzEw...
2025-06-30 22:40:56,463 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:40:56,463 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:40:56,464 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:40:56,464 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:40:56,465 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:56] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:40:56,466 - discord.client - INFO - logging in using static token
2025-06-30 22:40:57,565 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:40:59,561 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:59] "GET / HTTP/1.1" 200 -
2025-06-30 22:40:59,625 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:59] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:40:59,628 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:59] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:40:59,641 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:59] "GET /socket.io/?EIO=4&transport=polling&t=PV0sk7q HTTP/1.1" 200 -
2025-06-30 22:40:59,651 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:59] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:40:59,658 - web_server - INFO - Client connected
2025-06-30 22:40:59,660 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:59] "POST /socket.io/?EIO=4&transport=polling&t=PV0sk82&sid=2lEsx5snj0brMA3vAAAC HTTP/1.1" 200 -
2025-06-30 22:40:59,660 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:59] "GET /socket.io/?EIO=4&transport=polling&t=PV0sk83&sid=2lEsx5snj0brMA3vAAAC HTTP/1.1" 200 -
2025-06-30 22:40:59,669 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:40:59] "GET /socket.io/?EIO=4&transport=polling&t=PV0sk8H&sid=2lEsx5snj0brMA3vAAAC HTTP/1.1" 200 -
2025-06-30 22:41:02,578 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:02] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 22:41:05,371 - web_server - INFO - Client disconnected
2025-06-30 22:41:05,371 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:05] "GET /socket.io/?EIO=4&transport=websocket&sid=2lEsx5snj0brMA3vAAAC HTTP/1.1" 200 -
2025-06-30 22:41:05,383 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:05] "GET / HTTP/1.1" 200 -
2025-06-30 22:41:05,416 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:05] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:41:05,416 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:05] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:41:05,429 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0slYI HTTP/1.1" 200 -
2025-06-30 22:41:05,436 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:05] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:41:05,445 - web_server - INFO - Client connected
2025-06-30 22:41:05,446 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:05] "POST /socket.io/?EIO=4&transport=polling&t=PV0slYS&sid=tngWo12FJiA6_M6sAAAE HTTP/1.1" 200 -
2025-06-30 22:41:05,447 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0slYT&sid=tngWo12FJiA6_M6sAAAE HTTP/1.1" 200 -
2025-06-30 22:41:05,455 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0slYi&sid=tngWo12FJiA6_M6sAAAE HTTP/1.1" 200 -
2025-06-30 22:41:06,844 - web_server - INFO - Client disconnected
2025-06-30 22:41:06,844 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:06] "GET /socket.io/?EIO=4&transport=websocket&sid=tngWo12FJiA6_M6sAAAE HTTP/1.1" 200 -
2025-06-30 22:41:12,504 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:12] "POST /api/test-token HTTP/1.1" 200 -
2025-06-30 22:41:19,610 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:19] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:41:19,650 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:19] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:41:19,653 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:19] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:41:21,786 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:21] "POST /api/test-token HTTP/1.1" 200 -
2025-06-30 22:41:23,155 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:41:23,156 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:41:23,156 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:41:23,156 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:23] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:41:23,157 - discord.client - INFO - logging in using static token
2025-06-30 22:41:24,033 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:24] "GET / HTTP/1.1" 200 -
2025-06-30 22:41:24,070 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:41:24,075 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:24] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:41:24,076 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:24] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:41:24,090 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:24] "GET /socket.io/?EIO=4&transport=polling&t=PV0sq5s HTTP/1.1" 200 -
2025-06-30 22:41:24,092 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:24] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:41:24,107 - web_server - INFO - Client connected
2025-06-30 22:41:24,108 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:24] "POST /socket.io/?EIO=4&transport=polling&t=PV0sq67&sid=2qPbUtWTEIaPpoXpAAAG HTTP/1.1" 200 -
2025-06-30 22:41:24,110 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:24] "GET /socket.io/?EIO=4&transport=polling&t=PV0sq68&sid=2qPbUtWTEIaPpoXpAAAG HTTP/1.1" 200 -
2025-06-30 22:41:24,117 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:41:24] "GET /socket.io/?EIO=4&transport=polling&t=PV0sq6I&sid=2qPbUtWTEIaPpoXpAAAG HTTP/1.1" 200 -
2025-06-30 22:46:05,053 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:46:05,053 - __main__ - INFO - Initializing database...
2025-06-30 22:46:05,055 - __main__ - INFO - Database initialized
2025-06-30 22:46:05,055 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:46:05,055 - __main__ - INFO - Initializing web server...
2025-06-30 22:46:05,099 - __main__ - INFO - Web server initialized
2025-06-30 22:46:05,099 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:46:05,099 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:46:05,099 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:46:05,125 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:46:05,125 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:46:05,125 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:46:06,243 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:46:06,243 - __main__ - INFO - Initializing database...
2025-06-30 22:46:06,246 - __main__ - INFO - Database initialized
2025-06-30 22:46:06,247 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:46:06,247 - __main__ - INFO - Initializing web server...
2025-06-30 22:46:06,296 - __main__ - INFO - Web server initialized
2025-06-30 22:46:06,296 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:46:06,296 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:46:06,296 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:46:06,305 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:46:06,322 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:46:07,259 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:07] "GET /socket.io/?EIO=4&transport=polling&t=PV0tvEO HTTP/1.1" 200 -
2025-06-30 22:46:07,264 - web_server - INFO - Client connected
2025-06-30 22:46:07,265 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:07] "POST /socket.io/?EIO=4&transport=polling&t=PV0tvET&sid=39uqLNhIuGVn5rW0AAAA HTTP/1.1" 200 -
2025-06-30 22:46:07,266 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:07] "GET /socket.io/?EIO=4&transport=polling&t=PV0tvEU&sid=39uqLNhIuGVn5rW0AAAA HTTP/1.1" 200 -
2025-06-30 22:46:07,273 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:07] "GET /socket.io/?EIO=4&transport=polling&t=PV0tvEb&sid=39uqLNhIuGVn5rW0AAAA HTTP/1.1" 200 -
2025-06-30 22:46:11,975 - web_server - INFO - Client disconnected
2025-06-30 22:46:11,975 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:11] "GET /socket.io/?EIO=4&transport=websocket&sid=39uqLNhIuGVn5rW0AAAA HTTP/1.1" 200 -
2025-06-30 22:46:11,985 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:11] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:46:11,999 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:11] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:46:12,100 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:46:12,102 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:12] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:46:14,239 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 22:46:14,239 - discord_api - INFO - Starting full data refresh...
2025-06-30 22:46:16,154 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:46:18,588 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 22:46:19,567 - discord_api - ERROR - Failed to get messages from channel 617069298980093966: 403
2025-06-30 22:46:20,625 - discord_api - ERROR - Failed to get messages from channel 617119519332237374: 403
2025-06-30 22:46:21,880 - discord_api - INFO - Loaded 3 messages from channel 617285524687683585
2025-06-30 22:46:23,325 - discord_api - ERROR - Failed to get messages from channel 617290334250205197: 403
2025-06-30 22:46:24,491 - discord_api - ERROR - Failed to get messages from channel 632965173979774976: 403
2025-06-30 22:46:25,573 - discord_api - ERROR - Failed to get messages from channel 639080342782869504: 403
2025-06-30 22:46:26,993 - discord_api - INFO - Loaded 20 messages from channel 639791489446182965
2025-06-30 22:46:28,102 - discord_api - ERROR - Failed to get messages from channel 640595135939215420: 403
2025-06-30 22:46:29,409 - discord_api - ERROR - Failed to get messages from channel 640810293899034635: 403
2025-06-30 22:46:30,608 - discord_api - ERROR - Failed to get messages from channel 646777141916073984: 403
2025-06-30 22:46:32,418 - discord_api - ERROR - Failed to get messages from channel 647839560633417728: 403
2025-06-30 22:46:33,677 - discord_api - ERROR - Failed to get messages from channel 648432605745905674: 403
2025-06-30 22:46:34,724 - discord_api - ERROR - Failed to get messages from channel 775403459003809804: 403
2025-06-30 22:46:37,623 - discord_api - ERROR - Failed to get messages from channel 779113597506158632: 403
2025-06-30 22:46:40,381 - discord_api - ERROR - Failed to get messages from channel 779708120494506004: 403
2025-06-30 22:46:41,589 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:41] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:46:41,661 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:41] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:46:41,662 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:41] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:46:41,730 - discord_api - ERROR - Failed to get messages from channel 815659596373819422: 403
2025-06-30 22:46:43,498 - discord_api - INFO - Loaded 20 messages from channel 815691774651531325
2025-06-30 22:46:43,502 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 22:46:43,502 - discord_api - INFO - Starting full data refresh...
2025-06-30 22:46:44,558 - discord_api - ERROR - Failed to get messages from channel 818088363738791957: 403
2025-06-30 22:46:45,430 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:46:45,801 - discord_api - ERROR - Failed to get messages from channel 821444292786389073: 403
2025-06-30 22:46:46,925 - discord_api - ERROR - Failed to get messages from channel 831294590791188530: 403
2025-06-30 22:46:47,994 - discord_api - ERROR - Failed to get messages from channel 844313584744923166: 403
2025-06-30 22:46:48,485 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 22:46:49,068 - discord_api - ERROR - Failed to get messages from channel 845390402034073640: 403
2025-06-30 22:46:49,188 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:49] "GET / HTTP/1.1" 200 -
2025-06-30 22:46:49,258 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:49] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:46:49,260 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:49] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:46:49,277 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0u3Ut HTTP/1.1" 200 -
2025-06-30 22:46:49,278 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:49] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:46:49,299 - web_server - INFO - Client connected
2025-06-30 22:46:49,299 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:49] "POST /socket.io/?EIO=4&transport=polling&t=PV0u3V7&sid=SbhYCKt28L_OZ6VHAAAC HTTP/1.1" 200 -
2025-06-30 22:46:49,303 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0u3V9&sid=SbhYCKt28L_OZ6VHAAAC HTTP/1.1" 200 -
2025-06-30 22:46:49,309 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0u3VQ&sid=SbhYCKt28L_OZ6VHAAAC HTTP/1.1" 200 -
2025-06-30 22:46:49,392 - discord_api - ERROR - Failed to get messages from channel 617069298980093966: 403
2025-06-30 22:46:50,217 - discord_api - ERROR - Failed to get messages from channel 846098229082456086: 403
2025-06-30 22:46:50,854 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:46:50] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 22:46:52,058 - discord_api - ERROR - Failed to get messages from channel 617119519332237374: 403
2025-06-30 22:46:52,570 - discord_api - ERROR - Failed to get messages from channel 848918881564229643: 403
2025-06-30 22:46:53,209 - discord_api - INFO - Loaded 3 messages from channel 617285524687683585
2025-06-30 22:46:53,608 - discord_api - ERROR - Failed to get messages from channel 855114118390349834: 403
2025-06-30 22:46:54,327 - discord_api - ERROR - Failed to get messages from channel 617290334250205197: 403
2025-06-30 22:46:54,676 - discord_api - ERROR - Failed to get messages from channel 856559720391639080: 403
2025-06-30 22:46:55,409 - discord_api - ERROR - Failed to get messages from channel 632965173979774976: 403
2025-06-30 22:46:56,566 - discord_api - ERROR - Failed to get messages from channel 639080342782869504: 403
2025-06-30 22:46:56,596 - discord_api - ERROR - Failed to get messages from channel 864585339212791809: 403
2025-06-30 22:46:57,718 - discord_api - ERROR - Failed to get messages from channel 870988403561340948: 403
2025-06-30 22:46:57,984 - discord_api - INFO - Loaded 20 messages from channel 639791489446182965
2025-06-30 22:46:59,027 - discord_api - ERROR - Failed to get messages from channel 640595135939215420: 403
2025-06-30 22:46:59,364 - discord_api - ERROR - Failed to get messages from channel 873218339315515392: 403
2025-06-30 22:47:00,064 - discord_api - ERROR - Failed to get messages from channel 640810293899034635: 403
2025-06-30 22:47:00,400 - discord_api - ERROR - Failed to get messages from channel 874765869123796992: 403
2025-06-30 22:47:01,127 - discord_api - ERROR - Failed to get messages from channel 646777141916073984: 403
2025-06-30 22:47:01,621 - discord_api - ERROR - Failed to get messages from channel 881511437653147718: 403
2025-06-30 22:47:02,236 - discord_api - ERROR - Failed to get messages from channel 647839560633417728: 403
2025-06-30 22:47:02,681 - discord_api - ERROR - Failed to get messages from channel 891599705883148328: 403
2025-06-30 22:47:03,265 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:03] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 22:47:04,551 - discord_api - ERROR - Failed to get messages from channel 648432605745905674: 403
2025-06-30 22:47:05,130 - discord_api - ERROR - Failed to get messages from channel 893881654563700777: 403
2025-06-30 22:47:05,457 - web_server - INFO - Client disconnected
2025-06-30 22:47:05,458 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:05] "GET /socket.io/?EIO=4&transport=websocket&sid=SbhYCKt28L_OZ6VHAAAC HTTP/1.1" 200 -
2025-06-30 22:47:05,462 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:05] "GET / HTTP/1.1" 200 -
2025-06-30 22:47:05,501 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:05] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:47:05,505 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:05] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:47:05,516 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:05] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:47:05,516 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0u7Se HTTP/1.1" 200 -
2025-06-30 22:47:05,530 - web_server - INFO - Client connected
2025-06-30 22:47:05,530 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:05] "POST /socket.io/?EIO=4&transport=polling&t=PV0u7Sn&sid=8R_oGxomF699b2JvAAAE HTTP/1.1" 200 -
2025-06-30 22:47:05,533 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0u7So&sid=8R_oGxomF699b2JvAAAE HTTP/1.1" 200 -
2025-06-30 22:47:05,539 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0u7T0&sid=8R_oGxomF699b2JvAAAE HTTP/1.1" 200 -
2025-06-30 22:47:06,232 - discord_api - ERROR - Failed to get messages from channel 775403459003809804: 403
2025-06-30 22:47:07,297 - discord_api - ERROR - Failed to get messages from channel 779113597506158632: 403
2025-06-30 22:47:07,884 - discord_api - ERROR - Failed to get messages from channel 893882041609900082: 403
2025-06-30 22:47:08,352 - discord_api - ERROR - Failed to get messages from channel 779708120494506004: 403
2025-06-30 22:47:09,028 - discord_api - ERROR - Failed to get messages from channel 894260269038047243: 403
2025-06-30 22:47:09,358 - discord_api - ERROR - Failed to get messages from channel 815659596373819422: 403
2025-06-30 22:47:10,972 - discord_api - INFO - Loaded 20 messages from channel 815691774651531325
2025-06-30 22:47:11,401 - discord_api - ERROR - Failed to get messages from channel 896089253673590854: 403
2025-06-30 22:47:11,972 - discord_api - ERROR - Failed to get messages from channel 818088363738791957: 403
2025-06-30 22:47:12,791 - discord_api - INFO - Loaded 20 messages from channel 896296485174509618
2025-06-30 22:47:13,022 - discord_api - ERROR - Failed to get messages from channel 821444292786389073: 403
2025-06-30 22:47:13,921 - discord_api - INFO - Loaded 5 messages from channel 896296733796073542
2025-06-30 22:47:14,263 - discord_api - ERROR - Failed to get messages from channel 831294590791188530: 403
2025-06-30 22:47:15,593 - discord_api - ERROR - Failed to get messages from channel 899539567374368798: 403
2025-06-30 22:47:15,980 - discord_api - ERROR - Failed to get messages from channel 844313584744923166: 403
2025-06-30 22:47:17,039 - discord_api - ERROR - Failed to get messages from channel 845390402034073640: 403
2025-06-30 22:47:17,467 - discord_api - ERROR - Failed to get messages from channel 924991192272351242: 403
2025-06-30 22:47:18,493 - discord_api - ERROR - Failed to get messages from channel 929752697928839249: 403
2025-06-30 22:47:19,534 - discord_api - ERROR - Failed to get messages from channel 930509719905390622: 403
2025-06-30 22:47:19,864 - discord_api - ERROR - Failed to get messages from channel 846098229082456086: 403
2025-06-30 22:47:20,119 - web_server - INFO - Client disconnected
2025-06-30 22:47:20,119 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:20] "GET /socket.io/?EIO=4&transport=websocket&sid=8R_oGxomF699b2JvAAAE HTTP/1.1" 200 -
2025-06-30 22:47:20,535 - discord_api - ERROR - Failed to get messages from channel 947126211287588864: 403
2025-06-30 22:47:20,950 - discord_api - ERROR - Failed to get messages from channel 848918881564229643: 403
2025-06-30 22:47:21,564 - discord_api - ERROR - Failed to get messages from channel 955889242326835220: 403
2025-06-30 22:47:22,164 - discord_api - ERROR - Failed to get messages from channel 855114118390349834: 403
2025-06-30 22:47:22,705 - discord_api - INFO - Loaded 3 messages from channel 995268606751408188
2025-06-30 22:47:23,180 - discord_api - ERROR - Failed to get messages from channel 856559720391639080: 403
2025-06-30 22:47:23,987 - discord_api - ERROR - Failed to get messages from channel 995686632348983397: 403
2025-06-30 22:47:24,177 - discord_api - ERROR - Failed to get messages from channel 864585339212791809: 403
2025-06-30 22:47:25,060 - discord_api - ERROR - Failed to get messages from channel 997531351056134184: 403
2025-06-30 22:47:25,223 - discord_api - ERROR - Failed to get messages from channel 870988403561340948: 403
2025-06-30 22:47:26,138 - discord_api - ERROR - Failed to get messages from channel 1017045912008282233: 403
2025-06-30 22:47:26,295 - discord_api - ERROR - Failed to get messages from channel 873218339315515392: 403
2025-06-30 22:47:27,195 - discord_api - ERROR - Failed to get messages from channel 1049390629332988005: 403
2025-06-30 22:47:28,306 - discord_api - ERROR - Failed to get messages from channel 1070655304284508221: 403
2025-06-30 22:47:29,445 - discord_api - ERROR - Failed to get messages from channel 1070655653460320266: 403
2025-06-30 22:47:29,552 - discord_api - ERROR - Failed to get messages from channel 874765869123796992: 403
2025-06-30 22:47:30,577 - discord_api - ERROR - Failed to get messages from channel 881511437653147718: 403
2025-06-30 22:47:30,592 - discord_api - ERROR - Failed to get messages from channel 1074043101452189786: 403
2025-06-30 22:47:31,597 - discord_api - ERROR - Failed to get messages from channel 1075159045918441623: 403
2025-06-30 22:47:32,350 - discord_api - ERROR - Failed to get messages from channel 891599705883148328: 403
2025-06-30 22:47:32,636 - discord_api - ERROR - Failed to get messages from channel 1080185612440698900: 403
2025-06-30 22:47:33,453 - discord_api - ERROR - Failed to get messages from channel 893881654563700777: 403
2025-06-30 22:47:34,039 - discord_api - ERROR - Failed to get messages from channel 1084458119477862493: 403
2025-06-30 22:47:34,472 - discord_api - ERROR - Failed to get messages from channel 893882041609900082: 403
2025-06-30 22:47:35,392 - discord_api - INFO - Loaded 20 messages from channel 1091751813444812943
2025-06-30 22:47:35,578 - discord_api - ERROR - Failed to get messages from channel 894260269038047243: 403
2025-06-30 22:47:36,667 - discord_api - ERROR - Failed to get messages from channel 896089253673590854: 403
2025-06-30 22:47:36,736 - discord_api - ERROR - Failed to get messages from channel 1105084570900119584: 403
2025-06-30 22:47:38,022 - discord_api - INFO - Loaded 20 messages from channel 896296485174509618
2025-06-30 22:47:39,241 - discord_api - ERROR - Failed to get messages from channel 1114911642929610782: 403
2025-06-30 22:47:39,328 - discord_api - INFO - Loaded 5 messages from channel 896296733796073542
2025-06-30 22:47:40,543 - discord_api - INFO - Loaded 20 messages from channel 1114932463177826395
2025-06-30 22:47:40,639 - discord_api - ERROR - Failed to get messages from channel 899539567374368798: 403
2025-06-30 22:47:41,597 - discord_api - ERROR - Failed to get messages from channel 1162831042403635211: 403
2025-06-30 22:47:41,947 - discord_api - ERROR - Failed to get messages from channel 924991192272351242: 403
2025-06-30 22:47:42,996 - discord_api - ERROR - Failed to get messages from channel 1166362116811403385: 403
2025-06-30 22:47:43,014 - discord_api - ERROR - Failed to get messages from channel 929752697928839249: 403
2025-06-30 22:47:44,045 - discord_api - ERROR - Failed to get messages from channel 1168994232510451772: 403
2025-06-30 22:47:44,052 - discord_api - ERROR - Failed to get messages from channel 930509719905390622: 403
2025-06-30 22:47:45,117 - discord_api - ERROR - Failed to get messages from channel 947126211287588864: 403
2025-06-30 22:47:45,121 - discord_api - ERROR - Failed to get messages from channel 1171481939119714305: 403
2025-06-30 22:47:46,142 - discord_api - ERROR - Failed to get messages from channel 955889242326835220: 403
2025-06-30 22:47:46,161 - discord_api - ERROR - Failed to get messages from channel 1188243889438605423: 403
2025-06-30 22:47:47,238 - discord_api - INFO - Loaded 3 messages from channel 995268606751408188
2025-06-30 22:47:47,265 - discord_api - ERROR - Failed to get messages from channel 1190690611498602557: 403
2025-06-30 22:47:48,299 - discord_api - ERROR - Failed to get messages from channel 1202997971726377060: 403
2025-06-30 22:47:48,317 - discord_api - ERROR - Failed to get messages from channel 995686632348983397: 403
2025-06-30 22:47:49,387 - discord_api - ERROR - Failed to get messages from channel 1223017990770131005: 403
2025-06-30 22:47:50,427 - discord_api - ERROR - Failed to get messages from channel 1225235244739989625: 403
2025-06-30 22:47:50,483 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:50] "GET /socket.io/?EIO=4&transport=polling&t=PV0uIRE HTTP/1.1" 200 -
2025-06-30 22:47:50,506 - web_server - INFO - Client connected
2025-06-30 22:47:50,507 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:50] "POST /socket.io/?EIO=4&transport=polling&t=PV0uIRU&sid=erodPyYXvERNqsJDAAAG HTTP/1.1" 200 -
2025-06-30 22:47:50,508 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:50] "GET /socket.io/?EIO=4&transport=polling&t=PV0uIRV&sid=erodPyYXvERNqsJDAAAG HTTP/1.1" 200 -
2025-06-30 22:47:50,516 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:50] "GET /socket.io/?EIO=4&transport=polling&t=PV0uIRo&sid=erodPyYXvERNqsJDAAAG HTTP/1.1" 200 -
2025-06-30 22:47:51,021 - discord_api - ERROR - Failed to get messages from channel 997531351056134184: 403
2025-06-30 22:47:51,497 - discord_api - ERROR - Failed to get messages from channel 1231282888163725322: 403
2025-06-30 22:47:52,020 - discord_api - ERROR - Failed to get messages from channel 1017045912008282233: 403
2025-06-30 22:47:52,608 - discord_api - ERROR - Failed to get messages from channel 1232359606408188011: 403
2025-06-30 22:47:52,634 - web_server - INFO - Client disconnected
2025-06-30 22:47:52,634 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:52] "GET /socket.io/?EIO=4&transport=websocket&sid=erodPyYXvERNqsJDAAAG HTTP/1.1" 200 -
2025-06-30 22:47:52,646 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:52] "GET / HTTP/1.1" 200 -
2025-06-30 22:47:52,678 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:52] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:47:52,679 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:52] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:47:52,695 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:52] "GET /socket.io/?EIO=4&transport=polling&t=PV0uIzm HTTP/1.1" 200 -
2025-06-30 22:47:52,705 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:52] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:47:52,711 - web_server - INFO - Client connected
2025-06-30 22:47:52,711 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:52] "POST /socket.io/?EIO=4&transport=polling&t=PV0uI-1&sid=aFGj8mCMIU7Q032PAAAI HTTP/1.1" 200 -
2025-06-30 22:47:52,716 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:52] "GET /socket.io/?EIO=4&transport=polling&t=PV0uI-3&sid=aFGj8mCMIU7Q032PAAAI HTTP/1.1" 200 -
2025-06-30 22:47:52,722 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:52] "GET /socket.io/?EIO=4&transport=polling&t=PV0uI-F&sid=aFGj8mCMIU7Q032PAAAI HTTP/1.1" 200 -
2025-06-30 22:47:53,546 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:53] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 22:47:53,601 - discord_api - ERROR - Failed to get messages from channel 1234237373177401414: 403
2025-06-30 22:47:53,757 - discord_api - ERROR - Failed to get messages from channel 1049390629332988005: 403
2025-06-30 22:47:54,844 - discord_api - ERROR - Failed to get messages from channel 1070655304284508221: 403
2025-06-30 22:47:55,900 - discord_api - ERROR - Failed to get messages from channel 1070655653460320266: 403
2025-06-30 22:47:56,279 - discord_api - ERROR - Failed to get messages from channel 1240197590947987467: 403
2025-06-30 22:47:56,496 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:47:56] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 22:47:56,989 - discord_api - ERROR - Failed to get messages from channel 1074043101452189786: 403
2025-06-30 22:47:57,378 - discord_api - ERROR - Failed to get messages from channel 1261431897846710272: 403
2025-06-30 22:47:58,033 - discord_api - ERROR - Failed to get messages from channel 1075159045918441623: 403
2025-06-30 22:47:58,449 - discord_api - ERROR - Failed to get messages from channel 1261777575873875969: 403
2025-06-30 22:47:59,178 - discord_api - ERROR - Failed to get messages from channel 1080185612440698900: 403
2025-06-30 22:47:59,515 - discord_api - ERROR - Failed to get messages from channel 1266441704119079046: 403
2025-06-30 22:48:00,286 - discord_api - ERROR - Failed to get messages from channel 1084458119477862493: 403
2025-06-30 22:48:00,560 - discord_api - ERROR - Failed to get messages from channel 1282831895721152554: 403
2025-06-30 22:48:01,601 - discord_api - ERROR - Failed to get messages from channel 1283780891671269522: 403
2025-06-30 22:48:01,717 - discord_api - INFO - Loaded 20 messages from channel 1091751813444812943
2025-06-30 22:48:02,643 - discord_api - ERROR - Failed to get messages from channel 1287023470051459093: 403
2025-06-30 22:48:02,723 - discord_api - ERROR - Failed to get messages from channel 1105084570900119584: 403
2025-06-30 22:48:03,665 - discord_api - ERROR - Failed to get messages from channel 1287360430997770311: 403
2025-06-30 22:48:03,754 - discord_api - ERROR - Failed to get messages from channel 1114911642929610782: 403
2025-06-30 22:48:04,743 - discord_api - ERROR - Failed to get messages from channel 1289895941339611206: 403
2025-06-30 22:48:05,221 - discord_api - INFO - Loaded 20 messages from channel 1114932463177826395
2025-06-30 22:48:05,761 - discord_api - ERROR - Failed to get messages from channel 1295446338166853734: 403
2025-06-30 22:48:06,409 - discord_api - ERROR - Failed to get messages from channel 1162831042403635211: 403
2025-06-30 22:48:06,850 - discord_api - ERROR - Failed to get messages from channel 1312717104985935903: 403
2025-06-30 22:48:07,422 - web_server - INFO - Client disconnected
2025-06-30 22:48:07,422 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:48:07] "GET /socket.io/?EIO=4&transport=websocket&sid=aFGj8mCMIU7Q032PAAAI HTTP/1.1" 200 -
2025-06-30 22:48:07,433 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:48:07] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:48:07,465 - discord_api - ERROR - Failed to get messages from channel 1166362116811403385: 403
2025-06-30 22:48:07,478 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:48:07] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:48:07,479 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:48:07] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:48:07,946 - discord_api - ERROR - Failed to get messages from channel 1316466225660035184: 403
2025-06-30 22:48:08,431 - discord_api - ERROR - Failed to get messages from channel 1168994232510451772: 403
2025-06-30 22:48:09,038 - discord_api - ERROR - Failed to get messages from channel 1316498690478833735: 403
2025-06-30 22:48:09,440 - discord_api - ERROR - Failed to get messages from channel 1171481939119714305: 403
2025-06-30 22:48:09,958 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 22:48:09,958 - discord_api - INFO - Starting full data refresh...
2025-06-30 22:48:10,137 - discord_api - ERROR - Failed to get messages from channel 1317467918715326524: 403
2025-06-30 22:48:10,564 - discord_api - ERROR - Failed to get messages from channel 1188243889438605423: 403
2025-06-30 22:48:11,184 - discord_api - ERROR - Failed to get messages from channel 1318280818401087588: 403
2025-06-30 22:48:11,588 - discord_api - ERROR - Failed to get messages from channel 1190690611498602557: 403
2025-06-30 22:48:12,314 - discord_api - ERROR - Failed to get messages from channel 1318281707857645699: 403
2025-06-30 22:48:12,660 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:48:12,683 - discord_api - ERROR - Failed to get messages from channel 1202997971726377060: 403
2025-06-30 22:48:13,339 - discord_api - ERROR - Failed to get messages from channel 1318284034496794715: 403
2025-06-30 22:48:13,714 - discord_api - ERROR - Failed to get messages from channel 1223017990770131005: 403
2025-06-30 22:48:14,352 - discord_api - ERROR - Failed to get messages from channel 1318287273061646366: 403
2025-06-30 22:48:14,715 - discord_api - ERROR - Failed to get messages from channel 1225235244739989625: 403
2025-06-30 22:48:15,201 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 22:48:15,505 - discord_api - ERROR - Failed to get messages from channel 1318347119127429241: 403
2025-06-30 22:48:16,197 - discord_api - ERROR - Failed to get messages from channel 617069298980093966: 403
2025-06-30 22:48:16,439 - discord_api - ERROR - Failed to get messages from channel 1231282888163725322: 403
2025-06-30 22:48:16,521 - discord_api - ERROR - Failed to get messages from channel 1318649389035687967: 403
2025-06-30 22:48:17,328 - discord_api - ERROR - Failed to get messages from channel 617119519332237374: 403
2025-06-30 22:48:17,533 - discord_api - ERROR - Failed to get messages from channel 1232359606408188011: 403
2025-06-30 22:48:17,541 - discord_api - ERROR - Failed to get messages from channel 1325481040017490013: 403
2025-06-30 22:48:18,468 - discord_api - INFO - Loaded 3 messages from channel 617285524687683585
2025-06-30 22:48:18,548 - discord_api - ERROR - Failed to get messages from channel 1234237373177401414: 403
2025-06-30 22:48:18,873 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:48:18] "POST /api/test-token HTTP/1.1" 200 -
2025-06-30 22:48:18,892 - discord_api - ERROR - Failed to get messages from channel 1344020417420984430: 403
2025-06-30 22:48:19,521 - discord_api - ERROR - Failed to get messages from channel 617290334250205197: 403
2025-06-30 22:48:19,548 - discord_api - ERROR - Failed to get messages from channel 1240197590947987467: 403
2025-06-30 22:48:19,935 - discord_api - ERROR - Failed to get messages from channel 1344020489114226760: 403
2025-06-30 22:48:20,564 - discord_api - ERROR - Failed to get messages from channel 632965173979774976: 403
2025-06-30 22:48:20,641 - discord_api - ERROR - Failed to get messages from channel 1261431897846710272: 403
2025-06-30 22:48:21,061 - discord_api - ERROR - Failed to get messages from channel 1356283798727164116: 403
2025-06-30 22:48:21,571 - discord_api - ERROR - Failed to get messages from channel 639080342782869504: 403
2025-06-30 22:48:21,672 - discord_api - ERROR - Failed to get messages from channel 1261777575873875969: 403
2025-06-30 22:48:22,157 - discord_api - ERROR - Failed to get messages from channel 1358776632223203488: 403
2025-06-30 22:48:22,741 - discord_api - ERROR - Failed to get messages from channel 1266441704119079046: 403
2025-06-30 22:48:22,892 - discord_api - INFO - Loaded 20 messages from channel 639791489446182965
2025-06-30 22:48:23,258 - discord_api - ERROR - Failed to get messages from channel 1359537422672724322: 403
2025-06-30 22:48:23,837 - discord_api - ERROR - Failed to get messages from channel 1282831895721152554: 403
2025-06-30 22:48:24,080 - discord_api - ERROR - Failed to get messages from channel 640595135939215420: 403
2025-06-30 22:48:24,521 - discord_api - ERROR - Failed to get messages from channel 1364224824263577741: 403
2025-06-30 22:48:24,875 - discord_api - ERROR - Failed to get messages from channel 1283780891671269522: 403
2025-06-30 22:48:25,161 - discord_api - ERROR - Failed to get messages from channel 640810293899034635: 403
2025-06-30 22:48:25,586 - discord_api - ERROR - Failed to get messages from channel 1366059304368734279: 403
2025-06-30 22:48:26,250 - discord_api - ERROR - Failed to get messages from channel 646777141916073984: 403
2025-06-30 22:48:26,326 - discord_api - ERROR - Failed to get messages from channel 1287023470051459093: 403
2025-06-30 22:48:26,695 - discord_api - ERROR - Failed to get messages from channel 1372851290555809833: 403
2025-06-30 22:48:27,309 - discord_api - ERROR - Failed to get messages from channel 647839560633417728: 403
2025-06-30 22:48:27,418 - discord_api - ERROR - Failed to get messages from channel 1287360430997770311: 403
2025-06-30 22:48:27,721 - discord_api - ERROR - Failed to get messages from channel 1372851646262022154: 403
2025-06-30 22:48:28,548 - discord_api - ERROR - Failed to get messages from channel 1289895941339611206: 403
2025-06-30 22:48:28,612 - discord_api - ERROR - Failed to get messages from channel 648432605745905674: 403
2025-06-30 22:48:28,813 - discord_api - ERROR - Failed to get messages from channel 1380967934888906752: 403
2025-06-30 22:48:29,597 - discord_api - ERROR - Failed to get messages from channel 1295446338166853734: 403
2025-06-30 22:48:29,671 - discord_api - ERROR - Failed to get messages from channel 775403459003809804: 403
2025-06-30 22:48:29,800 - discord_api - ERROR - Failed to get messages from channel 1380970992368619520: 403
2025-06-30 22:48:30,713 - discord_api - ERROR - Failed to get messages from channel 779113597506158632: 403
2025-06-30 22:48:30,777 - discord_api - ERROR - Failed to get messages from channel 1382792371594465280: 403
2025-06-30 22:48:31,415 - discord_api - ERROR - Failed to get messages from channel 1312717104985935903: 403
2025-06-30 22:48:31,720 - discord_api - ERROR - Failed to get messages from channel 779708120494506004: 403
2025-06-30 22:48:32,465 - discord_api - ERROR - Failed to get messages from channel 1384967872756842576: 403
2025-06-30 22:48:32,655 - discord_api - ERROR - Failed to get messages from channel 1316466225660035184: 403
2025-06-30 22:48:33,546 - discord_api - ERROR - Failed to get messages from channel 1385521958883426354: 403
2025-06-30 22:48:33,696 - discord_api - ERROR - Failed to get messages from channel 815659596373819422: 403
2025-06-30 22:48:33,943 - discord_api - ERROR - Failed to get messages from channel 1316498690478833735: 403
2025-06-30 22:48:34,697 - discord_api - ERROR - Failed to get messages from channel 1385697717321203917: 403
2025-06-30 22:48:35,013 - discord_api - ERROR - Failed to get messages from channel 1317467918715326524: 403
2025-06-30 22:48:35,068 - discord_api - INFO - Loaded 20 messages from channel 815691774651531325
2025-06-30 22:48:35,750 - discord_api - ERROR - Failed to get messages from channel 1386021759379312692: 403
2025-06-30 22:48:36,090 - discord_api - ERROR - Failed to get messages from channel 1318280818401087588: 403
2025-06-30 22:48:36,379 - discord_api - ERROR - Failed to get messages from channel 818088363738791957: 403
2025-06-30 22:48:36,807 - discord_api - ERROR - Failed to get messages from channel 1386420928665354320: 403
2025-06-30 22:48:37,209 - discord_api - ERROR - Failed to get messages from channel 1318281707857645699: 403
2025-06-30 22:48:37,436 - discord_api - ERROR - Failed to get messages from channel 821444292786389073: 403
2025-06-30 22:48:37,860 - discord_api - ERROR - Failed to get messages from channel 1386577945103175700: 403
2025-06-30 22:48:38,247 - discord_api - ERROR - Failed to get messages from channel 1318284034496794715: 403
2025-06-30 22:48:38,488 - discord_api - ERROR - Failed to get messages from channel 831294590791188530: 403
2025-06-30 22:48:38,939 - discord_api - ERROR - Failed to get messages from channel 1387568020750209125: 403
2025-06-30 22:48:39,294 - discord_api - ERROR - Failed to get messages from channel 1318287273061646366: 403
2025-06-30 22:48:39,516 - discord_api - ERROR - Failed to get messages from channel 844313584744923166: 403
2025-06-30 22:48:39,964 - discord_api - ERROR - Failed to get messages from channel 1387751479342927982: 403
2025-06-30 22:48:40,324 - discord_api - ERROR - Failed to get messages from channel 1318347119127429241: 403
2025-06-30 22:48:40,525 - discord_api - ERROR - Failed to get messages from channel 845390402034073640: 403
2025-06-30 22:48:40,998 - discord_api - ERROR - Failed to get messages from channel 1387791410081828975: 403
2025-06-30 22:48:41,363 - discord_api - ERROR - Failed to get messages from channel 1318649389035687967: 403
2025-06-30 22:48:41,625 - discord_api - ERROR - Failed to get messages from channel 846098229082456086: 403
2025-06-30 22:48:42,104 - discord_api - ERROR - Failed to get messages from channel 1388139264000397444: 403
2025-06-30 22:48:42,477 - discord_api - ERROR - Failed to get messages from channel 1325481040017490013: 403
2025-06-30 22:48:42,760 - discord_api - ERROR - Failed to get messages from channel 848918881564229643: 403
2025-06-30 22:48:43,103 - discord_api - ERROR - Failed to get messages from channel 1388299352690262199: 403
2025-06-30 22:48:43,544 - discord_api - ERROR - Failed to get messages from channel 1344020417420984430: 403
2025-06-30 22:48:43,821 - discord_api - ERROR - Failed to get messages from channel 855114118390349834: 403
2025-06-30 22:48:44,148 - discord_api - ERROR - Failed to get messages from channel 1388548111831404554: 403
2025-06-30 22:48:44,551 - discord_api - ERROR - Failed to get messages from channel 1344020489114226760: 403
2025-06-30 22:48:44,823 - discord_api - ERROR - Failed to get messages from channel 856559720391639080: 403
2025-06-30 22:48:45,270 - discord_api - ERROR - Failed to get messages from channel 1388564551275909191: 403
2025-06-30 22:48:45,550 - discord_api - ERROR - Failed to get messages from channel 1356283798727164116: 403
2025-06-30 22:48:45,866 - discord_api - ERROR - Failed to get messages from channel 864585339212791809: 403
2025-06-30 22:48:46,366 - discord_api - ERROR - Failed to get messages from channel 1388603766370074634: 403
2025-06-30 22:48:46,632 - discord_api - ERROR - Failed to get messages from channel 1358776632223203488: 403
2025-06-30 22:48:46,909 - discord_api - ERROR - Failed to get messages from channel 870988403561340948: 403
2025-06-30 22:48:47,617 - discord_api - ERROR - Failed to get messages from channel 1359537422672724322: 403
2025-06-30 22:48:48,947 - discord_api - ERROR - Failed to get messages from channel 1364224824263577741: 403
2025-06-30 22:48:49,964 - discord_api - INFO - Loaded 185 channels for guild 979483208339062785
2025-06-30 22:48:49,967 - discord_api - ERROR - Failed to get messages from channel 873218339315515392: 403
2025-06-30 22:48:49,975 - discord_api - ERROR - Failed to get messages from channel 1366059304368734279: 403
2025-06-30 22:48:50,937 - discord_api - INFO - Loaded 2 messages from channel 992539785379991602
2025-06-30 22:48:51,037 - discord_api - ERROR - Failed to get messages from channel 1372851290555809833: 403
2025-06-30 22:48:51,073 - discord_api - ERROR - Failed to get messages from channel 874765869123796992: 403
2025-06-30 22:48:52,048 - discord_api - ERROR - Failed to get messages from channel 1372851646262022154: 403
2025-06-30 22:48:52,088 - discord_api - ERROR - Failed to get messages from channel 881511437653147718: 403
2025-06-30 22:48:52,184 - discord_api - INFO - Loaded 20 messages from channel 1038633681218174996
2025-06-30 22:48:53,152 - discord_api - ERROR - Failed to get messages from channel 891599705883148328: 403
2025-06-30 22:48:53,596 - discord_api - INFO - Loaded 16 messages from channel 1038636854578253884
2025-06-30 22:48:54,191 - discord_api - ERROR - Failed to get messages from channel 893881654563700777: 403
2025-06-30 22:48:54,623 - discord_api - ERROR - Failed to get messages from channel 1380967934888906752: 403
2025-06-30 22:48:55,289 - discord_api - ERROR - Failed to get messages from channel 1038843685372170292: 403
2025-06-30 22:48:55,335 - discord_api - ERROR - Failed to get messages from channel 893882041609900082: 403
2025-06-30 22:48:56,045 - discord_api - ERROR - Failed to get messages from channel 1380970992368619520: 403
2025-06-30 22:48:56,404 - discord_api - ERROR - Failed to get messages from channel 894260269038047243: 403
2025-06-30 22:48:56,747 - discord_api - INFO - Loaded 20 messages from channel 1048836212464566323
2025-06-30 22:48:57,199 - discord_api - ERROR - Failed to get messages from channel 1382792371594465280: 403
2025-06-30 22:48:57,437 - discord_api - ERROR - Failed to get messages from channel 896089253673590854: 403
2025-06-30 22:48:57,743 - discord_api - ERROR - Failed to get messages from channel 1050242879349608488: 403
2025-06-30 22:48:58,250 - discord_api - ERROR - Failed to get messages from channel 1384967872756842576: 403
2025-06-30 22:48:58,394 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\config.py', reloading
2025-06-30 22:48:58,651 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:49:00,626 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:49:00,626 - __main__ - INFO - Initializing database...
2025-06-30 22:49:00,627 - __main__ - INFO - Database initialized
2025-06-30 22:49:00,627 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:49:00,627 - __main__ - INFO - Initializing web server...
2025-06-30 22:49:00,671 - __main__ - INFO - Web server initialized
2025-06-30 22:49:00,671 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:49:00,671 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:49:00,671 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:49:00,682 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:49:00,701 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:51:29,801 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:51:29,801 - __main__ - INFO - Initializing database...
2025-06-30 22:51:29,802 - __main__ - INFO - Database initialized
2025-06-30 22:51:29,802 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:51:29,803 - __main__ - INFO - Initializing web server...
2025-06-30 22:51:29,846 - __main__ - INFO - Web server initialized
2025-06-30 22:51:29,846 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:51:29,846 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:51:29,846 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:51:29,864 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:51:29,864 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:51:29,864 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:51:30,924 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:51:30,924 - __main__ - INFO - Initializing database...
2025-06-30 22:51:30,925 - __main__ - INFO - Database initialized
2025-06-30 22:51:30,925 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:51:30,926 - __main__ - INFO - Initializing web server...
2025-06-30 22:51:30,972 - __main__ - INFO - Web server initialized
2025-06-30 22:51:30,972 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:51:30,973 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:51:30,973 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:51:30,982 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:51:30,999 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:52:43,293 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:52:43] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:52:43,402 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:52:43] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:52:43,611 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:52:43] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:52:43,613 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:52:43] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 22:52:48,461 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 22:52:48,461 - discord_api - INFO - Starting quick connection...
2025-06-30 22:52:50,339 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:52:52,939 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 22:52:56,354 - discord_api - INFO - Loaded 185 channels for guild 979483208339062785
2025-06-30 22:53:02,216 - discord_api - INFO - Loaded 384 channels for guild 1149420682216747088
2025-06-30 22:53:03,150 - discord_api - INFO - Loaded 2 DM channels
2025-06-30 22:53:03,150 - discord_api - INFO - Quick connection completed: 3 guilds, 709 channels, 2 DMs
2025-06-30 22:53:03,150 - __main__ - INFO - Using quick connect mode (no initial message loading)
2025-06-30 22:53:03,151 - __main__ - INFO - Discord API client restarted successfully
2025-06-30 22:53:03,151 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:53:03] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:53:04,200 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:53:04] "GET / HTTP/1.1" 200 -
2025-06-30 22:53:04,240 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:53:04] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:53:04,242 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:53:04] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:53:04,256 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:53:04] "GET /socket.io/?EIO=4&transport=polling&t=PV0vV1x HTTP/1.1" 200 -
2025-06-30 22:53:04,258 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:53:04] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:53:04,275 - web_server - INFO - Client connected
2025-06-30 22:53:04,276 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:53:04] "POST /socket.io/?EIO=4&transport=polling&t=PV0vV27&sid=kH-iElgKpMz6NkRBAAAA HTTP/1.1" 200 -
2025-06-30 22:53:04,279 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:53:04] "GET /socket.io/?EIO=4&transport=polling&t=PV0vV28&sid=kH-iElgKpMz6NkRBAAAA HTTP/1.1" 200 -
2025-06-30 22:53:04,284 - web_server - INFO - Refresh data request received
2025-06-30 22:53:04,284 - web_server - INFO - Discord API status: ready=True
2025-06-30 22:53:04,284 - discord_api - INFO - Starting full data refresh...
2025-06-30 22:53:04,292 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:53:04] "GET /socket.io/?EIO=4&transport=polling&t=PV0vV2V&sid=kH-iElgKpMz6NkRBAAAA HTTP/1.1" 200 -
2025-06-30 22:53:06,206 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:53:08,892 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 22:53:09,953 - discord_api - ERROR - Failed to get messages from channel 617069298980093966: 403
2025-06-30 22:53:10,933 - discord_api - ERROR - Failed to get messages from channel 617119519332237374: 403
2025-06-30 22:53:12,059 - discord_api - INFO - Loaded 3 messages from channel 617285524687683585
2025-06-30 22:53:13,071 - discord_api - ERROR - Failed to get messages from channel 617290334250205197: 403
2025-06-30 22:53:14,086 - discord_api - ERROR - Failed to get messages from channel 632965173979774976: 403
2025-06-30 22:53:15,085 - discord_api - ERROR - Failed to get messages from channel 639080342782869504: 403
2025-06-30 22:53:16,376 - discord_api - INFO - Loaded 5 messages from channel 639791489446182965
2025-06-30 22:53:17,462 - discord_api - ERROR - Failed to get messages from channel 640595135939215420: 403
2025-06-30 22:53:18,458 - discord_api - ERROR - Failed to get messages from channel 640810293899034635: 403
2025-06-30 22:53:19,514 - discord_api - ERROR - Failed to get messages from channel 646777141916073984: 403
2025-06-30 22:53:21,542 - discord_api - ERROR - Failed to get messages from channel 647839560633417728: 403
2025-06-30 22:53:22,595 - discord_api - ERROR - Failed to get messages from channel 648432605745905674: 403
2025-06-30 22:53:23,671 - discord_api - ERROR - Failed to get messages from channel 775403459003809804: 403
2025-06-30 22:53:24,691 - discord_api - ERROR - Failed to get messages from channel 779113597506158632: 403
2025-06-30 22:53:26,117 - discord_api - ERROR - Failed to get messages from channel 779708120494506004: 403
2025-06-30 22:53:27,115 - discord_api - ERROR - Failed to get messages from channel 815659596373819422: 403
2025-06-30 22:53:28,251 - discord_api - INFO - Loaded 5 messages from channel 815691774651531325
2025-06-30 22:53:29,474 - discord_api - ERROR - Failed to get messages from channel 818088363738791957: 403
2025-06-30 22:53:30,540 - discord_api - ERROR - Failed to get messages from channel 821444292786389073: 403
2025-06-30 22:53:31,529 - discord_api - ERROR - Failed to get messages from channel 831294590791188530: 403
2025-06-30 22:53:32,536 - discord_api - ERROR - Failed to get messages from channel 844313584744923166: 403
2025-06-30 22:53:33,497 - discord_api - ERROR - Failed to get messages from channel 845390402034073640: 403
2025-06-30 22:53:34,512 - discord_api - ERROR - Failed to get messages from channel 846098229082456086: 403
2025-06-30 22:53:35,548 - discord_api - ERROR - Failed to get messages from channel 848918881564229643: 403
2025-06-30 22:53:36,563 - discord_api - ERROR - Failed to get messages from channel 855114118390349834: 403
2025-06-30 22:53:38,692 - discord_api - ERROR - Failed to get messages from channel 856559720391639080: 403
2025-06-30 22:53:40,733 - discord_api - ERROR - Failed to get messages from channel 864585339212791809: 403
2025-06-30 22:53:43,818 - discord_api - ERROR - Failed to get messages from channel 870988403561340948: 403
2025-06-30 22:53:44,821 - discord_api - ERROR - Failed to get messages from channel 873218339315515392: 403
2025-06-30 22:53:45,823 - discord_api - ERROR - Failed to get messages from channel 874765869123796992: 403
2025-06-30 22:53:46,803 - discord_api - ERROR - Failed to get messages from channel 881511437653147718: 403
2025-06-30 22:53:47,784 - discord_api - ERROR - Failed to get messages from channel 891599705883148328: 403
2025-06-30 22:53:48,798 - discord_api - ERROR - Failed to get messages from channel 893881654563700777: 403
2025-06-30 22:53:49,743 - discord_api - ERROR - Failed to get messages from channel 893882041609900082: 403
2025-06-30 22:53:50,947 - discord_api - ERROR - Failed to get messages from channel 894260269038047243: 403
2025-06-30 22:53:51,911 - discord_api - ERROR - Failed to get messages from channel 896089253673590854: 403
2025-06-30 22:53:53,785 - discord_api - INFO - Loaded 5 messages from channel 896296485174509618
2025-06-30 22:53:54,859 - discord_api - INFO - Loaded 5 messages from channel 896296733796073542
2025-06-30 22:53:55,876 - discord_api - ERROR - Failed to get messages from channel 899539567374368798: 403
2025-06-30 22:53:56,938 - discord_api - ERROR - Failed to get messages from channel 924991192272351242: 403
2025-06-30 22:53:57,941 - discord_api - ERROR - Failed to get messages from channel 929752697928839249: 403
2025-06-30 22:53:58,951 - discord_api - ERROR - Failed to get messages from channel 930509719905390622: 403
2025-06-30 22:54:00,024 - discord_api - ERROR - Failed to get messages from channel 947126211287588864: 403
2025-06-30 22:54:00,978 - discord_api - ERROR - Failed to get messages from channel 955889242326835220: 403
2025-06-30 22:54:02,032 - discord_api - INFO - Loaded 3 messages from channel 995268606751408188
2025-06-30 22:54:03,065 - discord_api - ERROR - Failed to get messages from channel 995686632348983397: 403
2025-06-30 22:54:04,029 - discord_api - ERROR - Failed to get messages from channel 997531351056134184: 403
2025-06-30 22:54:07,003 - discord_api - ERROR - Failed to get messages from channel 1017045912008282233: 403
2025-06-30 22:54:08,043 - discord_api - ERROR - Failed to get messages from channel 1049390629332988005: 403
2025-06-30 22:54:09,054 - discord_api - ERROR - Failed to get messages from channel 1070655304284508221: 403
2025-06-30 22:54:12,379 - discord_api - INFO - Loaded 185 channels for guild 979483208339062785
2025-06-30 22:54:13,449 - discord_api - INFO - Loaded 2 messages from channel 992539785379991602
2025-06-30 22:54:14,551 - discord_api - INFO - Loaded 5 messages from channel 1038633681218174996
2025-06-30 22:54:15,616 - discord_api - INFO - Loaded 5 messages from channel 1038636854578253884
2025-06-30 22:54:16,853 - discord_api - ERROR - Failed to get messages from channel 1038843685372170292: 403
2025-06-30 22:54:17,963 - discord_api - INFO - Loaded 5 messages from channel 1048836212464566323
2025-06-30 22:54:18,934 - discord_api - ERROR - Failed to get messages from channel 1050242879349608488: 403
2025-06-30 22:54:19,947 - discord_api - ERROR - Failed to get messages from channel 1050563901646966824: 403
2025-06-30 22:54:20,963 - discord_api - ERROR - Failed to get messages from channel 1050566771716935781: 403
2025-06-30 22:54:21,981 - discord_api - ERROR - Failed to get messages from channel 1053014636238217327: 403
2025-06-30 22:54:23,085 - discord_api - INFO - Loaded 5 messages from channel 1053468248240296037
2025-06-30 22:54:24,106 - discord_api - ERROR - Failed to get messages from channel 1065751932544749619: 403
2025-06-30 22:54:25,165 - discord_api - INFO - Loaded 2 messages from channel 1067216086921138206
2025-06-30 22:54:26,211 - discord_api - INFO - Loaded 2 messages from channel 1071507265468780574
2025-06-30 22:54:27,277 - discord_api - ERROR - Failed to get messages from channel 1072714995005145168: 403
2025-06-30 22:54:29,287 - discord_api - INFO - Loaded 1 messages from channel 1119663927614255164
2025-06-30 22:54:30,271 - discord_api - ERROR - Failed to get messages from channel 1120037509137703043: 403
2025-06-30 22:54:31,285 - discord_api - INFO - Loaded 3 messages from channel 1128031098757185686
2025-06-30 22:54:32,289 - discord_api - ERROR - Failed to get messages from channel 1141914479664320562: 403
2025-06-30 22:54:33,513 - discord_api - INFO - Loaded 1 messages from channel 1141926535666876507
2025-06-30 22:54:34,577 - discord_api - INFO - Loaded 5 messages from channel 1149515366670815242
2025-06-30 22:54:35,545 - discord_api - ERROR - Failed to get messages from channel 1162027970165219368: 403
2025-06-30 22:54:36,601 - discord_api - INFO - Loaded 5 messages from channel 1189622773853732864
2025-06-30 22:54:37,664 - discord_api - INFO - Loaded 5 messages from channel 1189622811250151504
2025-06-30 22:54:38,727 - discord_api - INFO - Loaded 5 messages from channel 1189622872545701998
2025-06-30 22:54:39,969 - discord_api - INFO - Loaded 5 messages from channel 1189623037562196009
2025-06-30 22:54:42,130 - discord_api - INFO - Loaded 5 messages from channel 1189672059794628678
2025-06-30 22:54:43,379 - discord_api - INFO - Loaded 5 messages from channel 1191940874586820698
2025-06-30 22:54:44,432 - discord_api - ERROR - Failed to get messages from channel 1193618486669348914: 403
2025-06-30 22:54:45,415 - discord_api - ERROR - Failed to get messages from channel 1194096199909773442: 403
2025-06-30 22:54:46,522 - discord_api - INFO - Loaded 5 messages from channel 1200591080954019880
2025-06-30 22:54:47,459 - discord_api - ERROR - Failed to get messages from channel 1210790757569662976: 403
2025-06-30 22:54:48,541 - discord_api - INFO - Loaded 5 messages from channel 1227009601816363008
2025-06-30 22:54:49,603 - discord_api - ERROR - Failed to get messages from channel 1240763010767781958: 403
2025-06-30 22:54:50,622 - discord_api - ERROR - Failed to get messages from channel 1247355709943976007: 403
2025-06-30 22:54:53,840 - discord_api - INFO - Loaded 4 messages from channel 1253389305376669706
2025-06-30 22:54:54,919 - discord_api - INFO - Loaded 5 messages from channel 1267537197649756254
2025-06-30 22:54:55,997 - discord_api - INFO - Loaded 5 messages from channel 1267540330119692419
2025-06-30 22:54:57,410 - discord_api - INFO - Loaded 5 messages from channel 1267587467934957734
2025-06-30 22:54:58,691 - discord_api - INFO - Loaded 5 messages from channel 1287421048949575743
2025-06-30 22:54:59,710 - discord_api - ERROR - Failed to get messages from channel 1300264345250435155: 403
2025-06-30 22:55:00,721 - discord_api - ERROR - Failed to get messages from channel 1311838436872683550: 403
2025-06-30 22:55:01,714 - discord_api - ERROR - Failed to get messages from channel 1335336218472484947: 403
2025-06-30 22:55:02,830 - discord_api - INFO - Loaded 5 messages from channel 1344171462818402364
2025-06-30 22:55:03,857 - discord_api - INFO - Loaded 4 messages from channel 1349228868724195380
2025-06-30 22:55:04,795 - discord_api - ERROR - Failed to get messages from channel 1355248546143801355: 403
2025-06-30 22:55:06,042 - discord_api - ERROR - Failed to get messages from channel 1357850808175038669: 403
2025-06-30 22:55:07,084 - discord_api - ERROR - Failed to get messages from channel 1358885539322921303: 403
2025-06-30 22:55:08,114 - discord_api - ERROR - Failed to get messages from channel 1360107887279738921: 403
2025-06-30 22:55:09,206 - discord_api - INFO - Loaded 1 messages from channel 1360700531021971476
2025-06-30 22:55:11,120 - discord_api - ERROR - Failed to get messages from channel 1364732530225582162: 403
2025-06-30 22:55:17,094 - discord_api - INFO - Loaded 384 channels for guild 1149420682216747088
2025-06-30 22:55:18,068 - discord_api - INFO - Loaded 5 messages from channel 1149422149233283153
2025-06-30 22:55:19,124 - discord_api - ERROR - Failed to get messages from channel 1149423090657398865: 403
2025-06-30 22:55:20,128 - discord_api - ERROR - Failed to get messages from channel 1149423366101536859: 403
2025-06-30 22:55:21,071 - discord_api - ERROR - Failed to get messages from channel 1149423443327058003: 403
2025-06-30 22:55:22,040 - discord_api - ERROR - Failed to get messages from channel 1149423570565472298: 403
2025-06-30 22:55:23,272 - discord_api - ERROR - Failed to get messages from channel 1149423658813640824: 403
2025-06-30 22:55:24,247 - discord_api - ERROR - Failed to get messages from channel 1149423752040427532: 403
2025-06-30 22:55:25,271 - discord_api - ERROR - Failed to get messages from channel 1149423815928062094: 403
2025-06-30 22:55:26,256 - discord_api - ERROR - Failed to get messages from channel 1149424066080542760: 403
2025-06-30 22:55:27,378 - discord_api - INFO - Loaded 5 messages from channel 1149424145566801961
2025-06-30 22:55:28,377 - discord_api - ERROR - Failed to get messages from channel 1149424313108279358: 403
2025-06-30 22:55:29,340 - discord_api - ERROR - Failed to get messages from channel 1149424398139392022: 403
2025-06-30 22:55:30,407 - discord_api - ERROR - Failed to get messages from channel 1149424465810292837: 403
2025-06-30 22:55:31,446 - discord_api - ERROR - Failed to get messages from channel 1149424540938674326: 403
2025-06-30 22:55:32,439 - discord_api - ERROR - Failed to get messages from channel 1149424635545391145: 403
2025-06-30 22:55:33,451 - discord_api - ERROR - Failed to get messages from channel 1149424741493506118: 403
2025-06-30 22:55:34,475 - discord_api - INFO - Loaded 1 messages from channel 1149425352939147304
2025-06-30 22:55:35,484 - discord_api - ERROR - Failed to get messages from channel 1149426188801024121: 403
2025-06-30 22:55:36,584 - discord_api - ERROR - Failed to get messages from channel 1149426227417976843: 403
2025-06-30 22:55:37,600 - discord_api - ERROR - Failed to get messages from channel 1149427112315473970: 403
2025-06-30 22:55:38,510 - web_server - INFO - Client disconnected
2025-06-30 22:55:38,510 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:38] "GET /socket.io/?EIO=4&transport=websocket&sid=kH-iElgKpMz6NkRBAAAA HTTP/1.1" 200 -
2025-06-30 22:55:38,522 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:38] "GET / HTTP/1.1" 200 -
2025-06-30 22:55:38,545 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:38] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:55:38,546 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:38] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:55:38,562 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:38] "GET /socket.io/?EIO=4&transport=polling&t=PV0w4i- HTTP/1.1" 200 -
2025-06-30 22:55:38,571 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:38] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:55:38,574 - web_server - INFO - Client connected
2025-06-30 22:55:38,575 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:38] "POST /socket.io/?EIO=4&transport=polling&t=PV0w4j9&sid=7OHZizfLJrGV_FECAAAC HTTP/1.1" 200 -
2025-06-30 22:55:38,576 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:38] "GET /socket.io/?EIO=4&transport=polling&t=PV0w4jA&sid=7OHZizfLJrGV_FECAAAC HTTP/1.1" 200 -
2025-06-30 22:55:38,589 - web_server - INFO - Refresh data request received
2025-06-30 22:55:38,590 - web_server - INFO - Discord API status: ready=True
2025-06-30 22:55:38,590 - discord_api - INFO - Starting full data refresh...
2025-06-30 22:55:38,590 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:38] "GET /socket.io/?EIO=4&transport=polling&t=PV0w4jQ&sid=7OHZizfLJrGV_FECAAAC HTTP/1.1" 200 -
2025-06-30 22:55:39,344 - discord_api - ERROR - Failed to get messages from channel 1149428530854244383: 403
2025-06-30 22:55:39,485 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:55:39,488 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:39] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 22:55:40,308 - discord_api - ERROR - Failed to get messages from channel 1149431881159430245: 403
2025-06-30 22:55:40,402 - discord_api - ERROR - Failed to get guilds: 429
2025-06-30 22:55:40,680 - web_server - INFO - Client disconnected
2025-06-30 22:55:40,680 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:40] "GET /socket.io/?EIO=4&transport=websocket&sid=7OHZizfLJrGV_FECAAAC HTTP/1.1" 200 -
2025-06-30 22:55:40,689 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:40] "GET / HTTP/1.1" 200 -
2025-06-30 22:55:40,714 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:40] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:55:40,715 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:40] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:55:40,729 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:40] "GET /socket.io/?EIO=4&transport=polling&t=PV0w5Es HTTP/1.1" 200 -
2025-06-30 22:55:40,729 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:40] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:55:40,745 - web_server - INFO - Refresh data request received
2025-06-30 22:55:40,746 - web_server - INFO - Client connected
2025-06-30 22:55:40,746 - web_server - INFO - Discord API status: ready=True
2025-06-30 22:55:40,747 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:40] "POST /socket.io/?EIO=4&transport=polling&t=PV0w5F4&sid=pLi5213JxbNYfTCKAAAE HTTP/1.1" 200 -
2025-06-30 22:55:40,747 - discord_api - INFO - Starting full data refresh...
2025-06-30 22:55:40,750 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:40] "GET /socket.io/?EIO=4&transport=polling&t=PV0w5F5&sid=pLi5213JxbNYfTCKAAAE HTTP/1.1" 200 -
2025-06-30 22:55:41,311 - web_server - INFO - Client disconnected
2025-06-30 22:55:41,311 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:41] "GET /socket.io/?EIO=4&transport=websocket&sid=pLi5213JxbNYfTCKAAAE HTTP/1.1" 200 -
2025-06-30 22:55:41,315 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:41] "GET / HTTP/1.1" 200 -
2025-06-30 22:55:41,317 - discord_api - ERROR - Failed to get messages from channel 1149441284898029628: 403
2025-06-30 22:55:41,440 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:41] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:55:41,440 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:41] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:55:41,445 - discord_api - INFO - Loaded 2 DM channels
2025-06-30 22:55:41,457 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:41] "GET /socket.io/?EIO=4&transport=polling&t=PV0w5QE HTTP/1.1" 200 -
2025-06-30 22:55:41,458 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:41] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:55:41,473 - web_server - INFO - Client connected
2025-06-30 22:55:41,473 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:41] "POST /socket.io/?EIO=4&transport=polling&t=PV0w5QR&sid=NR_usDinSLas5Cj2AAAG HTTP/1.1" 200 -
2025-06-30 22:55:41,474 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:41] "GET /socket.io/?EIO=4&transport=polling&t=PV0w5QS&sid=NR_usDinSLas5Cj2AAAG HTTP/1.1" 200 -
2025-06-30 22:55:41,488 - web_server - INFO - Refresh data request received
2025-06-30 22:55:41,488 - web_server - INFO - Discord API status: ready=True
2025-06-30 22:55:41,488 - discord_api - INFO - Starting full data refresh...
2025-06-30 22:55:41,491 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:41] "GET /socket.io/?EIO=4&transport=polling&t=PV0w5Qf&sid=NR_usDinSLas5Cj2AAAG HTTP/1.1" 200 -
2025-06-30 22:55:42,199 - discord_api - ERROR - Failed to get guilds: 429
2025-06-30 22:55:42,202 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:42] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 22:55:42,280 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:55:42,283 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:42] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 22:55:42,483 - discord_api - INFO - Loaded 5 messages from channel 1351651394217574525
2025-06-30 22:55:42,606 - discord_api - ERROR - Failed to get guilds: 429
2025-06-30 22:55:43,502 - discord_api - INFO - Loaded 2 messages from channel 1389168782856949772
2025-06-30 22:55:43,554 - discord_api - INFO - Data refresh completed: 0 guilds, 0 channels, 2 DMs
2025-06-30 22:55:43,554 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:43] "POST /api/refresh HTTP/1.1" 200 -
2025-06-30 22:55:43,597 - discord_api - INFO - Loaded 4 messages from channel 1149448689866260490
2025-06-30 22:55:43,621 - discord_api - INFO - Loaded 2 DM channels
2025-06-30 22:55:43,819 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:55:44,489 - web_server - INFO - Client disconnected
2025-06-30 22:55:44,490 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:44] "GET /socket.io/?EIO=4&transport=websocket&sid=NR_usDinSLas5Cj2AAAG HTTP/1.1" 200 -
2025-06-30 22:55:44,499 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:44] "GET / HTTP/1.1" 200 -
2025-06-30 22:55:44,541 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:44] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:55:44,555 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:44] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:55:44,569 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:44] "GET /socket.io/?EIO=4&transport=polling&t=PV0w6Aq HTTP/1.1" 200 -
2025-06-30 22:55:44,585 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:44] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:55:44,590 - web_server - INFO - Client connected
2025-06-30 22:55:44,590 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:44] "POST /socket.io/?EIO=4&transport=polling&t=PV0w6B9&sid=CJUGxiOT_TN_8ItQAAAI HTTP/1.1" 200 -
2025-06-30 22:55:44,591 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:44] "GET /socket.io/?EIO=4&transport=polling&t=PV0w6B9.0&sid=CJUGxiOT_TN_8ItQAAAI HTTP/1.1" 200 -
2025-06-30 22:55:44,596 - web_server - INFO - Refresh data request received
2025-06-30 22:55:44,597 - web_server - INFO - Discord API status: ready=True
2025-06-30 22:55:44,597 - discord_api - INFO - Starting full data refresh...
2025-06-30 22:55:44,600 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:44] "GET /socket.io/?EIO=4&transport=polling&t=PV0w6BK&sid=CJUGxiOT_TN_8ItQAAAI HTTP/1.1" 200 -
2025-06-30 22:55:44,603 - discord_api - INFO - Loaded 5 messages from channel 1351651394217574525
2025-06-30 22:55:44,663 - discord_api - INFO - Loaded 5 messages from channel 1149455826814238833
2025-06-30 22:55:45,651 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:55:45,740 - discord_api - INFO - Loaded 2 messages from channel 1389168782856949772
2025-06-30 22:55:45,791 - discord_api - INFO - Data refresh completed: 0 guilds, 0 channels, 2 DMs
2025-06-30 22:55:45,792 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:45] "POST /api/refresh HTTP/1.1" 200 -
2025-06-30 22:55:46,014 - discord_api - INFO - Loaded 5 messages from channel 1149463723480731688
2025-06-30 22:55:46,055 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:55:46] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 22:55:46,834 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:55:47,025 - discord_api - ERROR - Failed to get messages from channel 1149561883016310874: 403
2025-06-30 22:55:47,092 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 22:55:48,010 - discord_api - ERROR - Failed to get messages from channel 1149663551980580996: 403
2025-06-30 22:55:48,045 - discord_api - ERROR - Failed to get messages from channel 617069298980093966: 403
2025-06-30 22:55:48,987 - discord_api - ERROR - Failed to get messages from channel 1149731306532511776: 403
2025-06-30 22:55:49,355 - discord_api - ERROR - Failed to get messages from channel 617119519332237374: 403
2025-06-30 22:55:49,660 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 22:55:50,636 - discord_api - INFO - Loaded 3 messages from channel 617285524687683585
2025-06-30 22:55:50,676 - discord_api - ERROR - Failed to get messages from channel 617069298980093966: 403
2025-06-30 22:55:50,902 - discord_api - ERROR - Failed to get messages from channel 1160482679964315749: 403
2025-06-30 22:55:51,789 - discord_api - ERROR - Failed to get messages from channel 617290334250205197: 403
2025-06-30 22:55:51,849 - discord_api - ERROR - Failed to get messages from channel 1164363985227886723: 403
2025-06-30 22:55:52,057 - discord_api - ERROR - Failed to get messages from channel 617119519332237374: 403
2025-06-30 22:55:52,870 - discord_api - ERROR - Failed to get messages from channel 632965173979774976: 403
2025-06-30 22:55:52,895 - discord_api - ERROR - Failed to get messages from channel 1190532584137052180: 403
2025-06-30 22:55:53,670 - discord_api - INFO - Loaded 3 messages from channel 617285524687683585
2025-06-30 22:55:53,878 - discord_api - ERROR - Failed to get messages from channel 639080342782869504: 403
2025-06-30 22:55:53,925 - discord_api - ERROR - Failed to get messages from channel 1190533064317747260: 403
2025-06-30 22:55:54,701 - discord_api - ERROR - Failed to get messages from channel 617290334250205197: 403
2025-06-30 22:55:54,963 - discord_api - ERROR - Failed to get messages from channel 1190533714258698310: 403
2025-06-30 22:55:54,977 - discord_api - INFO - Loaded 5 messages from channel 639791489446182965
2025-06-30 22:55:55,707 - discord_api - ERROR - Failed to get messages from channel 632965173979774976: 403
2025-06-30 22:55:56,044 - discord_api - ERROR - Failed to get messages from channel 640595135939215420: 403
2025-06-30 22:55:56,110 - discord_api - ERROR - Failed to get messages from channel 1244900113898536981: 403
2025-06-30 22:55:57,061 - discord_api - ERROR - Failed to get messages from channel 640810293899034635: 403
2025-06-30 22:55:57,243 - discord_api - ERROR - Failed to get messages from channel 1245952707987243071: 403
2025-06-30 22:55:57,722 - discord_api - ERROR - Failed to get messages from channel 639080342782869504: 403
2025-06-30 22:55:58,109 - discord_api - ERROR - Failed to get messages from channel 646777141916073984: 403
2025-06-30 22:55:58,826 - discord_api - INFO - Loaded 5 messages from channel 639791489446182965
2025-06-30 22:58:23,620 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:58:23,620 - __main__ - INFO - Initializing database...
2025-06-30 22:58:23,622 - __main__ - INFO - Database initialized
2025-06-30 22:58:23,622 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:58:23,622 - __main__ - INFO - Initializing web server...
2025-06-30 22:58:23,668 - __main__ - INFO - Web server initialized
2025-06-30 22:58:23,668 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:58:23,668 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:58:23,668 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:58:23,690 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:58:23,691 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:58:23,691 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:58:24,825 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:58:24,825 - __main__ - INFO - Initializing database...
2025-06-30 22:58:24,827 - __main__ - INFO - Database initialized
2025-06-30 22:58:24,827 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:58:24,827 - __main__ - INFO - Initializing web server...
2025-06-30 22:58:24,875 - __main__ - INFO - Web server initialized
2025-06-30 22:58:24,875 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:58:24,875 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:58:24,875 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:58:24,886 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:58:24,912 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:58:29,004 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:29] "GET /socket.io/?EIO=4&transport=polling&t=PV0wkK9 HTTP/1.1" 200 -
2025-06-30 22:58:29,011 - web_server - INFO - Client connected
2025-06-30 22:58:29,013 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:29] "POST /socket.io/?EIO=4&transport=polling&t=PV0wkKG&sid=-kMo3Wz_q6Kv4CHcAAAA HTTP/1.1" 200 -
2025-06-30 22:58:29,014 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:29] "GET /socket.io/?EIO=4&transport=polling&t=PV0wkKH&sid=-kMo3Wz_q6Kv4CHcAAAA HTTP/1.1" 200 -
2025-06-30 22:58:29,023 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:29] "GET /socket.io/?EIO=4&transport=polling&t=PV0wkKS&sid=-kMo3Wz_q6Kv4CHcAAAA HTTP/1.1" 200 -
2025-06-30 22:58:29,369 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:29] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:58:29,407 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:29] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:58:29,607 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:29] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 22:58:29,629 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:29] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:58:29,799 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:29] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-30 22:58:32,368 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 22:58:32,368 - discord_api - INFO - Starting instant connection...
2025-06-30 22:58:34,707 - discord_api - INFO - Instant connection completed: 3 guilds found, 3 saved
2025-06-30 22:58:34,707 - __main__ - INFO - Using instant connect mode (basic info only)
2025-06-30 22:58:34,707 - __main__ - INFO - Discord API client restarted successfully
2025-06-30 22:58:34,708 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:34] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:58:35,753 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:35] "GET / HTTP/1.1" 200 -
2025-06-30 22:58:35,791 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:35] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:58:35,791 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:35] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:58:35,810 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:35] "GET /socket.io/?EIO=4&transport=polling&t=PV0wl-T HTTP/1.1" 200 -
2025-06-30 22:58:35,811 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:35] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:58:35,822 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:35] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-30 22:58:35,828 - web_server - INFO - Client connected
2025-06-30 22:58:35,828 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:35] "POST /socket.io/?EIO=4&transport=polling&t=PV0wl-g&sid=PZRiP1E6RpNwQjbYAAAC HTTP/1.1" 200 -
2025-06-30 22:58:35,834 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:35] "GET /socket.io/?EIO=4&transport=polling&t=PV0wl-i&sid=PZRiP1E6RpNwQjbYAAAC HTTP/1.1" 200 -
2025-06-30 22:58:35,836 - web_server - INFO - Refresh data request received
2025-06-30 22:58:35,836 - web_server - INFO - Discord API status: ready=True
2025-06-30 22:58:35,837 - discord_api - INFO - Starting full data refresh...
2025-06-30 22:58:35,841 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:58:35] "GET /socket.io/?EIO=4&transport=polling&t=PV0wl-_&sid=PZRiP1E6RpNwQjbYAAAC HTTP/1.1" 200 -
2025-06-30 22:58:38,503 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:58:41,653 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 22:58:42,669 - discord_api - ERROR - Failed to get messages from channel 617069298980093966: 403
2025-06-30 22:58:43,663 - discord_api - ERROR - Failed to get messages from channel 617119519332237374: 403
2025-06-30 22:58:44,724 - discord_api - INFO - Loaded 3 messages from channel 617285524687683585
2025-06-30 22:58:45,683 - discord_api - ERROR - Failed to get messages from channel 617290334250205197: 403
2025-06-30 22:58:46,696 - discord_api - ERROR - Failed to get messages from channel 632965173979774976: 403
2025-06-30 22:58:47,724 - discord_api - ERROR - Failed to get messages from channel 639080342782869504: 403
2025-06-30 22:58:48,810 - discord_api - INFO - Loaded 5 messages from channel 639791489446182965
2025-06-30 22:58:49,774 - discord_api - ERROR - Failed to get messages from channel 640595135939215420: 403
2025-06-30 22:58:50,811 - discord_api - ERROR - Failed to get messages from channel 640810293899034635: 403
2025-06-30 22:58:51,893 - discord_api - ERROR - Failed to get messages from channel 646777141916073984: 403
2025-06-30 22:58:52,860 - discord_api - ERROR - Failed to get messages from channel 647839560633417728: 403
2025-06-30 22:58:53,918 - discord_api - ERROR - Failed to get messages from channel 648432605745905674: 403
2025-06-30 22:58:54,954 - discord_api - ERROR - Failed to get messages from channel 775403459003809804: 403
2025-06-30 22:58:55,912 - discord_api - ERROR - Failed to get messages from channel 779113597506158632: 403
2025-06-30 22:58:56,878 - discord_api - ERROR - Failed to get messages from channel 779708120494506004: 403
2025-06-30 22:58:57,818 - discord_api - ERROR - Failed to get messages from channel 815659596373819422: 403
2025-06-30 22:58:59,949 - discord_api - INFO - Loaded 5 messages from channel 815691774651531325
2025-06-30 22:59:01,038 - discord_api - ERROR - Failed to get messages from channel 818088363738791957: 403
2025-06-30 22:59:02,405 - discord_api - ERROR - Failed to get messages from channel 821444292786389073: 403
2025-06-30 22:59:03,424 - discord_api - ERROR - Failed to get messages from channel 831294590791188530: 403
2025-06-30 22:59:04,400 - discord_api - ERROR - Failed to get messages from channel 844313584744923166: 403
2025-06-30 22:59:05,377 - discord_api - ERROR - Failed to get messages from channel 845390402034073640: 403
2025-06-30 22:59:06,315 - discord_api - ERROR - Failed to get messages from channel 846098229082456086: 403
2025-06-30 22:59:07,326 - discord_api - ERROR - Failed to get messages from channel 848918881564229643: 403
2025-06-30 22:59:08,315 - discord_api - ERROR - Failed to get messages from channel 855114118390349834: 403
2025-06-30 22:59:13,817 - web_server - INFO - Client disconnected
2025-06-30 22:59:13,817 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:59:13] "GET /socket.io/?EIO=4&transport=websocket&sid=PZRiP1E6RpNwQjbYAAAC HTTP/1.1" 200 -
2025-06-30 22:59:13,832 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:59:13] "GET / HTTP/1.1" 200 -
2025-06-30 22:59:13,867 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:59:13] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:59:13,871 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:59:13] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:59:13,885 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:59:13] "GET /socket.io/?EIO=4&transport=polling&t=PV0wvHP HTTP/1.1" 200 -
2025-06-30 22:59:13,897 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:59:13] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:59:13,905 - web_server - INFO - Client connected
2025-06-30 22:59:13,906 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:59:13] "POST /socket.io/?EIO=4&transport=polling&t=PV0wvHd&sid=Rl_f2ZCXOefk38J0AAAE HTTP/1.1" 200 -
2025-06-30 22:59:13,906 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:59:13] "GET /socket.io/?EIO=4&transport=polling&t=PV0wvHf&sid=Rl_f2ZCXOefk38J0AAAE HTTP/1.1" 200 -
2025-06-30 22:59:13,913 - web_server - INFO - Refresh data request received
2025-06-30 22:59:13,913 - web_server - INFO - Discord API status: ready=True
2025-06-30 22:59:13,913 - discord_api - INFO - Starting full data refresh...
2025-06-30 22:59:13,916 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:59:13] "GET /socket.io/?EIO=4&transport=polling&t=PV0wvHu&sid=Rl_f2ZCXOefk38J0AAAE HTTP/1.1" 200 -
2025-06-30 22:59:14,899 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:59:14,902 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:59:14] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 22:59:14,928 - discord_api - ERROR - Failed to get messages from channel 856559720391639080: 403
2025-06-30 22:59:16,071 - discord_api - INFO - Loaded 3 guilds
2025-06-30 22:59:16,943 - discord_api - ERROR - Failed to get messages from channel 864585339212791809: 403
2025-06-30 22:59:17,969 - discord_api - ERROR - Failed to get messages from channel 870988403561340948: 403
2025-06-30 22:59:18,896 - discord_api - ERROR - Failed to get messages from channel 873218339315515392: 403
2025-06-30 22:59:18,904 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 22:59:19,852 - discord_api - ERROR - Failed to get messages from channel 617069298980093966: 403
2025-06-30 22:59:19,980 - discord_api - ERROR - Failed to get messages from channel 874765869123796992: 403
2025-06-30 22:59:20,998 - discord_api - ERROR - Failed to get messages from channel 881511437653147718: 403
2025-06-30 22:59:21,089 - discord_api - ERROR - Failed to get messages from channel 617119519332237374: 403
2025-06-30 22:59:21,950 - discord_api - ERROR - Failed to get messages from channel 891599705883148328: 403
2025-06-30 22:59:22,637 - discord_api - INFO - Loaded 3 messages from channel 617285524687683585
2025-06-30 22:59:23,339 - discord_api - ERROR - Failed to get messages from channel 893881654563700777: 403
2025-06-30 22:59:23,619 - discord_api - ERROR - Failed to get messages from channel 617290334250205197: 403
2025-06-30 22:59:24,626 - discord_api - ERROR - Failed to get messages from channel 632965173979774976: 403
2025-06-30 22:59:26,260 - discord_api - ERROR - Failed to get messages from channel 893882041609900082: 403
2025-06-30 22:59:26,301 - discord_api - ERROR - Failed to get messages from channel 639080342782869504: 403
2025-06-30 22:59:27,422 - discord_api - INFO - Loaded 5 messages from channel 639791489446182965
2025-06-30 22:59:27,511 - discord_api - ERROR - Failed to get messages from channel 894260269038047243: 403
2025-06-30 22:59:28,503 - discord_api - ERROR - Failed to get messages from channel 640595135939215420: 403
2025-06-30 22:59:28,558 - discord_api - ERROR - Failed to get messages from channel 896089253673590854: 403
2025-06-30 22:59:30,144 - discord_api - INFO - Loaded 5 messages from channel 896296485174509618
2025-06-30 22:59:30,363 - discord_api - ERROR - Failed to get messages from channel 640810293899034635: 403
2025-06-30 22:59:31,472 - discord_api - ERROR - Failed to get messages from channel 646777141916073984: 403
2025-06-30 22:59:32,244 - discord_api - INFO - Loaded 5 messages from channel 896296733796073542
2025-06-30 22:59:32,471 - discord_api - ERROR - Failed to get messages from channel 647839560633417728: 403
2025-06-30 22:59:33,283 - discord_api - ERROR - Failed to get messages from channel 899539567374368798: 403
2025-06-30 22:59:33,552 - discord_api - ERROR - Failed to get messages from channel 648432605745905674: 403
2025-06-30 22:59:34,330 - discord_api - ERROR - Failed to get messages from channel 924991192272351242: 403
2025-06-30 22:59:34,615 - discord_api - ERROR - Failed to get messages from channel 775403459003809804: 403
2025-06-30 22:59:35,495 - discord_api - ERROR - Failed to get messages from channel 929752697928839249: 403
2025-06-30 22:59:35,604 - discord_api - ERROR - Failed to get messages from channel 779113597506158632: 403
2025-06-30 22:59:36,554 - discord_api - ERROR - Failed to get messages from channel 930509719905390622: 403
2025-06-30 22:59:36,889 - discord_api - ERROR - Failed to get messages from channel 779708120494506004: 403
2025-06-30 22:59:37,571 - discord_api - ERROR - Failed to get messages from channel 947126211287588864: 403
2025-06-30 22:59:38,554 - discord_api - ERROR - Failed to get messages from channel 815659596373819422: 403
2025-06-30 22:59:38,620 - discord_api - ERROR - Failed to get messages from channel 955889242326835220: 403
2025-06-30 22:59:39,678 - discord_api - INFO - Loaded 3 messages from channel 995268606751408188
2025-06-30 22:59:39,742 - discord_api - INFO - Loaded 5 messages from channel 815691774651531325
2025-06-30 22:59:41,751 - discord_api - ERROR - Failed to get messages from channel 995686632348983397: 403
2025-06-30 23:01:27,494 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:01:27,494 - __main__ - INFO - Initializing database...
2025-06-30 23:01:27,496 - __main__ - INFO - Database initialized
2025-06-30 23:01:27,496 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:01:27,496 - __main__ - INFO - Initializing web server...
2025-06-30 23:01:27,542 - __main__ - INFO - Web server initialized
2025-06-30 23:01:27,542 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:01:27,542 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:01:27,543 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:01:27,564 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 23:01:27,564 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 23:01:27,564 - werkzeug - INFO -  * Restarting with stat
2025-06-30 23:01:28,693 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:01:28,693 - __main__ - INFO - Initializing database...
2025-06-30 23:01:28,694 - __main__ - INFO - Database initialized
2025-06-30 23:01:28,694 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:01:28,694 - __main__ - INFO - Initializing web server...
2025-06-30 23:01:28,740 - __main__ - INFO - Web server initialized
2025-06-30 23:01:28,740 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:01:28,740 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:01:28,740 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:01:28,750 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 23:01:28,765 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 23:01:28,779 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0xPoc HTTP/1.1" 200 -
2025-06-30 23:01:28,779 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0xQ2u HTTP/1.1" 200 -
2025-06-30 23:01:28,788 - web_server - INFO - Client connected
2025-06-30 23:01:28,788 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:28] "POST /socket.io/?EIO=4&transport=polling&t=PV0xQDF&sid=JgJ2vw3ibGMbVA1VAAAA HTTP/1.1" 200 -
2025-06-30 23:01:28,790 - web_server - INFO - Client connected
2025-06-30 23:01:28,790 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:28] "POST /socket.io/?EIO=4&transport=polling&t=PV0xQDG&sid=fJtx9bbuAYNu4UluAAAB HTTP/1.1" 200 -
2025-06-30 23:01:28,791 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0xQDG&sid=JgJ2vw3ibGMbVA1VAAAA HTTP/1.1" 200 -
2025-06-30 23:01:28,794 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0xQDG.0&sid=fJtx9bbuAYNu4UluAAAB HTTP/1.1" 200 -
2025-06-30 23:01:28,799 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0xQDS&sid=JgJ2vw3ibGMbVA1VAAAA HTTP/1.1" 200 -
2025-06-30 23:01:28,804 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0xQDV&sid=fJtx9bbuAYNu4UluAAAB HTTP/1.1" 200 -
2025-06-30 23:01:28,808 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0xQDZ&sid=JgJ2vw3ibGMbVA1VAAAA HTTP/1.1" 200 -
2025-06-30 23:01:28,811 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0xQDe&sid=fJtx9bbuAYNu4UluAAAB HTTP/1.1" 200 -
2025-06-30 23:01:33,607 - web_server - INFO - Client disconnected
2025-06-30 23:01:33,607 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:33] "GET /socket.io/?EIO=4&transport=websocket&sid=JgJ2vw3ibGMbVA1VAAAA HTTP/1.1" 200 -
2025-06-30 23:01:33,620 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:33] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 23:01:33,648 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:33] "GET /setup HTTP/1.1" 200 -
2025-06-30 23:01:33,767 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:33] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 23:01:33,773 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:33] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 23:01:33,789 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:33] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-30 23:01:36,675 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 23:01:36,675 - discord_api - INFO - Starting instant connection...
2025-06-30 23:01:38,640 - discord_api - INFO - Instant connection completed: 3 guilds found, 3 saved
2025-06-30 23:01:38,641 - __main__ - INFO - Using instant connect mode (basic info only)
2025-06-30 23:01:38,641 - __main__ - INFO - Discord API client restarted successfully
2025-06-30 23:01:38,641 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:38] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 23:01:39,694 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:39] "GET / HTTP/1.1" 200 -
2025-06-30 23:01:39,727 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:39] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 23:01:39,728 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:39] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 23:01:39,742 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:39] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-30 23:01:39,750 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:39] "GET /api/status HTTP/1.1" 200 -
2025-06-30 23:01:39,751 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:39] "GET /socket.io/?EIO=4&transport=polling&t=PV0xSuT HTTP/1.1" 200 -
2025-06-30 23:01:39,761 - web_server - INFO - Client connected
2025-06-30 23:01:39,762 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:39] "POST /socket.io/?EIO=4&transport=polling&t=PV0xSuj&sid=MDVIFE53piIiOHMvAAAE HTTP/1.1" 200 -
2025-06-30 23:01:39,766 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:39] "GET /socket.io/?EIO=4&transport=polling&t=PV0xSuk&sid=MDVIFE53piIiOHMvAAAE HTTP/1.1" 200 -
2025-06-30 23:01:39,778 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:39] "POST /api/quick-setup HTTP/1.1" 200 -
2025-06-30 23:01:39,778 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:39] "GET /socket.io/?EIO=4&transport=polling&t=PV0xSux&sid=MDVIFE53piIiOHMvAAAE HTTP/1.1" 200 -
2025-06-30 23:01:41,300 - discord_api - INFO - Loaded 3 guilds
2025-06-30 23:01:41,303 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:41] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 23:01:42,291 - discord_api - INFO - Loaded 2 DM channels
2025-06-30 23:01:42,295 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:42] "GET /api/dms HTTP/1.1" 200 -
2025-06-30 23:01:44,844 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:01:44,846 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:44] "GET /api/channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:01:46,565 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:01:46,566 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:46] "POST /api/load-channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:01:48,147 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:01:48,149 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:48] "GET /api/channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:01:50,935 - discord_api - ERROR - Failed to get channels for guild 979483208339062800: 404
2025-06-30 23:01:50,938 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:50] "GET /api/channels/979483208339062800 HTTP/1.1" 200 -
2025-06-30 23:01:52,902 - discord_api - ERROR - Failed to get channels for guild 979483208339062800: 404
2025-06-30 23:01:52,902 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:52] "POST /api/load-channels/979483208339062800 HTTP/1.1" 200 -
2025-06-30 23:01:53,799 - discord_api - ERROR - Failed to get channels for guild 979483208339062800: 404
2025-06-30 23:01:53,801 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:53] "GET /api/channels/979483208339062800 HTTP/1.1" 200 -
2025-06-30 23:01:56,795 - discord_api - ERROR - Failed to get messages from channel 1351651394217574400: 404
2025-06-30 23:01:56,797 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:56] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:01:58,003 - discord_api - ERROR - Failed to get messages from channel 1351651394217574400: 404
2025-06-30 23:01:58,004 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:58] "POST /api/load-messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:01:58,909 - discord_api - ERROR - Failed to get messages from channel 1351651394217574400: 404
2025-06-30 23:01:58,912 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:01:58] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:02:03,141 - discord_api - ERROR - Failed to get channels for guild 979483208339062800: 404
2025-06-30 23:02:03,143 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:02:03] "GET /api/channels/979483208339062800 HTTP/1.1" 200 -
2025-06-30 23:02:04,432 - discord_api - ERROR - Failed to get channels for guild 979483208339062800: 404
2025-06-30 23:02:04,433 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:02:04] "POST /api/load-channels/979483208339062800 HTTP/1.1" 200 -
2025-06-30 23:02:05,376 - discord_api - ERROR - Failed to get channels for guild 979483208339062800: 404
2025-06-30 23:02:05,377 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:02:05] "GET /api/channels/979483208339062800 HTTP/1.1" 200 -
2025-06-30 23:02:07,283 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:02:07,285 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:02:07] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:02:08,199 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:02:08,200 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:02:08] "POST /api/load-channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:02:09,417 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:02:09,419 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:02:09] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:02:11,058 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:02:11,061 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:02:11] "GET /api/channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:02:12,900 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:02:12,901 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:02:12] "POST /api/load-channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:02:13,855 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:02:13,858 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:02:13] "GET /api/channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:04:52,247 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:04:52,247 - __main__ - INFO - Initializing database...
2025-06-30 23:04:52,248 - __main__ - INFO - Database initialized
2025-06-30 23:04:52,248 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:04:52,248 - __main__ - INFO - Initializing web server...
2025-06-30 23:04:52,320 - __main__ - INFO - Web server initialized
2025-06-30 23:04:52,320 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:04:52,320 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:04:52,320 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:04:52,349 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 23:04:52,349 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 23:04:52,349 - werkzeug - INFO -  * Restarting with stat
2025-06-30 23:04:53,741 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:04:53,741 - __main__ - INFO - Initializing database...
2025-06-30 23:04:53,743 - __main__ - INFO - Database initialized
2025-06-30 23:04:53,743 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:04:53,743 - __main__ - INFO - Initializing web server...
2025-06-30 23:04:53,842 - __main__ - INFO - Web server initialized
2025-06-30 23:04:53,842 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:04:53,842 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:04:53,842 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:04:53,861 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 23:04:53,906 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 23:04:54,210 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:04:54] "GET /socket.io/?EIO=4&transport=polling&t=PV0yCMz HTTP/1.1" 200 -
2025-06-30 23:04:54,222 - web_server - INFO - Client connected
2025-06-30 23:04:54,222 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:04:54] "POST /socket.io/?EIO=4&transport=polling&t=PV0yCN6&sid=VCsGcI7czQaMuYVMAAAA HTTP/1.1" 200 -
2025-06-30 23:04:54,223 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:04:54] "GET /socket.io/?EIO=4&transport=polling&t=PV0yCN7&sid=VCsGcI7czQaMuYVMAAAA HTTP/1.1" 200 -
2025-06-30 23:04:54,235 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:04:54] "GET /socket.io/?EIO=4&transport=polling&t=PV0yCNL&sid=VCsGcI7czQaMuYVMAAAA HTTP/1.1" 200 -
2025-06-30 23:04:56,402 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:04:56] "GET /socket.io/?EIO=4&transport=polling&t=PV0yCvF HTTP/1.1" 200 -
2025-06-30 23:04:56,409 - web_server - INFO - Client connected
2025-06-30 23:04:56,410 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:04:56] "POST /socket.io/?EIO=4&transport=polling&t=PV0yCvN&sid=vRC5A0FFJhoSKFwaAAAC HTTP/1.1" 200 -
2025-06-30 23:04:56,411 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:04:56] "GET /socket.io/?EIO=4&transport=polling&t=PV0yCvO&sid=vRC5A0FFJhoSKFwaAAAC HTTP/1.1" 200 -
2025-06-30 23:04:56,418 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:04:56] "GET /socket.io/?EIO=4&transport=polling&t=PV0yCvW&sid=vRC5A0FFJhoSKFwaAAAC HTTP/1.1" 200 -
2025-06-30 23:05:08,942 - web_server - INFO - Client disconnected
2025-06-30 23:05:08,942 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:08] "GET /socket.io/?EIO=4&transport=websocket&sid=vRC5A0FFJhoSKFwaAAAC HTTP/1.1" 200 -
2025-06-30 23:05:08,956 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:08] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 23:05:08,983 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:08] "GET /setup HTTP/1.1" 200 -
2025-06-30 23:05:09,096 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:09] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 23:05:09,099 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:09] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 23:05:11,575 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 23:05:11,576 - discord_api - INFO - Starting quick connection...
2025-06-30 23:05:13,507 - discord_api - INFO - Loaded 3 guilds
2025-06-30 23:05:16,082 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 23:05:19,172 - discord_api - INFO - Loaded 185 channels for guild 979483208339062785
2025-06-30 23:05:25,452 - discord_api - INFO - Loaded 384 channels for guild 1149420682216747088
2025-06-30 23:05:26,405 - discord_api - INFO - Loaded 2 DM channels
2025-06-30 23:05:26,406 - discord_api - INFO - Quick connection completed: 3 guilds, 709 channels, 2 DMs
2025-06-30 23:05:26,406 - __main__ - INFO - Using quick connect mode (guilds and channels, no messages)
2025-06-30 23:05:26,406 - __main__ - INFO - Discord API client restarted successfully
2025-06-30 23:05:26,406 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:26] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 23:05:27,443 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "GET / HTTP/1.1" 200 -
2025-06-30 23:05:27,480 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 23:05:27,481 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 23:05:27,507 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "GET /socket.io/?EIO=4&transport=polling&t=PV0yKVB HTTP/1.1" 200 -
2025-06-30 23:05:27,513 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "GET /api/status HTTP/1.1" 200 -
2025-06-30 23:05:27,519 - web_server - INFO - Client connected
2025-06-30 23:05:27,520 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "POST /socket.io/?EIO=4&transport=polling&t=PV0yKVP&sid=ly7gPBJqMl2QaQ-XAAAE HTTP/1.1" 200 -
2025-06-30 23:05:27,523 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "GET /socket.io/?EIO=4&transport=polling&t=PV0yKVP.0&sid=ly7gPBJqMl2QaQ-XAAAE HTTP/1.1" 200 -
2025-06-30 23:05:27,530 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "GET /socket.io/?EIO=4&transport=polling&t=PV0yKVc&sid=ly7gPBJqMl2QaQ-XAAAE HTTP/1.1" 200 -
2025-06-30 23:05:27,532 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "POST /api/quick-setup HTTP/1.1" 200 -
2025-06-30 23:05:27,543 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 23:05:27,544 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-30 23:05:27,555 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:27] "GET /api/dms HTTP/1.1" 200 -
2025-06-30 23:05:31,729 - discord_api - ERROR - Failed to get messages from channel 1351651394217574400: 404
2025-06-30 23:05:31,731 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:31] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:05:33,496 - discord_api - ERROR - Failed to get messages from channel 1351651394217574400: 404
2025-06-30 23:05:33,496 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:33] "POST /api/load-messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:05:33,508 - discord_api - ERROR - Failed to get messages from channel 1389168782856949800: 404
2025-06-30 23:05:33,510 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:33] "GET /api/messages/1389168782856949800?page=0 HTTP/1.1" 200 -
2025-06-30 23:05:34,410 - discord_api - ERROR - Failed to get messages from channel 1351651394217574400: 404
2025-06-30 23:05:34,414 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:05:34,416 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:34] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:05:34,492 - discord_api - ERROR - Failed to get messages from channel 1389168782856949800: 404
2025-06-30 23:05:34,493 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:34] "POST /api/load-messages/1389168782856949800 HTTP/1.1" 200 -
2025-06-30 23:05:35,280 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:05:35,282 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:35] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:05:35,368 - discord_api - ERROR - Failed to get messages from channel 1389168782856949800: 404
2025-06-30 23:05:35,371 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:35] "GET /api/messages/1389168782856949800?page=0 HTTP/1.1" 200 -
2025-06-30 23:05:36,221 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:05:36,221 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:36] "POST /api/load-channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:05:36,229 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:05:37,208 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:05:37,209 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:37] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:05:48,663 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:48] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 23:05:54,735 - discord_api - ERROR - Failed to get messages from channel 1351651394217574400: 404
2025-06-30 23:05:54,737 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:54] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:05:56,395 - discord_api - ERROR - Failed to get messages from channel 1351651394217574400: 404
2025-06-30 23:05:56,396 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:56] "POST /api/load-messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:05:57,362 - discord_api - ERROR - Failed to get messages from channel 1351651394217574400: 404
2025-06-30 23:05:57,364 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:05:57] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:07:55,095 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:07:55,096 - __main__ - INFO - Initializing database...
2025-06-30 23:07:55,097 - __main__ - INFO - Database initialized
2025-06-30 23:07:55,097 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:07:55,097 - __main__ - INFO - Initializing web server...
2025-06-30 23:07:55,149 - __main__ - INFO - Web server initialized
2025-06-30 23:07:55,149 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:07:55,149 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:07:55,149 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:07:55,173 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 23:07:55,174 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 23:07:55,174 - werkzeug - INFO -  * Restarting with stat
2025-06-30 23:07:56,479 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:07:56,479 - __main__ - INFO - Initializing database...
2025-06-30 23:07:56,481 - __main__ - INFO - Database initialized
2025-06-30 23:07:56,481 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:07:56,481 - __main__ - INFO - Initializing web server...
2025-06-30 23:07:56,527 - __main__ - INFO - Web server initialized
2025-06-30 23:07:56,527 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:07:56,527 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:07:56,528 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:07:56,539 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 23:07:56,551 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 23:07:56,868 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:07:56] "GET /socket.io/?EIO=4&transport=polling&t=PV0yuz0 HTTP/1.1" 200 -
2025-06-30 23:07:56,875 - web_server - INFO - Client connected
2025-06-30 23:07:56,875 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:07:56] "POST /socket.io/?EIO=4&transport=polling&t=PV0yuz7&sid=OZLP5ZcU8UKZq7wBAAAA HTTP/1.1" 200 -
2025-06-30 23:07:56,876 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:07:56] "GET /socket.io/?EIO=4&transport=polling&t=PV0yuz8&sid=OZLP5ZcU8UKZq7wBAAAA HTTP/1.1" 200 -
2025-06-30 23:07:56,885 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:07:56] "GET /socket.io/?EIO=4&transport=polling&t=PV0yuzI&sid=OZLP5ZcU8UKZq7wBAAAA HTTP/1.1" 200 -
2025-06-30 23:07:56,914 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:07:56] "GET /socket.io/?EIO=4&transport=polling&t=PV0yuzk HTTP/1.1" 200 -
2025-06-30 23:07:56,919 - web_server - INFO - Client connected
2025-06-30 23:07:56,920 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:07:56] "POST /socket.io/?EIO=4&transport=polling&t=PV0yuzq&sid=Meh7mHlOGewPfsouAAAC HTTP/1.1" 200 -
2025-06-30 23:07:56,920 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:07:56] "GET /socket.io/?EIO=4&transport=polling&t=PV0yuzq.0&sid=Meh7mHlOGewPfsouAAAC HTTP/1.1" 200 -
2025-06-30 23:07:56,927 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:07:56] "GET /socket.io/?EIO=4&transport=polling&t=PV0yuzy&sid=Meh7mHlOGewPfsouAAAC HTTP/1.1" 200 -
2025-06-30 23:08:01,126 - web_server - INFO - Client disconnected
2025-06-30 23:08:01,126 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:01] "GET /socket.io/?EIO=4&transport=websocket&sid=Meh7mHlOGewPfsouAAAC HTTP/1.1" 200 -
2025-06-30 23:08:01,141 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:01] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 23:08:01,172 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:01] "GET /setup HTTP/1.1" 200 -
2025-06-30 23:08:01,290 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:01] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 23:08:01,291 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:01] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 23:08:03,187 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 23:08:03,187 - discord_api - INFO - Starting quick connection...
2025-06-30 23:08:05,572 - discord_api - INFO - Loaded 3 guilds
2025-06-30 23:08:08,497 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 23:08:11,627 - discord_api - INFO - Loaded 185 channels for guild 979483208339062785
2025-06-30 23:08:17,746 - discord_api - INFO - Loaded 384 channels for guild 1149420682216747088
2025-06-30 23:08:18,726 - discord_api - INFO - Loaded 2 DM channels
2025-06-30 23:08:18,727 - discord_api - INFO - Quick connection completed: 3 guilds, 709 channels, 2 DMs
2025-06-30 23:08:18,727 - __main__ - INFO - Using quick connect mode (guilds and channels, no messages)
2025-06-30 23:08:18,727 - __main__ - INFO - Discord API client restarted successfully
2025-06-30 23:08:18,728 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:18] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 23:08:19,770 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "GET / HTTP/1.1" 200 -
2025-06-30 23:08:19,816 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 23:08:19,819 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 23:08:19,838 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "GET /socket.io/?EIO=4&transport=polling&t=PV0y-Zu HTTP/1.1" 200 -
2025-06-30 23:08:19,838 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "GET /api/status HTTP/1.1" 200 -
2025-06-30 23:08:19,857 - web_server - INFO - Client connected
2025-06-30 23:08:19,858 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "POST /socket.io/?EIO=4&transport=polling&t=PV0y-a6&sid=r4b8HE9SRRrzaokXAAAE HTTP/1.1" 200 -
2025-06-30 23:08:19,859 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "GET /socket.io/?EIO=4&transport=polling&t=PV0y-a7&sid=r4b8HE9SRRrzaokXAAAE HTTP/1.1" 200 -
2025-06-30 23:08:19,870 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "POST /api/quick-setup HTTP/1.1" 200 -
2025-06-30 23:08:19,870 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "GET /socket.io/?EIO=4&transport=polling&t=PV0y-aQ&sid=r4b8HE9SRRrzaokXAAAE HTTP/1.1" 200 -
2025-06-30 23:08:19,893 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 23:08:19,990 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:19] "GET /api/dms HTTP/1.1" 200 -
2025-06-30 23:08:22,186 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:08:22,186 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:08:23,094 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:08:23,094 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:08:23,094 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:08:23,096 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:23] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:08:23,181 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:08:23,181 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:08:24,075 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:08:24,075 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:08:24,075 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:08:24,077 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:24] "GET /api/messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:08:24,084 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:08:26,712 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:08:26,712 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:08:26,712 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:26] "POST /api/load-messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:08:26,718 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:08:26,719 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:08:27,621 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:08:27,621 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:08:27,621 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:08:27,623 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:27] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:08:29,062 - web_server - INFO - No channels in database for guild 617069298980094000, loading from Discord API...
2025-06-30 23:08:30,048 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:08:30,049 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:30] "GET /api/channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:08:31,217 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:08:31,218 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:31] "POST /api/load-channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:08:31,224 - web_server - INFO - No channels in database for guild 617069298980094000, loading from Discord API...
2025-06-30 23:08:32,114 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:08:32,115 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:32] "GET /api/channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:08:35,259 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:08:36,181 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:08:36,183 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:36] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:08:37,093 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:08:37,093 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:37] "POST /api/load-channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:08:37,101 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:08:37,981 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:08:37,982 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:37] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:08:39,720 - web_server - INFO - No messages in database for channel 1389168782856949800, loading from Discord API...
2025-06-30 23:08:39,720 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:08:40,662 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:08:40,662 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:08:40,662 - web_server - INFO - Loaded 0 messages from Discord API for channel 1389168782856949800
2025-06-30 23:08:40,664 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:40] "GET /api/messages/1389168782856949800?page=0 HTTP/1.1" 200 -
2025-06-30 23:08:40,736 - web_server - INFO - No messages in database for channel 1389168782856949800, loading from Discord API...
2025-06-30 23:08:40,736 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:08:41,684 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:08:41,685 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:08:41,685 - web_server - INFO - Loaded 0 messages from Discord API for channel 1389168782856949800
2025-06-30 23:08:41,686 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:41] "GET /api/messages/1389168782856949800 HTTP/1.1" 200 -
2025-06-30 23:08:41,691 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:08:42,642 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:08:42,642 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:08:42,643 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:42] "POST /api/load-messages/1389168782856949800 HTTP/1.1" 200 -
2025-06-30 23:08:42,649 - web_server - INFO - No messages in database for channel 1389168782856949800, loading from Discord API...
2025-06-30 23:08:42,650 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:08:43,515 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:08:43,515 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:08:43,516 - web_server - INFO - Loaded 0 messages from Discord API for channel 1389168782856949800
2025-06-30 23:08:43,517 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:08:43] "GET /api/messages/1389168782856949800?page=0 HTTP/1.1" 200 -
2025-06-30 23:10:55,801 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:10:55,801 - __main__ - INFO - Initializing database...
2025-06-30 23:10:55,803 - __main__ - INFO - Database initialized
2025-06-30 23:10:55,803 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:10:55,803 - __main__ - INFO - Initializing web server...
2025-06-30 23:10:55,874 - __main__ - INFO - Web server initialized
2025-06-30 23:10:55,874 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:10:55,874 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:10:55,875 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:10:55,916 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 23:10:55,916 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 23:10:55,916 - werkzeug - INFO -  * Restarting with stat
2025-06-30 23:10:58,426 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:10:58,426 - __main__ - INFO - Initializing database...
2025-06-30 23:10:58,429 - __main__ - INFO - Database initialized
2025-06-30 23:10:58,429 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:10:58,429 - __main__ - INFO - Initializing web server...
2025-06-30 23:10:58,507 - __main__ - INFO - Web server initialized
2025-06-30 23:10:58,507 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:10:58,507 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:10:58,508 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:10:58,526 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 23:10:58,565 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 23:10:58,578 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:10:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0zamP HTTP/1.1" 200 -
2025-06-30 23:10:58,589 - web_server - INFO - Client connected
2025-06-30 23:10:58,590 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:10:58] "POST /socket.io/?EIO=4&transport=polling&t=PV0zbKM&sid=oSOqfUphAg3vbhcEAAAA HTTP/1.1" 200 -
2025-06-30 23:10:58,591 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:10:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0zbKN&sid=oSOqfUphAg3vbhcEAAAA HTTP/1.1" 200 -
2025-06-30 23:10:58,604 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:10:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0zbKc&sid=oSOqfUphAg3vbhcEAAAA HTTP/1.1" 200 -
2025-06-30 23:11:01,398 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:01] "GET /socket.io/?EIO=4&transport=polling&t=PV0zc0J HTTP/1.1" 200 -
2025-06-30 23:11:01,406 - web_server - INFO - Client connected
2025-06-30 23:11:01,408 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:01] "POST /socket.io/?EIO=4&transport=polling&t=PV0zc0Q&sid=vKeIUEdf1wAUoFkSAAAC HTTP/1.1" 200 -
2025-06-30 23:11:01,408 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:01] "GET /socket.io/?EIO=4&transport=polling&t=PV0zc0R&sid=vKeIUEdf1wAUoFkSAAAC HTTP/1.1" 200 -
2025-06-30 23:11:01,419 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:01] "GET /socket.io/?EIO=4&transport=polling&t=PV0zc0e&sid=vKeIUEdf1wAUoFkSAAAC HTTP/1.1" 200 -
2025-06-30 23:11:17,074 - web_server - INFO - Client disconnected
2025-06-30 23:11:17,074 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:17] "GET /socket.io/?EIO=4&transport=websocket&sid=vKeIUEdf1wAUoFkSAAAC HTTP/1.1" 200 -
2025-06-30 23:11:17,088 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:17] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 23:11:17,115 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:17] "GET /setup HTTP/1.1" 200 -
2025-06-30 23:11:17,251 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:17] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 23:11:17,253 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:17] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 23:11:19,430 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 23:11:19,430 - discord_api - INFO - Starting quick connection...
2025-06-30 23:11:22,097 - discord_api - INFO - Loaded 3 guilds
2025-06-30 23:11:25,172 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 23:11:28,651 - discord_api - INFO - Loaded 185 channels for guild 979483208339062785
2025-06-30 23:11:34,589 - discord_api - INFO - Loaded 384 channels for guild 1149420682216747088
2025-06-30 23:11:35,555 - discord_api - INFO - Loaded 2 DM channels
2025-06-30 23:11:35,555 - discord_api - INFO - Quick connection completed: 3 guilds, 709 channels, 2 DMs
2025-06-30 23:11:35,556 - __main__ - INFO - Using quick connect mode (guilds and channels, no messages)
2025-06-30 23:11:35,556 - __main__ - INFO - Discord API client restarted successfully
2025-06-30 23:11:35,556 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:35] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 23:11:36,603 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "GET / HTTP/1.1" 200 -
2025-06-30 23:11:36,639 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 23:11:36,640 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 23:11:36,671 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "GET /socket.io/?EIO=4&transport=polling&t=PV0zkdP HTTP/1.1" 200 -
2025-06-30 23:11:36,672 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "GET /api/status HTTP/1.1" 200 -
2025-06-30 23:11:36,680 - web_server - INFO - Client connected
2025-06-30 23:11:36,681 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "POST /socket.io/?EIO=4&transport=polling&t=PV0zkdZ&sid=OlCKR5t6VAHtwhytAAAE HTTP/1.1" 200 -
2025-06-30 23:11:36,681 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "GET /socket.io/?EIO=4&transport=polling&t=PV0zkdb&sid=OlCKR5t6VAHtwhytAAAE HTTP/1.1" 200 -
2025-06-30 23:11:36,687 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "POST /api/quick-setup HTTP/1.1" 200 -
2025-06-30 23:11:36,688 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "GET /socket.io/?EIO=4&transport=polling&t=PV0zkdk&sid=OlCKR5t6VAHtwhytAAAE HTTP/1.1" 200 -
2025-06-30 23:11:36,702 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 23:11:36,711 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:36] "GET /api/dms HTTP/1.1" 200 -
2025-06-30 23:11:40,360 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:11:40,360 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:11:41,520 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:11:41,520 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:11:41,521 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:11:41,522 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:41] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:11:41,592 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:11:41,593 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:11:42,465 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:11:42,465 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:11:42,465 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:11:42,466 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:42] "GET /api/messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:11:42,471 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:11:43,419 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:11:43,419 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:11:43,419 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:43] "POST /api/load-messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:11:43,426 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:11:43,427 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:11:44,579 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:11:44,579 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:11:44,579 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:11:44,580 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:44] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:11:46,623 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:46] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 23:11:49,300 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:11:50,172 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:11:50,174 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:50] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:11:50,358 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:11:51,929 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:11:51,929 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:51] "POST /api/load-channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:11:51,936 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:11:52,565 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:11:52,567 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:52] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:11:52,856 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:11:52,857 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:52] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:11:53,453 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:11:53,454 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:53] "POST /api/load-channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:11:53,466 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:11:54,437 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:11:54,439 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:11:54] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:14:42,987 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:14:42,987 - __main__ - INFO - Initializing database...
2025-06-30 23:14:42,988 - __main__ - INFO - Database initialized
2025-06-30 23:14:42,988 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:14:42,988 - __main__ - INFO - Initializing web server...
2025-06-30 23:14:43,034 - __main__ - INFO - Web server initialized
2025-06-30 23:14:43,034 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:14:43,034 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:14:43,035 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:14:43,052 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 23:14:43,053 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 23:14:43,053 - werkzeug - INFO -  * Restarting with stat
2025-06-30 23:14:44,105 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:14:44,105 - __main__ - INFO - Initializing database...
2025-06-30 23:14:44,106 - __main__ - INFO - Database initialized
2025-06-30 23:14:44,106 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:14:44,107 - __main__ - INFO - Initializing web server...
2025-06-30 23:14:44,150 - __main__ - INFO - Web server initialized
2025-06-30 23:14:44,150 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:14:44,150 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:14:44,151 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:14:44,160 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 23:14:44,176 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 23:15:06,261 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:06] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 23:15:06,352 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:06] "GET /setup HTTP/1.1" 200 -
2025-06-30 23:15:06,512 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:06] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 23:15:06,515 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:06] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 23:15:08,916 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 23:15:08,916 - discord_api - INFO - Starting quick connection...
2025-06-30 23:15:13,274 - discord_api - INFO - Loaded 3 guilds
2025-06-30 23:15:16,001 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 23:15:19,425 - discord_api - INFO - Loaded 184 channels for guild 979483208339062785
2025-06-30 23:15:26,408 - discord_api - INFO - Loaded 384 channels for guild 1149420682216747088
2025-06-30 23:15:27,395 - discord_api - INFO - Loaded 2 DM channels
2025-06-30 23:15:27,395 - discord_api - INFO - Quick connection completed: 3 guilds, 708 channels, 2 DMs
2025-06-30 23:15:27,396 - __main__ - INFO - Using quick connect mode (guilds and channels, no messages)
2025-06-30 23:15:27,397 - __main__ - INFO - Discord API client restarted successfully
2025-06-30 23:15:27,397 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:27] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 23:15:28,455 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "GET / HTTP/1.1" 200 -
2025-06-30 23:15:28,523 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 23:15:28,523 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 23:15:28,559 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0-dEg HTTP/1.1" 200 -
2025-06-30 23:15:28,563 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "GET /api/status HTTP/1.1" 200 -
2025-06-30 23:15:28,587 - web_server - INFO - Client connected
2025-06-30 23:15:28,588 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "POST /socket.io/?EIO=4&transport=polling&t=PV0-dF1&sid=tRf3ayhefLjbVGA4AAAA HTTP/1.1" 200 -
2025-06-30 23:15:28,595 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0-dF7&sid=tRf3ayhefLjbVGA4AAAA HTTP/1.1" 200 -
2025-06-30 23:15:28,605 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "POST /api/quick-setup HTTP/1.1" 200 -
2025-06-30 23:15:28,615 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "GET /socket.io/?EIO=4&transport=polling&t=PV0-dFT&sid=tRf3ayhefLjbVGA4AAAA HTTP/1.1" 200 -
2025-06-30 23:15:28,637 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 23:15:28,695 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:15:28] "GET /api/dms HTTP/1.1" 200 -
2025-06-30 23:16:06,463 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:16:06,463 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:16:07,653 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:16:07,654 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:16:07,654 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:16:07,656 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:07] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:16:07,736 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:16:07,736 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:16:08,650 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:16:08,650 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:16:08,650 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:16:08,651 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:08] "GET /api/messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:16:08,658 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:16:09,566 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:16:09,566 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:16:09,567 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:09] "POST /api/load-messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:16:09,582 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:16:09,582 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:16:10,520 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:16:10,521 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:16:10,521 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:16:10,522 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:10] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:16:12,768 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:12] "GET /api/debug HTTP/1.1" 200 -
2025-06-30 23:16:14,880 - web_server - INFO - No channels in database for guild 617069298980094000, loading from Discord API...
2025-06-30 23:16:15,802 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:16:15,804 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:15] "GET /api/channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:16:16,057 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:16:16,713 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:16:16,713 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:16] "POST /api/load-channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:16:16,722 - web_server - INFO - No channels in database for guild 617069298980094000, loading from Discord API...
2025-06-30 23:16:17,122 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:16:17,124 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:17] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:16:17,612 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:16:17,613 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:17] "GET /api/channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:16:18,115 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:16:18,116 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:18] "POST /api/load-channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:16:18,122 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:16:19,051 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:16:19,053 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:19] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:16:19,440 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:16:19,718 - web_server - INFO - No channels in database for guild 979483208339062800, loading from Discord API...
2025-06-30 23:16:20,333 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:16:20,335 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:20] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:16:20,755 - discord_api - ERROR - Failed to get channels for guild 979483208339062800: 404
2025-06-30 23:16:20,756 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:20] "GET /api/channels/979483208339062800 HTTP/1.1" 200 -
2025-06-30 23:16:21,244 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:16:21,244 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:21] "POST /api/load-channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:16:21,251 - web_server - INFO - No channels in database for guild 1149420682216747100, loading from Discord API...
2025-06-30 23:16:21,846 - discord_api - ERROR - Failed to get channels for guild 979483208339062800: 404
2025-06-30 23:16:21,846 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:21] "POST /api/load-channels/979483208339062800 HTTP/1.1" 200 -
2025-06-30 23:16:21,854 - web_server - INFO - No channels in database for guild 979483208339062800, loading from Discord API...
2025-06-30 23:16:22,213 - discord_api - ERROR - Failed to get channels for guild 1149420682216747100: 404
2025-06-30 23:16:22,214 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:22] "GET /api/channels/1149420682216747100 HTTP/1.1" 200 -
2025-06-30 23:16:22,767 - discord_api - ERROR - Failed to get channels for guild 979483208339062800: 404
2025-06-30 23:16:22,769 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:22] "GET /api/channels/979483208339062800 HTTP/1.1" 200 -
2025-06-30 23:16:43,848 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:16:43,849 - __main__ - INFO - Initializing database...
2025-06-30 23:16:43,850 - __main__ - INFO - Database initialized
2025-06-30 23:16:43,851 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:16:43,851 - __main__ - INFO - Initializing web server...
2025-06-30 23:16:43,911 - __main__ - INFO - Web server initialized
2025-06-30 23:16:43,912 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:16:43,912 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:16:43,936 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 23:16:43,936 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 23:16:43,937 - werkzeug - INFO -  * Restarting with stat
2025-06-30 23:16:45,148 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:16:45,149 - __main__ - INFO - Initializing database...
2025-06-30 23:16:45,150 - __main__ - INFO - Database initialized
2025-06-30 23:16:45,150 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:16:45,150 - __main__ - INFO - Initializing web server...
2025-06-30 23:16:45,207 - __main__ - INFO - Web server initialized
2025-06-30 23:16:45,207 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:16:45,207 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:16:45,222 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 23:16:45,240 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 23:16:45,256 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:45] "GET /socket.io/?EIO=4&transport=polling&t=PV0-vu1 HTTP/1.1" 200 -
2025-06-30 23:16:45,267 - web_server - INFO - Client connected
2025-06-30 23:16:45,268 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:45] "POST /socket.io/?EIO=4&transport=polling&t=PV0-vzE&sid=KZcx0JYv7Y6ZsdEVAAAA HTTP/1.1" 200 -
2025-06-30 23:16:45,272 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:45] "GET /socket.io/?EIO=4&transport=polling&t=PV0-vzF&sid=KZcx0JYv7Y6ZsdEVAAAA HTTP/1.1" 200 -
2025-06-30 23:16:45,288 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:45] "GET /socket.io/?EIO=4&transport=polling&t=PV0-vzW&sid=KZcx0JYv7Y6ZsdEVAAAA HTTP/1.1" 200 -
2025-06-30 23:16:45,823 - web_server - INFO - Client disconnected
2025-06-30 23:16:45,823 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:45] "GET /socket.io/?EIO=4&transport=websocket&sid=KZcx0JYv7Y6ZsdEVAAAA HTTP/1.1" 200 -
2025-06-30 23:16:45,837 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:45] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 23:16:45,864 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:45] "GET /setup HTTP/1.1" 200 -
2025-06-30 23:16:45,986 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:45] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 23:16:45,989 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:16:45] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 23:16:47,885 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 23:16:47,885 - discord_api - INFO - Starting quick connection...
2025-06-30 23:16:49,812 - discord_api - INFO - Loaded 3 guilds
2025-06-30 23:16:52,389 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 23:16:55,959 - discord_api - INFO - Loaded 184 channels for guild 979483208339062785
2025-06-30 23:17:02,411 - discord_api - INFO - Loaded 384 channels for guild 1149420682216747088
2025-06-30 23:17:04,101 - discord_api - INFO - Loaded 2 DM channels
2025-06-30 23:17:04,101 - discord_api - INFO - Quick connection completed: 3 guilds, 708 channels, 2 DMs
2025-06-30 23:17:04,102 - __main__ - INFO - Using quick connect mode (guilds and channels, no messages)
2025-06-30 23:17:04,103 - __main__ - INFO - Discord API client restarted successfully
2025-06-30 23:17:04,103 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:04] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 23:17:05,135 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "GET / HTTP/1.1" 200 -
2025-06-30 23:17:05,168 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 23:17:05,171 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 23:17:05,209 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0--qs HTTP/1.1" 200 -
2025-06-30 23:17:05,216 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "GET /api/status HTTP/1.1" 200 -
2025-06-30 23:17:05,223 - web_server - INFO - Client connected
2025-06-30 23:17:05,223 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "POST /socket.io/?EIO=4&transport=polling&t=PV0--q-&sid=ReQ8XN4oYfgm24IUAAAC HTTP/1.1" 200 -
2025-06-30 23:17:05,224 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0--r0&sid=ReQ8XN4oYfgm24IUAAAC HTTP/1.1" 200 -
2025-06-30 23:17:05,236 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0--rH&sid=ReQ8XN4oYfgm24IUAAAC HTTP/1.1" 200 -
2025-06-30 23:17:05,238 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "POST /api/quick-setup HTTP/1.1" 200 -
2025-06-30 23:17:05,253 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 23:17:05,264 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:05] "GET /api/dms HTTP/1.1" 200 -
2025-06-30 23:17:08,027 - web_server - INFO - No messages in database for channel 1389168782856949800, loading from Discord API...
2025-06-30 23:17:08,027 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:17:08,907 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:17:08,907 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:17:08,907 - web_server - INFO - Loaded 0 messages from Discord API for channel 1389168782856949800
2025-06-30 23:17:08,909 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:08] "GET /api/messages/1389168782856949800?page=0 HTTP/1.1" 200 -
2025-06-30 23:17:08,917 - web_server - INFO - No messages in database for channel 1389168782856949800, loading from Discord API...
2025-06-30 23:17:08,917 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:17:09,227 - web_server - INFO - No messages in database for channel 1389168782856949800, loading from Discord API...
2025-06-30 23:17:09,228 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:17:09,839 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:17:09,839 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:17:10,212 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:17:10,213 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:17:10,214 - web_server - INFO - Loaded 0 messages from Discord API for channel 1389168782856949800
2025-06-30 23:17:10,215 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:10] "GET /api/messages/1389168782856949800?page=0 HTTP/1.1" 200 -
2025-06-30 23:17:10,231 - web_server - INFO - No messages in database for channel 1389168782856949800, loading from Discord API...
2025-06-30 23:17:10,231 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:17:10,762 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:17:10,762 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:17:10,763 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:17:10,764 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:10] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:17:10,848 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:17:10,848 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:17:11,138 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:17:11,139 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:17:11,139 - web_server - INFO - Loaded 0 messages from Discord API for channel 1389168782856949800
2025-06-30 23:17:11,140 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:11] "GET /api/messages/1389168782856949800 HTTP/1.1" 200 -
2025-06-30 23:17:11,141 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:17:11,142 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:17:11,142 - web_server - INFO - Loaded 0 messages from Discord API for channel 1389168782856949800
2025-06-30 23:17:11,144 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:11] "GET /api/messages/1389168782856949800 HTTP/1.1" 200 -
2025-06-30 23:17:11,147 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:17:11,150 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:17:11,810 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:17:11,810 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:17:11,811 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:17:11,812 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:11] "GET /api/messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:17:11,817 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:17:12,107 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:17:12,108 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:17:12,108 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:12] "POST /api/load-messages/1389168782856949800 HTTP/1.1" 200 -
2025-06-30 23:17:12,116 - web_server - INFO - No messages in database for channel 1389168782856949800, loading from Discord API...
2025-06-30 23:17:12,116 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:17:12,240 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:17:12,240 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:17:12,241 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:12] "POST /api/load-messages/1389168782856949800 HTTP/1.1" 200 -
2025-06-30 23:17:12,248 - web_server - INFO - No messages in database for channel 1389168782856949800, loading from Discord API...
2025-06-30 23:17:12,248 - discord_api - INFO - Requesting 50 messages from channel 1389168782856949800
2025-06-30 23:17:12,732 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:17:12,732 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:17:12,733 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:12] "POST /api/load-messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:17:12,742 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:17:12,743 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:17:13,188 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:17:13,188 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:17:13,189 - web_server - INFO - Loaded 0 messages from Discord API for channel 1389168782856949800
2025-06-30 23:17:13,190 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:13] "GET /api/messages/1389168782856949800?page=0 HTTP/1.1" 200 -
2025-06-30 23:17:13,360 - discord_api - INFO - Discord API response for channel 1389168782856949800: 404
2025-06-30 23:17:13,360 - discord_api - WARNING - Channel 1389168782856949800 not found (404)
2025-06-30 23:17:13,360 - web_server - INFO - Loaded 0 messages from Discord API for channel 1389168782856949800
2025-06-30 23:17:13,361 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:13] "GET /api/messages/1389168782856949800?page=0 HTTP/1.1" 200 -
2025-06-30 23:17:14,852 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:17:14,853 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:17:14,853 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:17:14,854 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:17:14] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:18:44,912 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:18:44,913 - __main__ - INFO - Initializing database...
2025-06-30 23:18:44,914 - __main__ - INFO - Database initialized
2025-06-30 23:18:44,914 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:18:44,914 - __main__ - INFO - Initializing web server...
2025-06-30 23:18:44,960 - __main__ - INFO - Web server initialized
2025-06-30 23:18:44,960 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:18:44,960 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:18:44,961 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:18:44,980 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 23:18:44,980 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 23:18:44,980 - werkzeug - INFO -  * Restarting with stat
2025-06-30 23:18:46,054 - __main__ - INFO - Starting Discord Logger...
2025-06-30 23:18:46,054 - __main__ - INFO - Initializing database...
2025-06-30 23:18:46,055 - __main__ - INFO - Database initialized
2025-06-30 23:18:46,056 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 23:18:46,056 - __main__ - INFO - Initializing web server...
2025-06-30 23:18:46,100 - __main__ - INFO - Web server initialized
2025-06-30 23:18:46,100 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 23:18:46,100 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 23:18:46,101 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 23:18:46,111 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 23:18:46,121 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 23:18:49,848 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:18:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0_ONl HTTP/1.1" 200 -
2025-06-30 23:18:49,865 - web_server - INFO - Client connected
2025-06-30 23:18:49,865 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:18:49] "POST /socket.io/?EIO=4&transport=polling&t=PV0_OO2&sid=rZj6Fl0X6FRBq7INAAAA HTTP/1.1" 200 -
2025-06-30 23:18:49,873 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:18:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0_OO4&sid=rZj6Fl0X6FRBq7INAAAA HTTP/1.1" 200 -
2025-06-30 23:18:51,434 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:18:51] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 23:18:51,479 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:18:51] "GET /setup HTTP/1.1" 200 -
2025-06-30 23:18:51,673 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:18:51] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 23:18:51,675 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:18:51] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 23:18:53,458 - __main__ - INFO - Discord API connected as cozynightathome
2025-06-30 23:18:53,458 - discord_api - INFO - Starting quick connection...
2025-06-30 23:18:55,337 - discord_api - INFO - Loaded 3 guilds
2025-06-30 23:18:58,118 - discord_api - INFO - Loaded 140 channels for guild 617069298980093963
2025-06-30 23:19:02,307 - discord_api - INFO - Loaded 184 channels for guild 979483208339062785
2025-06-30 23:19:08,217 - discord_api - INFO - Loaded 384 channels for guild 1149420682216747088
2025-06-30 23:19:09,158 - discord_api - INFO - Loaded 2 DM channels
2025-06-30 23:19:09,159 - discord_api - INFO - Quick connection completed: 3 guilds, 708 channels, 2 DMs
2025-06-30 23:19:09,159 - __main__ - INFO - Using quick connect mode (guilds and channels, no messages)
2025-06-30 23:19:09,159 - __main__ - INFO - Discord API client restarted successfully
2025-06-30 23:19:09,160 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:09] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 23:19:10,236 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "GET / HTTP/1.1" 200 -
2025-06-30 23:19:10,263 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 23:19:10,263 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 23:19:10,282 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "GET /socket.io/?EIO=4&transport=polling&t=PV0_TN6 HTTP/1.1" 200 -
2025-06-30 23:19:10,283 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "GET /api/status HTTP/1.1" 200 -
2025-06-30 23:19:10,292 - web_server - INFO - Client connected
2025-06-30 23:19:10,292 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "POST /socket.io/?EIO=4&transport=polling&t=PV0_TNE&sid=5hNcp8dkygi5CWSQAAAC HTTP/1.1" 200 -
2025-06-30 23:19:10,296 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "GET /socket.io/?EIO=4&transport=polling&t=PV0_TNG&sid=5hNcp8dkygi5CWSQAAAC HTTP/1.1" 200 -
2025-06-30 23:19:10,302 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "GET /socket.io/?EIO=4&transport=polling&t=PV0_TNS&sid=5hNcp8dkygi5CWSQAAAC HTTP/1.1" 200 -
2025-06-30 23:19:10,304 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "POST /api/quick-setup HTTP/1.1" 200 -
2025-06-30 23:19:10,316 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 23:19:10,324 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:10] "GET /api/dms HTTP/1.1" 200 -
2025-06-30 23:19:16,825 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:19:16,825 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:19:17,762 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:19:17,767 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:19:17,767 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:19:17,768 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:17] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:19:17,818 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:19:17,818 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:19:18,735 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:19:18,735 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:19:18,735 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:19:18,736 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:18] "GET /api/messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:19:18,770 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:19:21,123 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:19:21,123 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:19:21,123 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:21] "POST /api/load-messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:19:21,130 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:19:21,130 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:19:22,051 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:19:22,051 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:19:22,051 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:19:22,052 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:22] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:19:27,480 - web_server - INFO - No channels in database for guild 617069298980094000, loading from Discord API...
2025-06-30 23:19:28,403 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:19:28,405 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:28] "GET /api/channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:19:29,733 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:19:29,734 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:29] "POST /api/load-channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:19:29,739 - web_server - INFO - No channels in database for guild 617069298980094000, loading from Discord API...
2025-06-30 23:19:30,890 - discord_api - ERROR - Failed to get channels for guild 617069298980094000: 404
2025-06-30 23:19:30,891 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:30] "GET /api/channels/617069298980094000 HTTP/1.1" 200 -
2025-06-30 23:19:32,443 - web_server - INFO - Client disconnected
2025-06-30 23:19:32,444 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:32] "GET /socket.io/?EIO=4&transport=websocket&sid=5hNcp8dkygi5CWSQAAAC HTTP/1.1" 200 -
2025-06-30 23:19:36,427 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:36] "GET /socket.io/?EIO=4&transport=polling&t=PV0_ZlX HTTP/1.1" 200 -
2025-06-30 23:19:36,438 - web_server - INFO - Client connected
2025-06-30 23:19:36,438 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:36] "POST /socket.io/?EIO=4&transport=polling&t=PV0_Zlo&sid=_Fe7FY5PpCAHkyyPAAAE HTTP/1.1" 200 -
2025-06-30 23:19:36,440 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:36] "GET /socket.io/?EIO=4&transport=polling&t=PV0_Zlp&sid=_Fe7FY5PpCAHkyyPAAAE HTTP/1.1" 200 -
2025-06-30 23:19:36,448 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:36] "GET /socket.io/?EIO=4&transport=polling&t=PV0_Zlz&sid=_Fe7FY5PpCAHkyyPAAAE HTTP/1.1" 200 -
2025-06-30 23:19:36,448 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:36] "POST /api/quick-setup HTTP/1.1" 200 -
2025-06-30 23:19:36,459 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:36] "GET /api/guilds HTTP/1.1" 200 -
2025-06-30 23:19:36,469 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:36] "GET /api/dms HTTP/1.1" 200 -
2025-06-30 23:19:40,085 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:19:40,085 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:19:41,011 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:19:41,011 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:19:41,012 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:19:41,013 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:41] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
2025-06-30 23:19:41,029 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:19:41,029 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:19:41,969 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:19:41,970 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:19:41,970 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:19:41,972 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:41] "GET /api/messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:19:41,977 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:19:43,312 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:19:43,312 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:19:43,313 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:43] "POST /api/load-messages/1351651394217574400 HTTP/1.1" 200 -
2025-06-30 23:19:43,320 - web_server - INFO - No messages in database for channel 1351651394217574400, loading from Discord API...
2025-06-30 23:19:43,320 - discord_api - INFO - Requesting 50 messages from channel 1351651394217574400
2025-06-30 23:19:44,201 - discord_api - INFO - Discord API response for channel 1351651394217574400: 404
2025-06-30 23:19:44,202 - discord_api - WARNING - Channel 1351651394217574400 not found (404)
2025-06-30 23:19:44,202 - web_server - INFO - Loaded 0 messages from Discord API for channel 1351651394217574400
2025-06-30 23:19:44,204 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 23:19:44] "GET /api/messages/1351651394217574400?page=0 HTTP/1.1" 200 -
