2025-06-30 22:09:24,582 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:09:24,582 - __main__ - INFO - Initializing database...
2025-06-30 22:09:24,583 - __main__ - INFO - Database initialized
2025-06-30 22:09:24,584 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:09:24,584 - __main__ - INFO - Initializing web server...
2025-06-30 22:09:24,624 - __main__ - INFO - Web server initialized
2025-06-30 22:09:24,625 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:09:24,625 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:09:24,625 - __main__ - ERROR - Critical error: The Werkzeug web server is not designed to run in production. Pass allow_unsafe_werkzeug=True to the run() method to disable this error.
2025-06-30 22:09:24,625 - __main__ - INFO - Stopping Discord Logger...
2025-06-30 22:09:24,625 - __main__ - INFO - Discord Logger stopped
2025-06-30 22:09:48,169 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:09:48,169 - __main__ - INFO - Initializing database...
2025-06-30 22:09:48,171 - __main__ - INFO - Database initialized
2025-06-30 22:09:48,171 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:09:48,171 - __main__ - INFO - Initializing web server...
2025-06-30 22:09:48,212 - __main__ - INFO - Web server initialized
2025-06-30 22:09:48,212 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:09:48,212 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:09:48,212 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:09:48,245 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:09:48,245 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:09:48,245 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:09:49,243 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:09:49,243 - __main__ - INFO - Initializing database...
2025-06-30 22:09:49,244 - __main__ - INFO - Database initialized
2025-06-30 22:09:49,244 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:09:49,244 - __main__ - INFO - Initializing web server...
2025-06-30 22:09:49,284 - __main__ - INFO - Web server initialized
2025-06-30 22:09:49,284 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:09:49,284 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:09:49,284 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:09:49,294 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:09:49,309 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:09:52,300 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:09:52,318 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:09:52,428 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 22:09:52,429 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:09:57,088 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:09:57,096 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:09:57,155 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:09:57,156 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:06,370 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:06,388 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:06,442 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:06,443 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:10,765 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:10,774 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:10,819 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:10,821 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:14,687 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:14,693 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:14,726 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:14,727 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:17,619 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:17,638 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:17,686 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:17,686 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:23,087 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:23,093 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:23,137 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:23,138 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:41,924 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:11:42,013 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:11:43,135 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:11:43,135 - __main__ - INFO - Initializing database...
2025-06-30 22:11:43,136 - __main__ - INFO - Database initialized
2025-06-30 22:11:43,136 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:11:43,137 - __main__ - INFO - Initializing web server...
2025-06-30 22:11:43,178 - __main__ - INFO - Web server initialized
2025-06-30 22:11:43,178 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:11:43,178 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:11:43,178 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:11:43,186 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:11:43,206 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:11:55,084 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:55] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:11:55,199 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:55] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:11:55,208 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:55] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:56,444 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:56] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:11:56,476 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:56] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:11:56,478 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:56] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:57,224 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:57] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:11:58,411 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET / HTTP/1.1" 200 -
2025-06-30 22:11:58,446 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:58,448 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:11:58,465 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0m51y HTTP/1.1" 200 -
2025-06-30 22:11:58,468 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:11:58,480 - web_server - INFO - Client connected
2025-06-30 22:11:58,480 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "POST /socket.io/?EIO=4&transport=polling&t=PV0m52B&sid=xHSIDXUhl3muGZ6LAAAA HTTP/1.1" 200 -
2025-06-30 22:11:58,481 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0m52D&sid=xHSIDXUhl3muGZ6LAAAA HTTP/1.1" 200 -
2025-06-30 22:11:58,489 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0m52N&sid=xHSIDXUhl3muGZ6LAAAA HTTP/1.1" 200 -
2025-06-30 22:12:04,330 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:12:04,419 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:12:05,452 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:12:05,452 - __main__ - INFO - Initializing database...
2025-06-30 22:12:05,453 - __main__ - INFO - Database initialized
2025-06-30 22:12:05,453 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:12:05,453 - __main__ - INFO - Initializing web server...
2025-06-30 22:12:05,494 - __main__ - INFO - Web server initialized
2025-06-30 22:12:05,494 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:12:05,494 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:12:05,494 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:12:05,502 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:12:05,519 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:12:05,588 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0m6nH HTTP/1.1" 200 -
2025-06-30 22:12:05,595 - web_server - INFO - Client connected
2025-06-30 22:12:05,595 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "POST /socket.io/?EIO=4&transport=polling&t=PV0m6nN&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:05,596 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0m6nO&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:05,603 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0m6nW&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:07,094 - web_server - INFO - Client disconnected
2025-06-30 22:12:07,094 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "GET /socket.io/?EIO=4&transport=websocket&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:07,106 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:12:07,119 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:12:07,220 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:12:07,220 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:12:08,571 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:08] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:12:09,342 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET / HTTP/1.1" 200 -
2025-06-30 22:12:09,383 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:12:09,384 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:12:09,433 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0m7j6 HTTP/1.1" 200 -
2025-06-30 22:12:09,443 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:12:09,455 - web_server - INFO - Client connected
2025-06-30 22:12:09,456 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "POST /socket.io/?EIO=4&transport=polling&t=PV0m7jb&sid=y64OoznonzDfoMKGAAAC HTTP/1.1" 200 -
2025-06-30 22:12:09,461 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0m7jc&sid=y64OoznonzDfoMKGAAAC HTTP/1.1" 200 -
2025-06-30 22:12:09,482 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0m7k4&sid=y64OoznonzDfoMKGAAAC HTTP/1.1" 200 -
