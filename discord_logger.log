2025-06-30 22:09:24,582 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:09:24,582 - __main__ - INFO - Initializing database...
2025-06-30 22:09:24,583 - __main__ - INFO - Database initialized
2025-06-30 22:09:24,584 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:09:24,584 - __main__ - INFO - Initializing web server...
2025-06-30 22:09:24,624 - __main__ - INFO - Web server initialized
2025-06-30 22:09:24,625 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:09:24,625 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:09:24,625 - __main__ - ERROR - Critical error: The Werkzeug web server is not designed to run in production. Pass allow_unsafe_werkzeug=True to the run() method to disable this error.
2025-06-30 22:09:24,625 - __main__ - INFO - Stopping Discord Logger...
2025-06-30 22:09:24,625 - __main__ - INFO - Discord Logger stopped
2025-06-30 22:09:48,169 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:09:48,169 - __main__ - INFO - Initializing database...
2025-06-30 22:09:48,171 - __main__ - INFO - Database initialized
2025-06-30 22:09:48,171 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:09:48,171 - __main__ - INFO - Initializing web server...
2025-06-30 22:09:48,212 - __main__ - INFO - Web server initialized
2025-06-30 22:09:48,212 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:09:48,212 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:09:48,212 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:09:48,245 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:09:48,245 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:09:48,245 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:09:49,243 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:09:49,243 - __main__ - INFO - Initializing database...
2025-06-30 22:09:49,244 - __main__ - INFO - Database initialized
2025-06-30 22:09:49,244 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:09:49,244 - __main__ - INFO - Initializing web server...
2025-06-30 22:09:49,284 - __main__ - INFO - Web server initialized
2025-06-30 22:09:49,284 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:09:49,284 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:09:49,284 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:09:49,294 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:09:49,309 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:09:52,300 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:09:52,318 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:09:52,428 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "GET /static/app.js HTTP/1.1" 200 -
2025-06-30 22:09:52,429 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:52] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:09:57,088 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:09:57,096 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:09:57,155 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:09:57,156 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:09:57] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:06,370 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:06,388 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:06,442 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:06,443 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:06] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:10,765 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:10,774 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:10,819 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:10,821 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:10] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:14,687 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:14,693 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:14,726 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:14,727 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:14] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:17,619 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:17,638 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:17,686 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:10:17,686 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:17] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:23,087 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:10:23,093 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:10:23,137 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:10:23,138 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:10:23] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:41,924 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:11:42,013 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:11:43,135 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:11:43,135 - __main__ - INFO - Initializing database...
2025-06-30 22:11:43,136 - __main__ - INFO - Database initialized
2025-06-30 22:11:43,136 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:11:43,137 - __main__ - INFO - Initializing web server...
2025-06-30 22:11:43,178 - __main__ - INFO - Web server initialized
2025-06-30 22:11:43,178 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:11:43,178 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:11:43,178 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:11:43,186 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:11:43,206 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:11:55,084 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:55] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:11:55,199 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:55] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:11:55,208 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:55] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:56,444 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:56] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:11:56,476 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:56] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:11:56,478 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:56] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:57,224 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:57] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:11:58,411 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET / HTTP/1.1" 200 -
2025-06-30 22:11:58,446 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:11:58,448 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:11:58,465 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0m51y HTTP/1.1" 200 -
2025-06-30 22:11:58,468 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:11:58,480 - web_server - INFO - Client connected
2025-06-30 22:11:58,480 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "POST /socket.io/?EIO=4&transport=polling&t=PV0m52B&sid=xHSIDXUhl3muGZ6LAAAA HTTP/1.1" 200 -
2025-06-30 22:11:58,481 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0m52D&sid=xHSIDXUhl3muGZ6LAAAA HTTP/1.1" 200 -
2025-06-30 22:11:58,489 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:11:58] "GET /socket.io/?EIO=4&transport=polling&t=PV0m52N&sid=xHSIDXUhl3muGZ6LAAAA HTTP/1.1" 200 -
2025-06-30 22:12:04,330 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:12:04,419 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:12:05,452 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:12:05,452 - __main__ - INFO - Initializing database...
2025-06-30 22:12:05,453 - __main__ - INFO - Database initialized
2025-06-30 22:12:05,453 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:12:05,453 - __main__ - INFO - Initializing web server...
2025-06-30 22:12:05,494 - __main__ - INFO - Web server initialized
2025-06-30 22:12:05,494 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:12:05,494 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:12:05,494 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:12:05,502 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:12:05,519 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:12:05,588 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0m6nH HTTP/1.1" 200 -
2025-06-30 22:12:05,595 - web_server - INFO - Client connected
2025-06-30 22:12:05,595 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "POST /socket.io/?EIO=4&transport=polling&t=PV0m6nN&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:05,596 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0m6nO&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:05,603 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0m6nW&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:07,094 - web_server - INFO - Client disconnected
2025-06-30 22:12:07,094 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "GET /socket.io/?EIO=4&transport=websocket&sid=MbHR6CA3ExE9gEhBAAAA HTTP/1.1" 200 -
2025-06-30 22:12:07,106 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:12:07,119 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:12:07,220 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:12:07,220 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:07] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:12:08,571 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:08] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:12:09,342 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET / HTTP/1.1" 200 -
2025-06-30 22:12:09,383 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:12:09,384 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:12:09,433 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0m7j6 HTTP/1.1" 200 -
2025-06-30 22:12:09,443 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:12:09,455 - web_server - INFO - Client connected
2025-06-30 22:12:09,456 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "POST /socket.io/?EIO=4&transport=polling&t=PV0m7jb&sid=y64OoznonzDfoMKGAAAC HTTP/1.1" 200 -
2025-06-30 22:12:09,461 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0m7jc&sid=y64OoznonzDfoMKGAAAC HTTP/1.1" 200 -
2025-06-30 22:12:09,482 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:09] "GET /socket.io/?EIO=4&transport=polling&t=PV0m7k4&sid=y64OoznonzDfoMKGAAAC HTTP/1.1" 200 -
2025-06-30 22:12:54,075 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:12:54,076 - __main__ - INFO - Initializing database...
2025-06-30 22:12:54,077 - __main__ - INFO - Database initialized
2025-06-30 22:12:54,077 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:12:54,077 - __main__ - INFO - Initializing web server...
2025-06-30 22:12:54,118 - __main__ - INFO - Web server initialized
2025-06-30 22:12:54,118 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:12:54,118 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:12:54,118 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:12:54,136 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:12:54,136 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:12:54,136 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:12:55,228 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:12:55,228 - __main__ - INFO - Initializing database...
2025-06-30 22:12:55,230 - __main__ - INFO - Database initialized
2025-06-30 22:12:55,230 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:12:55,230 - __main__ - INFO - Initializing web server...
2025-06-30 22:12:55,279 - __main__ - INFO - Web server initialized
2025-06-30 22:12:55,279 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:12:55,279 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:12:55,279 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:12:55,288 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:12:55,310 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:12:58,286 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:58] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:12:58,319 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:58] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:12:58,434 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:58] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:12:58,435 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:58] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:12:59,101 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:12:59,101 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:12:59,102 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:12:59,102 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:12:59,102 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:12:59] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:12:59,103 - discord.client - INFO - logging in using static token
2025-06-30 22:13:00,218 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "GET / HTTP/1.1" 200 -
2025-06-30 22:13:00,250 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:13:00,252 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:13:00,268 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "GET /socket.io/?EIO=4&transport=polling&t=PV0mK7c HTTP/1.1" 200 -
2025-06-30 22:13:00,269 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:13:00,282 - web_server - INFO - Client connected
2025-06-30 22:13:00,283 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "POST /socket.io/?EIO=4&transport=polling&t=PV0mK7s&sid=yJfgQIvvFTQB1n4eAAAA HTTP/1.1" 200 -
2025-06-30 22:13:00,285 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "GET /socket.io/?EIO=4&transport=polling&t=PV0mK7t&sid=yJfgQIvvFTQB1n4eAAAA HTTP/1.1" 200 -
2025-06-30 22:13:00,291 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:00] "GET /socket.io/?EIO=4&transport=polling&t=PV0mK82&sid=yJfgQIvvFTQB1n4eAAAA HTTP/1.1" 200 -
2025-06-30 22:13:01,165 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:13:12,246 - web_server - INFO - Client disconnected
2025-06-30 22:13:12,246 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET /socket.io/?EIO=4&transport=websocket&sid=yJfgQIvvFTQB1n4eAAAA HTTP/1.1" 200 -
2025-06-30 22:13:12,257 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET / HTTP/1.1" 200 -
2025-06-30 22:13:12,298 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:13:12,301 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:13:12,316 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mN3v HTTP/1.1" 200 -
2025-06-30 22:13:12,325 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:13:12,333 - web_server - INFO - Client connected
2025-06-30 22:13:12,334 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "POST /socket.io/?EIO=4&transport=polling&t=PV0mN46&sid=I3grbkGuPFdmVpuQAAAC HTTP/1.1" 200 -
2025-06-30 22:13:12,334 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mN46.0&sid=I3grbkGuPFdmVpuQAAAC HTTP/1.1" 200 -
2025-06-30 22:13:12,344 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mN4L&sid=I3grbkGuPFdmVpuQAAAC HTTP/1.1" 200 -
2025-06-30 22:13:15,116 - web_server - INFO - Client disconnected
2025-06-30 22:13:15,117 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET /socket.io/?EIO=4&transport=websocket&sid=I3grbkGuPFdmVpuQAAAC HTTP/1.1" 200 -
2025-06-30 22:13:15,120 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET / HTTP/1.1" 200 -
2025-06-30 22:13:15,154 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:13:15,156 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:13:15,172 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET /socket.io/?EIO=4&transport=polling&t=PV0mNmX HTTP/1.1" 200 -
2025-06-30 22:13:15,181 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:13:15,190 - web_server - INFO - Client connected
2025-06-30 22:13:15,190 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "POST /socket.io/?EIO=4&transport=polling&t=PV0mNmm&sid=M1aCD6JvcJH_c7CKAAAE HTTP/1.1" 200 -
2025-06-30 22:13:15,191 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET /socket.io/?EIO=4&transport=polling&t=PV0mNmn&sid=M1aCD6JvcJH_c7CKAAAE HTTP/1.1" 200 -
2025-06-30 22:13:15,199 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:13:15] "GET /socket.io/?EIO=4&transport=polling&t=PV0mNmy&sid=M1aCD6JvcJH_c7CKAAAE HTTP/1.1" 200 -
2025-06-30 22:14:03,738 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:14:03,828 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:14:04,924 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:14:04,924 - __main__ - INFO - Initializing database...
2025-06-30 22:14:04,925 - __main__ - INFO - Database initialized
2025-06-30 22:14:04,925 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:14:04,925 - __main__ - INFO - Initializing web server...
2025-06-30 22:14:04,966 - __main__ - INFO - Web server initialized
2025-06-30 22:14:04,966 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:14:04,966 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:14:04,966 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:14:04,975 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:14:04,989 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:14:05,000 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0mZtn HTTP/1.1" 200 -
2025-06-30 22:14:05,009 - web_server - INFO - Client connected
2025-06-30 22:14:05,010 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:05] "POST /socket.io/?EIO=4&transport=polling&t=PV0mZxC&sid=WZVkoTRmn7aULX5aAAAA HTTP/1.1" 200 -
2025-06-30 22:14:05,010 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0mZxD&sid=WZVkoTRmn7aULX5aAAAA HTTP/1.1" 200 -
2025-06-30 22:14:05,017 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:05] "GET /socket.io/?EIO=4&transport=polling&t=PV0mZxN&sid=WZVkoTRmn7aULX5aAAAA HTTP/1.1" 200 -
2025-06-30 22:14:19,080 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:14:19,171 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:14:20,213 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:14:20,213 - __main__ - INFO - Initializing database...
2025-06-30 22:14:20,214 - __main__ - INFO - Database initialized
2025-06-30 22:14:20,214 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:14:20,215 - __main__ - INFO - Initializing web server...
2025-06-30 22:14:20,257 - __main__ - INFO - Web server initialized
2025-06-30 22:14:20,258 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:14:20,258 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:14:20,258 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:14:20,268 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:14:20,283 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:14:20,437 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:20] "GET /socket.io/?EIO=4&transport=polling&t=PV0mdiG HTTP/1.1" 200 -
2025-06-30 22:14:20,449 - web_server - INFO - Client connected
2025-06-30 22:14:20,450 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:20] "POST /socket.io/?EIO=4&transport=polling&t=PV0mdiQ&sid=BOeqK-bq0eXvoG7vAAAA HTTP/1.1" 200 -
2025-06-30 22:14:20,452 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:20] "GET /socket.io/?EIO=4&transport=polling&t=PV0mdiS&sid=BOeqK-bq0eXvoG7vAAAA HTTP/1.1" 200 -
2025-06-30 22:14:20,464 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:20] "GET /socket.io/?EIO=4&transport=polling&t=PV0mdih&sid=BOeqK-bq0eXvoG7vAAAA HTTP/1.1" 200 -
2025-06-30 22:14:22,200 - web_server - INFO - Client disconnected
2025-06-30 22:14:22,201 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:22] "GET /socket.io/?EIO=4&transport=websocket&sid=BOeqK-bq0eXvoG7vAAAA HTTP/1.1" 200 -
2025-06-30 22:14:22,217 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:22] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:14:22,252 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:22] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:14:22,384 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:22] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:14:22,384 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:22] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:14:23,370 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:14:23,371 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:14:23,372 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:14:23,372 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:14:23,372 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:23] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:14:23,373 - discord.client - INFO - logging in using static token
2025-06-30 22:14:24,270 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:14:24,503 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "GET / HTTP/1.1" 200 -
2025-06-30 22:14:24,540 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:14:24,541 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:14:24,554 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "GET /socket.io/?EIO=4&transport=polling&t=PV0meic HTTP/1.1" 200 -
2025-06-30 22:14:24,555 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:14:24,570 - web_server - INFO - Client connected
2025-06-30 22:14:24,570 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "POST /socket.io/?EIO=4&transport=polling&t=PV0meip&sid=NZPAetIgF3NMBtvRAAAC HTTP/1.1" 200 -
2025-06-30 22:14:24,570 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "GET /socket.io/?EIO=4&transport=polling&t=PV0meir&sid=NZPAetIgF3NMBtvRAAAC HTTP/1.1" 200 -
2025-06-30 22:14:24,579 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:24] "GET /socket.io/?EIO=4&transport=polling&t=PV0mej1&sid=NZPAetIgF3NMBtvRAAAC HTTP/1.1" 200 -
2025-06-30 22:14:31,367 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\web_server.py', reloading
2025-06-30 22:14:31,478 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:14:32,541 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:14:32,541 - __main__ - INFO - Initializing database...
2025-06-30 22:14:32,542 - __main__ - INFO - Database initialized
2025-06-30 22:14:32,542 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:14:32,543 - __main__ - INFO - Initializing web server...
2025-06-30 22:14:32,584 - __main__ - INFO - Web server initialized
2025-06-30 22:14:32,584 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:14:32,584 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:14:32,585 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:14:32,594 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:14:32,610 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:14:32,620 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:32] "GET /socket.io/?EIO=4&transport=polling&t=PV0mgZQ HTTP/1.1" 200 -
2025-06-30 22:14:32,628 - web_server - INFO - Client connected
2025-06-30 22:14:32,629 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:32] "POST /socket.io/?EIO=4&transport=polling&t=PV0mggm&sid=89xVfJdV4luDL4A5AAAA HTTP/1.1" 200 -
2025-06-30 22:14:32,630 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:32] "GET /socket.io/?EIO=4&transport=polling&t=PV0mggn&sid=89xVfJdV4luDL4A5AAAA HTTP/1.1" 200 -
2025-06-30 22:14:32,637 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:32] "GET /socket.io/?EIO=4&transport=polling&t=PV0mggx&sid=89xVfJdV4luDL4A5AAAA HTTP/1.1" 200 -
2025-06-30 22:14:37,715 - web_server - INFO - Client disconnected
2025-06-30 22:14:37,715 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:37] "GET /socket.io/?EIO=4&transport=websocket&sid=89xVfJdV4luDL4A5AAAA HTTP/1.1" 200 -
2025-06-30 22:14:37,723 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:37] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:14:37,741 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:37] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:14:37,838 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:37] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:14:37,839 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:37] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:14:38,881 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:14:38,881 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:14:38,882 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:14:38,882 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:14:38,882 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:38] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:14:38,883 - discord.client - INFO - logging in using static token
2025-06-30 22:14:39,806 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:14:39,991 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:39] "GET / HTTP/1.1" 200 -
2025-06-30 22:14:40,035 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:14:40,040 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:14:40,056 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "GET /socket.io/?EIO=4&transport=polling&t=PV0miUm HTTP/1.1" 200 -
2025-06-30 22:14:40,062 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:14:40,067 - web_server - INFO - Client connected
2025-06-30 22:14:40,068 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "POST /socket.io/?EIO=4&transport=polling&t=PV0miU-&sid=ARu5LBcOs2frUi4SAAAC HTTP/1.1" 200 -
2025-06-30 22:14:40,071 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "GET /socket.io/?EIO=4&transport=polling&t=PV0miV2&sid=ARu5LBcOs2frUi4SAAAC HTTP/1.1" 200 -
2025-06-30 22:14:40,077 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:14:40] "GET /socket.io/?EIO=4&transport=polling&t=PV0miVD&sid=ARu5LBcOs2frUi4SAAAC HTTP/1.1" 200 -
2025-06-30 22:15:06,951 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:15:06,952 - __main__ - INFO - Initializing database...
2025-06-30 22:15:06,953 - __main__ - INFO - Database initialized
2025-06-30 22:15:06,953 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:15:06,953 - __main__ - INFO - Initializing web server...
2025-06-30 22:15:07,001 - __main__ - INFO - Web server initialized
2025-06-30 22:15:07,001 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:15:07,001 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:15:07,001 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:15:07,021 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:15:07,021 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:15:07,021 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:15:08,024 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:15:08,024 - __main__ - INFO - Initializing database...
2025-06-30 22:15:08,025 - __main__ - INFO - Database initialized
2025-06-30 22:15:08,025 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:15:08,025 - __main__ - INFO - Initializing web server...
2025-06-30 22:15:08,066 - __main__ - INFO - Web server initialized
2025-06-30 22:15:08,066 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:15:08,066 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:15:08,066 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:15:08,076 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:15:08,088 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:15:09,828 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:09] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:15:09,848 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:09] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:15:09,957 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:09] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:15:09,958 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:09] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:15:10,980 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:15:10,980 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:15:10,980 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:15:10,981 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:15:10,981 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:10] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:15:10,982 - discord.client - INFO - logging in using static token
2025-06-30 22:15:12,061 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:15:12,225 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "GET / HTTP/1.1" 200 -
2025-06-30 22:15:12,263 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:15:12,264 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:15:12,282 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mqML HTTP/1.1" 200 -
2025-06-30 22:15:12,284 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:15:12,302 - web_server - INFO - Client connected
2025-06-30 22:15:12,303 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "POST /socket.io/?EIO=4&transport=polling&t=PV0mqMY&sid=fP6RjOusIQAaRlLoAAAA HTTP/1.1" 200 -
2025-06-30 22:15:12,304 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mqMa&sid=fP6RjOusIQAaRlLoAAAA HTTP/1.1" 200 -
2025-06-30 22:15:12,315 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:15:12] "GET /socket.io/?EIO=4&transport=polling&t=PV0mqMs&sid=fP6RjOusIQAaRlLoAAAA HTTP/1.1" 200 -
2025-06-30 22:16:40,620 - web_server - INFO - Client disconnected
2025-06-30 22:16:40,620 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:40] "GET /socket.io/?EIO=4&transport=websocket&sid=fP6RjOusIQAaRlLoAAAA HTTP/1.1" 200 -
2025-06-30 22:16:40,632 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:40] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:16:40,675 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:40] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:16:40,677 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:40] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:16:47,695 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:16:47,695 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:16:47,695 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:16:47,696 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:47] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:16:47,696 - discord.client - INFO - logging in using static token
2025-06-30 22:16:48,953 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:48] "GET / HTTP/1.1" 200 -
2025-06-30 22:16:48,980 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:16:48,989 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:48] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:16:48,992 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:48] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:16:49,006 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0nBzg HTTP/1.1" 200 -
2025-06-30 22:16:49,007 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:49] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:16:49,022 - web_server - INFO - Client connected
2025-06-30 22:16:49,022 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:49] "POST /socket.io/?EIO=4&transport=polling&t=PV0nBzu&sid=C2mQoBh4ommG4p_gAAAC HTTP/1.1" 200 -
2025-06-30 22:16:49,023 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0nBzv&sid=C2mQoBh4ommG4p_gAAAC HTTP/1.1" 200 -
2025-06-30 22:16:49,030 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:16:49] "GET /socket.io/?EIO=4&transport=polling&t=PV0nB-4&sid=C2mQoBh4ommG4p_gAAAC HTTP/1.1" 200 -
2025-06-30 22:17:14,845 - web_server - INFO - Client disconnected
2025-06-30 22:17:14,845 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET /socket.io/?EIO=4&transport=websocket&sid=C2mQoBh4ommG4p_gAAAC HTTP/1.1" 200 -
2025-06-30 22:17:14,854 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET / HTTP/1.1" 200 -
2025-06-30 22:17:14,889 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:17:14,890 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:17:14,906 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET /socket.io/?EIO=4&transport=polling&t=PV0nIIM HTTP/1.1" 200 -
2025-06-30 22:17:14,913 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:17:14,921 - web_server - INFO - Client connected
2025-06-30 22:17:14,922 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "POST /socket.io/?EIO=4&transport=polling&t=PV0nIIc&sid=I3YuQKlr4v_uOq3-AAAE HTTP/1.1" 200 -
2025-06-30 22:17:14,922 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET /socket.io/?EIO=4&transport=polling&t=PV0nIIc.0&sid=I3YuQKlr4v_uOq3-AAAE HTTP/1.1" 200 -
2025-06-30 22:17:14,931 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:17:14] "GET /socket.io/?EIO=4&transport=polling&t=PV0nIIn&sid=I3YuQKlr4v_uOq3-AAAE HTTP/1.1" 200 -
2025-06-30 22:20:49,823 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:20:49,823 - __main__ - INFO - Initializing database...
2025-06-30 22:20:49,824 - __main__ - INFO - Database initialized
2025-06-30 22:20:49,824 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:20:49,825 - __main__ - INFO - Initializing web server...
2025-06-30 22:20:49,868 - __main__ - INFO - Web server initialized
2025-06-30 22:20:49,868 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:20:49,868 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:20:49,868 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:20:49,885 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:20:49,885 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:20:49,885 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:20:50,893 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:20:50,894 - __main__ - INFO - Initializing database...
2025-06-30 22:20:50,895 - __main__ - INFO - Database initialized
2025-06-30 22:20:50,895 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:20:50,895 - __main__ - INFO - Initializing web server...
2025-06-30 22:20:50,936 - __main__ - INFO - Web server initialized
2025-06-30 22:20:50,936 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:20:50,936 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:20:50,937 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:20:50,946 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:20:50,961 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:20:52,895 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:20:52] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:20:53,009 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:20:53] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:20:53,010 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:20:53] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:21:09,657 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:21:10,694 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:10] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:21:14,292 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:21:15,551 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:15] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:21:24,732 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:21:25,613 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:25] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:21:29,206 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:21:30,285 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:30] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:21:38,726 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:21:38,726 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:21:38,726 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:21:38,727 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:21:38,727 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:38] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:21:38,727 - discord.client - INFO - logging in using static token
2025-06-30 22:21:39,465 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "GET / HTTP/1.1" 200 -
2025-06-30 22:21:39,547 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:21:39,547 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:21:39,566 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "GET /socket.io/?EIO=4&transport=polling&t=PV0oIvd HTTP/1.1" 200 -
2025-06-30 22:21:39,571 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:21:39,579 - web_server - INFO - Client connected
2025-06-30 22:21:39,579 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "POST /socket.io/?EIO=4&transport=polling&t=PV0oIvp&sid=Vsvu2Tsz6zzqHJ0JAAAA HTTP/1.1" 200 -
2025-06-30 22:21:39,583 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "GET /socket.io/?EIO=4&transport=polling&t=PV0oIvq&sid=Vsvu2Tsz6zzqHJ0JAAAA HTTP/1.1" 200 -
2025-06-30 22:21:39,592 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:39] "GET /socket.io/?EIO=4&transport=polling&t=PV0oIw3&sid=Vsvu2Tsz6zzqHJ0JAAAA HTTP/1.1" 200 -
2025-06-30 22:21:40,125 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:21:59,710 - web_server - INFO - Client disconnected
2025-06-30 22:21:59,711 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:21:59] "GET /socket.io/?EIO=4&transport=websocket&sid=Vsvu2Tsz6zzqHJ0JAAAA HTTP/1.1" 200 -
2025-06-30 22:22:15,245 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:22:15] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:22:15,281 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:22:15] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:22:15,282 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:22:15] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:22:21,927 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:22:22,804 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:22:22] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:22:53,147 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:22:53,992 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:22:53] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:24:50,244 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:24:50,244 - __main__ - INFO - Initializing database...
2025-06-30 22:24:50,245 - __main__ - INFO - Database initialized
2025-06-30 22:24:50,246 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:24:50,246 - __main__ - INFO - Initializing web server...
2025-06-30 22:24:50,286 - __main__ - INFO - Web server initialized
2025-06-30 22:24:50,288 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:24:50,288 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:24:50,309 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:24:50,309 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:24:50,309 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:24:51,262 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:24:51,262 - __main__ - INFO - Initializing database...
2025-06-30 22:24:51,264 - __main__ - INFO - Database initialized
2025-06-30 22:24:51,264 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:24:51,264 - __main__ - INFO - Initializing web server...
2025-06-30 22:24:51,304 - __main__ - INFO - Web server initialized
2025-06-30 22:24:51,304 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:24:51,304 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:24:51,315 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:24:51,328 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:24:59,071 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:24:59] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:24:59,101 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:24:59] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:24:59,212 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:24:59] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:24:59,212 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:24:59] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:25:17,830 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:25:19,353 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:25:19] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:26:21,818 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:26:21,939 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:26:23,174 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:26:23,174 - __main__ - INFO - Initializing database...
2025-06-30 22:26:23,176 - __main__ - INFO - Database initialized
2025-06-30 22:26:23,176 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:26:23,176 - __main__ - INFO - Initializing web server...
2025-06-30 22:26:23,222 - __main__ - INFO - Web server initialized
2025-06-30 22:26:23,223 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:26:23,223 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:26:23,235 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:26:23,258 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:27:38,119 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:27:38,119 - __main__ - INFO - Initializing database...
2025-06-30 22:27:38,121 - __main__ - INFO - Database initialized
2025-06-30 22:27:38,121 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:27:38,121 - __main__ - INFO - Initializing web server...
2025-06-30 22:27:38,163 - __main__ - INFO - Web server initialized
2025-06-30 22:27:38,163 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:27:38,163 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:27:38,163 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:27:38,180 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:27:38,180 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:27:38,181 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:27:39,255 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:27:39,255 - __main__ - INFO - Initializing database...
2025-06-30 22:27:39,257 - __main__ - INFO - Database initialized
2025-06-30 22:27:39,257 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:27:39,257 - __main__ - INFO - Initializing web server...
2025-06-30 22:27:39,323 - __main__ - INFO - Web server initialized
2025-06-30 22:27:39,323 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:27:39,323 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:27:39,324 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:27:39,341 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:27:39,365 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:27:41,612 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:27:41] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 22:27:41,645 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:27:41] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:27:41,772 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:27:41] "GET /static/style.css HTTP/1.1" 200 -
2025-06-30 22:27:41,773 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:27:41] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:28:03,043 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:28:04,147 - __main__ - INFO - Login response status: 400
2025-06-30 22:28:04,148 - __main__ - INFO - Login response: {"message": "Invalid Form Body", "code": 50035, "errors": {"login": {"_errors": [{"code": "BASE_TYPE_REQUIRED", "message": "This field is required"}]}}}...
2025-06-30 22:28:04,148 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:04] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:28:09,478 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:28:09,479 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-30 22:28:09,479 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:28:09,480 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:28:09,480 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:09] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:28:09,481 - discord.client - INFO - logging in using static token
2025-06-30 22:28:11,338 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:28:11,721 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "GET / HTTP/1.1" 200 -
2025-06-30 22:28:11,778 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:28:11,780 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:28:11,810 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "GET /socket.io/?EIO=4&transport=polling&t=PV0pogK HTTP/1.1" 200 -
2025-06-30 22:28:11,815 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:28:11,820 - web_server - INFO - Client connected
2025-06-30 22:28:11,821 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "POST /socket.io/?EIO=4&transport=polling&t=PV0pogd&sid=NT36N18AFZ0eRZhTAAAA HTTP/1.1" 200 -
2025-06-30 22:28:11,828 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:11] "GET /socket.io/?EIO=4&transport=polling&t=PV0poge&sid=NT36N18AFZ0eRZhTAAAA HTTP/1.1" 200 -
2025-06-30 22:28:14,931 - web_server - INFO - Client disconnected
2025-06-30 22:28:14,931 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:14] "GET /socket.io/?EIO=4&transport=websocket&sid=NT36N18AFZ0eRZhTAAAA HTTP/1.1" 200 -
2025-06-30 22:28:19,462 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:19] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:28:19,492 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:19] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:28:19,495 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:19] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:28:21,364 - __main__ - INFO - Initializing Discord client...
2025-06-30 22:28:21,365 - __main__ - INFO - Discord client started in separate thread
2025-06-30 22:28:21,365 - __main__ - INFO - Discord client restarted successfully
2025-06-30 22:28:21,365 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:21] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:28:21,365 - discord.client - INFO - logging in using static token
2025-06-30 22:28:22,240 - __main__ - ERROR - Discord client error: Improper token has been passed.
2025-06-30 22:28:22,409 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "GET / HTTP/1.1" 200 -
2025-06-30 22:28:22,487 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:28:22,489 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:28:22,505 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "GET /socket.io/?EIO=4&transport=polling&t=PV0prHc HTTP/1.1" 200 -
2025-06-30 22:28:22,518 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "GET /api/status HTTP/1.1" 200 -
2025-06-30 22:28:22,529 - web_server - INFO - Client connected
2025-06-30 22:28:22,532 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "POST /socket.io/?EIO=4&transport=polling&t=PV0prHp&sid=nIFlgbHyuS26BwdiAAAC HTTP/1.1" 200 -
2025-06-30 22:28:22,534 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "GET /socket.io/?EIO=4&transport=polling&t=PV0prHw&sid=nIFlgbHyuS26BwdiAAAC HTTP/1.1" 200 -
2025-06-30 22:28:22,559 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:22] "GET /socket.io/?EIO=4&transport=polling&t=PV0prIN&sid=nIFlgbHyuS26BwdiAAAC HTTP/1.1" 200 -
2025-06-30 22:28:24,368 - web_server - INFO - Client disconnected
2025-06-30 22:28:24,369 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:24] "GET /socket.io/?EIO=4&transport=websocket&sid=nIFlgbHyuS26BwdiAAAC HTTP/1.1" 200 -
2025-06-30 22:28:36,861 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:36] "GET /setup HTTP/1.1" 200 -
2025-06-30 22:28:36,897 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:36] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-30 22:28:36,900 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:36] "[36mGET /static/app.js HTTP/1.1[0m" 304 -
2025-06-30 22:28:42,173 - __main__ - INFO - Attempting to login with email and password...
2025-06-30 22:28:43,284 - __main__ - INFO - Login response status: 400
2025-06-30 22:28:43,284 - __main__ - INFO - Login response: {"message": "Invalid Form Body", "code": 50035, "errors": {"login": {"_errors": [{"code": "BASE_TYPE_REQUIRED", "message": "This field is required"}]}}}...
2025-06-30 22:28:43,285 - werkzeug - INFO - 127.0.0.1 - - [30/Jun/2025 22:28:43] "POST /api/setup HTTP/1.1" 200 -
2025-06-30 22:29:11,931 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:29:11,955 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<_wait_for_close() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\aiohttp\connector.py:136>>
2025-06-30 22:29:12,074 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:29:13,295 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:29:13,297 - __main__ - INFO - Initializing database...
2025-06-30 22:29:13,298 - __main__ - INFO - Database initialized
2025-06-30 22:29:13,298 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:29:13,298 - __main__ - INFO - Initializing web server...
2025-06-30 22:29:13,342 - __main__ - INFO - Web server initialized
2025-06-30 22:29:13,342 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:29:13,342 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:29:13,342 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:29:13,354 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:29:13,373 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:29:29,475 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:29:29,569 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:29:30,660 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:29:30,660 - __main__ - INFO - Initializing database...
2025-06-30 22:29:30,662 - __main__ - INFO - Database initialized
2025-06-30 22:29:30,662 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:29:30,662 - __main__ - INFO - Initializing web server...
2025-06-30 22:29:30,707 - __main__ - INFO - Web server initialized
2025-06-30 22:29:30,707 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:29:30,707 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:29:30,707 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:29:30,716 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:29:30,732 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:29:55,892 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\Discord\\main.py', reloading
2025-06-30 22:29:56,071 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:29:57,323 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:29:57,323 - __main__ - INFO - Initializing database...
2025-06-30 22:29:57,324 - __main__ - INFO - Database initialized
2025-06-30 22:29:57,324 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:29:57,324 - __main__ - INFO - Initializing web server...
2025-06-30 22:29:57,372 - __main__ - INFO - Web server initialized
2025-06-30 22:29:57,372 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:29:57,372 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:29:57,372 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:29:57,383 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:29:57,397 - werkzeug - INFO -  * Debugger PIN: 142-463-969
2025-06-30 22:30:18,135 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:30:18,135 - __main__ - INFO - Initializing database...
2025-06-30 22:30:18,136 - __main__ - INFO - Database initialized
2025-06-30 22:30:18,136 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:30:18,136 - __main__ - INFO - Initializing web server...
2025-06-30 22:30:18,179 - __main__ - INFO - Web server initialized
2025-06-30 22:30:18,179 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:30:18,179 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:30:18,180 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:30:18,198 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 22:30:18,199 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 22:30:18,199 - werkzeug - INFO -  * Restarting with stat
2025-06-30 22:30:19,645 - __main__ - INFO - Starting Discord Logger...
2025-06-30 22:30:19,645 - __main__ - INFO - Initializing database...
2025-06-30 22:30:19,646 - __main__ - INFO - Database initialized
2025-06-30 22:30:19,646 - __main__ - WARNING - Discord token not found. Starting web server only...
2025-06-30 22:30:19,646 - __main__ - INFO - Initializing web server...
2025-06-30 22:30:19,690 - __main__ - INFO - Web server initialized
2025-06-30 22:30:19,690 - __main__ - INFO - Starting web server on http://127.0.0.1:5000
2025-06-30 22:30:19,690 - web_server - INFO - Starting web server on 127.0.0.1:5000
2025-06-30 22:30:19,690 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
2025-06-30 22:30:19,700 - werkzeug - WARNING -  * Debugger is active!
2025-06-30 22:30:19,717 - werkzeug - INFO -  * Debugger PIN: 142-463-969
