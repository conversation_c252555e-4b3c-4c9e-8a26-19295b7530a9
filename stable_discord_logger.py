#!/usr/bin/env python3
"""
Stable Discord Logger - Ultra-fast and reliable
Maximum speed, minimum complexity
"""

import requests
import sqlite3
import json
import time
import threading
from datetime import datetime
from flask import Flask, request, jsonify

# Global variables
app = Flask(__name__)
DISCORD_TOKEN = None
BASE_URL = "https://discord.com/api/v9"
SCAN_ACTIVE = False

def init_db():
    """Initialize database"""
    conn = sqlite3.connect('stable_discord.db')
    cursor = conn.cursor()
    
    cursor.execute('''CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY, channel_id TEXT, author TEXT, content TEXT, 
        timestamp TEXT, created_at TEXT DEFAULT CURRENT_TIMESTAMP)''')
    
    cursor.execute('''CREATE TABLE IF NOT EXISTS channels (
        id TEXT PRIMARY KEY, guild_id TEXT, name TEXT, message_count INTEGER DEFAULT 0)''')
    
    conn.commit()
    conn.close()

def test_token(token):
    """Test Discord token"""
    try:
        response = requests.get(f"{BASE_URL}/users/@me", 
                              headers={"Authorization": token}, timeout=5)
        if response.status_code == 200:
            return {"success": True, "user": response.json()}
        return {"success": False, "error": f"HTTP {response.status_code}"}
    except Exception as e:
        return {"success": False, "error": str(e)}

def get_guilds(token):
    """Get guilds"""
    try:
        response = requests.get(f"{BASE_URL}/users/@me/guilds", 
                              headers={"Authorization": token}, timeout=5)
        if response.status_code == 200:
            return response.json()
        return []
    except:
        return []

def get_channels(token, guild_id):
    """Get channels"""
    try:
        response = requests.get(f"{BASE_URL}/guilds/{guild_id}/channels", 
                              headers={"Authorization": token}, timeout=5)
        if response.status_code == 200:
            channels = [ch for ch in response.json() if ch['type'] == 0]
            
            # Update database
            conn = sqlite3.connect('stable_discord.db')
            cursor = conn.cursor()
            for ch in channels:
                cursor.execute('INSERT OR REPLACE INTO channels (id, guild_id, name) VALUES (?, ?, ?)',
                             (ch['id'], guild_id, ch['name']))
            conn.commit()
            conn.close()
            
            return channels
        return get_cached_channels(guild_id)
    except:
        return get_cached_channels(guild_id)

def get_cached_channels(guild_id):
    """Get cached channels"""
    try:
        conn = sqlite3.connect('stable_discord.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM channels WHERE guild_id = ?', (guild_id,))
        channels = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return channels
    except:
        return []

def get_messages(token, channel_id):
    """Get messages"""
    try:
        response = requests.get(f"{BASE_URL}/channels/{channel_id}/messages?limit=50", 
                              headers={"Authorization": token}, timeout=5)
        if response.status_code == 200:
            messages = response.json()
            
            # Save to database
            conn = sqlite3.connect('stable_discord.db')
            cursor = conn.cursor()
            for msg in messages:
                cursor.execute('''INSERT OR REPLACE INTO messages 
                                (id, channel_id, author, content, timestamp) VALUES (?, ?, ?, ?, ?)''',
                             (msg['id'], channel_id, msg['author']['username'], 
                              msg['content'], msg['timestamp']))
            
            # Update channel count
            cursor.execute('UPDATE channels SET message_count = ? WHERE id = ?', 
                         (len(messages), channel_id))
            conn.commit()
            conn.close()
            
            return get_cached_messages(channel_id)
        return get_cached_messages(channel_id)
    except:
        return get_cached_messages(channel_id)

def get_cached_messages(channel_id):
    """Get cached messages"""
    try:
        conn = sqlite3.connect('stable_discord.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM messages WHERE channel_id = ? ORDER BY timestamp DESC', 
                     (channel_id,))
        messages = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return messages
    except:
        return []

def ultra_fast_scan():
    """Ultra-fast background scanning"""
    global SCAN_ACTIVE
    while SCAN_ACTIVE:
        try:
            if DISCORD_TOKEN:
                # Get all channels
                conn = sqlite3.connect('stable_discord.db')
                cursor = conn.cursor()
                cursor.execute('SELECT id FROM channels LIMIT 10')  # Scan top 10 channels
                channels = cursor.fetchall()
                conn.close()
                
                for (channel_id,) in channels:
                    get_messages(DISCORD_TOKEN, channel_id)
                    time.sleep(0.1)  # 100ms between channels
            
            time.sleep(0.2)  # 200ms scan interval - ULTRA FAST!
        except:
            time.sleep(1)

# HTML Template - Minimal and fast
HTML = '''<!DOCTYPE html>
<html>
<head>
    <title>Stable Discord Logger</title>
    <meta charset="UTF-8">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: white; display: flex; height: 100vh; }
        .sidebar { width: 300px; background: #2c2c2c; padding: 15px; overflow-y: auto; border-right: 1px solid #444; }
        .main { flex: 1; display: flex; flex-direction: column; }
        .header { height: 60px; background: #333; padding: 15px; border-bottom: 1px solid #444; display: flex; align-items: center; justify-content: space-between; }
        .content { flex: 1; padding: 15px; overflow-y: auto; }
        .item { padding: 10px; margin: 5px 0; background: #333; border-radius: 5px; cursor: pointer; display: flex; justify-content: space-between; }
        .item:hover { background: #444; }
        .item.active { background: #5865f2; }
        .item.has-messages { border-left: 3px solid #faa61a; }
        .message { margin: 10px 0; padding: 10px; background: #333; border-radius: 5px; }
        .message-author { font-weight: bold; color: #5865f2; margin-bottom: 5px; }
        .message-content { color: #ddd; }
        .btn { padding: 8px 15px; background: #5865f2; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 2px; }
        .btn:hover { background: #4752c4; }
        .btn-success { background: #3ba55d; }
        .btn-danger { background: #ed4245; }
        .setup { max-width: 400px; margin: 100px auto; padding: 30px; background: #2c2c2c; border-radius: 10px; }
        .setup input { width: 100%; padding: 10px; margin: 10px 0; background: #333; border: 1px solid #555; color: white; border-radius: 5px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .status.success { background: rgba(59, 165, 93, 0.2); color: #3ba55d; }
        .status.error { background: rgba(237, 66, 69, 0.2); color: #ed4245; }
        .count { background: #555; padding: 2px 8px; border-radius: 10px; font-size: 11px; }
        .scanning { background: #3ba55d !important; animation: pulse 1s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } }
    </style>
</head>
<body>
    <div id="app">
        <div class="setup" id="setup-screen">
            <h2>Stable Discord Logger</h2>
            <p>Ultra-fast and reliable Discord monitoring</p>
            <input type="password" id="token-input" placeholder="Discord Token">
            <button class="btn" onclick="setupToken()">Connect</button>
            <div id="setup-status"></div>
        </div>
        
        <div id="main-screen" style="display: none; width: 100%; display: flex;">
            <div class="sidebar">
                <h3>Servers</h3>
                <button class="btn" onclick="loadGuilds()" style="width: 100%; margin-bottom: 10px;">Refresh</button>
                <div id="guilds-list"></div>
                
                <h3 style="margin-top: 20px;">Channels</h3>
                <div id="channels-list">Select a server</div>
            </div>
            
            <div class="main">
                <div class="header">
                    <h2 id="current-title">Stable Discord Logger</h2>
                    <div>
                        <button class="btn btn-success" onclick="toggleScan()" id="scan-btn">Start ULTRA-FAST</button>
                        <button class="btn" onclick="scanGuild()" id="manual-scan-btn" disabled>Manual Scan</button>
                    </div>
                </div>
                
                <div class="content">
                    <div id="messages-container">
                        <h2>Welcome to Stable Discord Logger</h2>
                        <p>Ultra-fast Discord monitoring with 200ms updates</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentGuild = null;
        let currentChannel = null;
        let scanActive = false;
        let refreshInterval = null;

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('setup-status');
            statusEl.innerHTML = '<div class="status ' + type + '">' + message + '</div>';
        }

        async function setupToken() {
            const token = document.getElementById('token-input').value.trim();
            if (!token) return;

            try {
                const response = await fetch('/api/setup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token: token })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('Connected as ' + data.user.username, 'success');
                    document.getElementById('setup-screen').style.display = 'none';
                    document.getElementById('main-screen').style.display = 'flex';
                    loadGuilds();
                    startAutoRefresh();
                } else {
                    showStatus('Error: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('Connection failed: ' + error.message, 'error');
            }
        }

        async function loadGuilds() {
            try {
                const response = await fetch('/api/guilds');
                const data = await response.json();
                
                if (data.success) {
                    const container = document.getElementById('guilds-list');
                    container.innerHTML = '';
                    
                    data.guilds.forEach(guild => {
                        const div = document.createElement('div');
                        div.className = 'item';
                        div.innerHTML = guild.name + ' <span class="count">' + (guild.member_count || 0) + '</span>';
                        div.onclick = () => selectGuild(guild.id, guild.name);
                        container.appendChild(div);
                    });
                }
            } catch (error) {
                console.error('Error loading guilds:', error);
            }
        }

        async function selectGuild(guildId, guildName) {
            currentGuild = guildId;
            
            document.querySelectorAll('#guilds-list .item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            document.getElementById('current-title').textContent = guildName;
            document.getElementById('manual-scan-btn').disabled = false;
            
            await loadChannels(guildId);
        }

        async function loadChannels(guildId) {
            try {
                const response = await fetch('/api/channels/' + guildId);
                const data = await response.json();
                
                if (data.success) {
                    const container = document.getElementById('channels-list');
                    container.innerHTML = '';
                    
                    data.channels.forEach(channel => {
                        const div = document.createElement('div');
                        div.className = 'item';
                        if (channel.message_count > 0) {
                            div.classList.add('has-messages');
                        }
                        div.innerHTML = '# ' + channel.name + ' <span class="count">' + (channel.message_count || 0) + '</span>';
                        div.onclick = () => selectChannel(channel.id, channel.name);
                        container.appendChild(div);
                    });
                }
            } catch (error) {
                console.error('Error loading channels:', error);
            }
        }

        async function selectChannel(channelId, channelName) {
            currentChannel = channelId;
            
            document.querySelectorAll('#channels-list .item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            document.getElementById('current-title').textContent = '# ' + channelName;
            
            await loadMessages(channelId);
        }

        async function loadMessages(channelId) {
            try {
                const response = await fetch('/api/messages/' + channelId);
                const data = await response.json();
                
                if (data.success) {
                    const container = document.getElementById('messages-container');
                    
                    if (data.messages.length === 0) {
                        container.innerHTML = '<h2>No messages found</h2>';
                        return;
                    }
                    
                    container.innerHTML = '';
                    data.messages.forEach(message => {
                        const div = document.createElement('div');
                        div.className = 'message';
                        
                        const time = new Date(message.timestamp).toLocaleString();
                        
                        div.innerHTML = 
                            '<div class="message-author">' + message.author + ' - ' + time + '</div>' +
                            '<div class="message-content">' + (message.content || 'No content') + '</div>';
                        
                        container.appendChild(div);
                    });
                    
                    container.scrollTop = container.scrollHeight;
                }
            } catch (error) {
                console.error('Error loading messages:', error);
            }
        }

        function toggleScan() {
            const btn = document.getElementById('scan-btn');
            
            if (scanActive) {
                fetch('/api/stop-scan', { method: 'POST' });
                btn.textContent = 'Start ULTRA-FAST';
                btn.className = 'btn btn-success';
                scanActive = false;
            } else {
                fetch('/api/start-scan', { method: 'POST' });
                btn.textContent = 'Stop Scanning';
                btn.className = 'btn btn-danger scanning';
                scanActive = true;
            }
        }

        async function scanGuild() {
            if (!currentGuild) return;
            
            const btn = document.getElementById('manual-scan-btn');
            btn.textContent = 'Scanning...';
            btn.disabled = true;
            
            try {
                const response = await fetch('/api/scan/' + currentGuild, { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    await loadChannels(currentGuild);
                }
            } finally {
                btn.textContent = 'Manual Scan';
                btn.disabled = false;
            }
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                if (currentChannel) {
                    loadMessages(currentChannel);
                }
                if (currentGuild) {
                    loadChannels(currentGuild);
                }
            }, 200); // 200ms refresh - ULTRA FAST!
        }
    </script>
</body>
</html>'''

# Routes
@app.route('/')
def index():
    return HTML

@app.route('/api/setup', methods=['POST'])
def setup():
    global DISCORD_TOKEN
    
    try:
        data = request.get_json()
        token = data.get('token', '').strip()
        
        result = test_token(token)
        if result['success']:
            DISCORD_TOKEN = token
            print(f"[SUCCESS] Connected as {result['user']['username']}")
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/guilds')
def api_guilds():
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    guilds = get_guilds(DISCORD_TOKEN)
    return jsonify({'success': True, 'guilds': guilds})

@app.route('/api/channels/<guild_id>')
def api_channels(guild_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    channels = get_channels(DISCORD_TOKEN, guild_id)
    return jsonify({'success': True, 'channels': channels})

@app.route('/api/messages/<channel_id>')
def api_messages(channel_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    messages = get_messages(DISCORD_TOKEN, channel_id)
    return jsonify({'success': True, 'messages': messages})

@app.route('/api/scan/<guild_id>', methods=['POST'])
def api_scan(guild_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    try:
        channels = get_channels(DISCORD_TOKEN, guild_id)
        for channel in channels:
            get_messages(DISCORD_TOKEN, channel['id'])
            time.sleep(0.05)
        
        return jsonify({'success': True, 'message': f'Scanned {len(channels)} channels'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/start-scan', methods=['POST'])
def start_scan():
    global SCAN_ACTIVE
    if not SCAN_ACTIVE:
        SCAN_ACTIVE = True
        scan_thread = threading.Thread(target=ultra_fast_scan, daemon=True)
        scan_thread.start()
        print("[ULTRA-FAST] Background scanning started (200ms interval)")
    return jsonify({'success': True})

@app.route('/api/stop-scan', methods=['POST'])
def stop_scan():
    global SCAN_ACTIVE
    SCAN_ACTIVE = False
    print("[STOP] Background scanning stopped")
    return jsonify({'success': True})

if __name__ == '__main__':
    print("=" * 50)
    print("STABLE DISCORD LOGGER - ULTRA FAST")
    print("=" * 50)
    print("Web interface: http://127.0.0.1:5001")
    print("Features:")
    print("- 200ms scan interval")
    print("- 200ms UI refresh")
    print("- Minimal and stable")
    print("=" * 50)
    
    init_db()
    app.run(host='127.0.0.1', port=5001, debug=False)
