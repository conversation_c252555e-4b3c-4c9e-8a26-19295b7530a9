#!/usr/bin/env python3
"""
Working Discord Logger - Simplified but reliable version
Single file, no external dependencies, guaranteed to work
"""

import requests
import sqlite3
import json
import time
from datetime import datetime
from flask import Flask, request, jsonify

# Global variables
app = Flask(__name__)
DISCORD_TOKEN = None
BASE_URL = "https://discord.com/api/v9"
AUTO_SCAN_ACTIVE = False
LAST_SCAN_TIME = {}  # Track last scan time per channel

def init_database():
    """Initialize SQLite database"""
    conn = sqlite3.connect('working_discord.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS guilds (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            member_count INTEGER DEFAULT 0,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS channels (
            id TEXT PRIMARY KEY,
            guild_id TEXT,
            name TEXT NOT NULL,
            type INTEGER,
            has_messages INTEGER DEFAULT 0,
            message_count INTEGER DEFAULT 0,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id TEXT PRIMARY KEY,
            channel_id TEXT,
            author_name TEXT,
            content TEXT,
            timestamp TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()
    print("Database initialized successfully")

def test_token(token):
    """Test Discord token"""
    headers = {"Authorization": token}
    
    try:
        print(f"Testing token (length: {len(token)})")
        response = requests.get(f"{BASE_URL}/users/@me", headers=headers, timeout=10)
        print(f"Token test response: {response.status_code}")
        
        if response.status_code == 200:
            user_data = response.json()
            print(f"Token valid for user: {user_data['username']}")
            return {"success": True, "user": user_data}
        else:
            print(f"Token test failed: {response.status_code} - {response.text}")
            return {"success": False, "error": f"HTTP {response.status_code}"}
    except Exception as e:
        print(f"Token test error: {e}")
        return {"success": False, "error": str(e)}

def get_guilds(token):
    """Get user guilds"""
    headers = {"Authorization": token}
    
    try:
        print("Fetching guilds from Discord API")
        response = requests.get(f"{BASE_URL}/users/@me/guilds", headers=headers, timeout=10)
        print(f"Guilds response: {response.status_code}")
        
        if response.status_code == 200:
            guilds = response.json()
            print(f"Got {len(guilds)} guilds from API")
            
            # Save to database
            conn = sqlite3.connect('working_discord.db')
            cursor = conn.cursor()
            
            for guild in guilds:
                cursor.execute('''
                    INSERT OR REPLACE INTO guilds (id, name, member_count, timestamp)
                    VALUES (?, ?, ?, ?)
                ''', (
                    guild['id'],
                    guild['name'],
                    guild.get('approximate_member_count', 0),
                    datetime.now().isoformat()
                ))
            
            conn.commit()
            conn.close()
            print(f"Saved {len(guilds)} guilds to database")
            return guilds
        else:
            print(f"Failed to get guilds: {response.status_code}")
            return []
    except Exception as e:
        print(f"Error getting guilds: {e}")
        return []

def get_channels(token, guild_id):
    """Get guild channels"""
    headers = {"Authorization": token}
    
    try:
        print(f"Fetching channels for guild {guild_id}")
        response = requests.get(f"{BASE_URL}/guilds/{guild_id}/channels", headers=headers, timeout=10)
        print(f"Channels response: {response.status_code}")
        
        if response.status_code == 200:
            channels = response.json()
            text_channels = [ch for ch in channels if ch['type'] == 0]
            print(f"Got {len(text_channels)} text channels")
            
            # Save to database
            conn = sqlite3.connect('working_discord.db')
            cursor = conn.cursor()
            
            for channel in text_channels:
                cursor.execute('''
                    INSERT OR REPLACE INTO channels (id, guild_id, name, type, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    channel['id'],
                    guild_id,
                    channel['name'],
                    channel['type'],
                    datetime.now().isoformat()
                ))
            
            conn.commit()
            conn.close()
            print(f"Saved {len(text_channels)} channels to database")
            return text_channels
        else:
            print(f"Failed to get channels: {response.status_code}")
            return get_cached_channels(guild_id)
    except Exception as e:
        print(f"Error getting channels: {e}")
        return get_cached_channels(guild_id)

def get_cached_channels(guild_id):
    """Get channels from database"""
    try:
        conn = sqlite3.connect('working_discord.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM channels WHERE guild_id = ? ORDER BY name', (guild_id,))
        channels = [dict(row) for row in cursor.fetchall()]
        conn.close()
        print(f"Got {len(channels)} cached channels")
        return channels
    except Exception as e:
        print(f"Error getting cached channels: {e}")
        return []

def get_messages(token, channel_id):
    """Get channel messages"""
    headers = {"Authorization": token}
    
    try:
        print(f"Fetching messages for channel {channel_id}")
        response = requests.get(f"{BASE_URL}/channels/{channel_id}/messages?limit=50", headers=headers, timeout=10)
        print(f"Messages response: {response.status_code}")
        
        if response.status_code == 200:
            messages = response.json()
            print(f"Got {len(messages)} messages")
            
            # Save to database
            conn = sqlite3.connect('working_discord.db')
            cursor = conn.cursor()
            
            for message in messages:
                cursor.execute('''
                    INSERT OR REPLACE INTO messages (id, channel_id, author_name, content, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    message['id'],
                    channel_id,
                    message['author']['username'],
                    message['content'],
                    message['timestamp']
                ))
            
            # Update channel message count
            cursor.execute('''
                UPDATE channels SET has_messages = 1, message_count = ? WHERE id = ?
            ''', (len(messages), channel_id))
            
            conn.commit()
            conn.close()
            print(f"Saved {len(messages)} messages to database")
            return get_cached_messages(channel_id)
        else:
            print(f"No access to channel {channel_id}: {response.status_code}")
            return get_cached_messages(channel_id)
    except Exception as e:
        print(f"Error getting messages: {e}")
        return get_cached_messages(channel_id)

def get_cached_messages(channel_id):
    """Get messages from database"""
    try:
        conn = sqlite3.connect('working_discord.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM messages WHERE channel_id = ? ORDER BY timestamp', (channel_id,))
        messages = [dict(row) for row in cursor.fetchall()]
        conn.close()
        print(f"Got {len(messages)} cached messages")
        return messages
    except Exception as e:
        print(f"Error getting cached messages: {e}")
        return []

def auto_scan_channels():
    """Automatically scan channels for new messages"""
    global AUTO_SCAN_ACTIVE, LAST_SCAN_TIME

    if not DISCORD_TOKEN or not AUTO_SCAN_ACTIVE:
        return

    try:
        # Get all channels from database
        conn = sqlite3.connect('working_discord.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM channels ORDER BY has_messages DESC, message_count DESC')
        channels = [dict(row) for row in cursor.fetchall()]
        conn.close()

        print(f"Auto-scanning {len(channels)} channels...")
        new_messages_found = 0

        for channel in channels:
            channel_id = channel['id']

            # Skip if scanned recently (less than 2 seconds ago)
            if channel_id in LAST_SCAN_TIME:
                time_since_scan = time.time() - LAST_SCAN_TIME[channel_id]
                if time_since_scan < 2:
                    continue

            # Get messages and check for new ones
            old_count = channel.get('message_count', 0)
            messages = get_messages(DISCORD_TOKEN, channel_id)
            new_count = len(messages)

            if new_count > old_count:
                new_messages_found += new_count - old_count
                print(f"[NEW] Found {new_count - old_count} new messages in #{channel['name']}")

            LAST_SCAN_TIME[channel_id] = time.time()

            # Very small delay to avoid rate limiting
            time.sleep(0.05)

        if new_messages_found > 0:
            print(f"[SUCCESS] Auto-scan complete: {new_messages_found} new messages found")
        else:
            print("[INFO] Auto-scan complete: no new messages")

    except Exception as e:
        print(f"Error in auto-scan: {e}")

import threading

def start_auto_scan():
    """Start background auto-scanning"""
    global AUTO_SCAN_ACTIVE
    AUTO_SCAN_ACTIVE = True

    def scan_loop():
        while AUTO_SCAN_ACTIVE:
            try:
                auto_scan_channels()
                time.sleep(0.5)  # Scan every 500ms - ULTRA FAST!
            except Exception as e:
                print(f"Auto-scan loop error: {e}")
                time.sleep(1)

    scan_thread = threading.Thread(target=scan_loop, daemon=True)
    scan_thread.start()
    print("[ULTRA-FAST] Auto-scan started (every 500ms!)")

# HTML Template
HTML_TEMPLATE = '''<!DOCTYPE html>
<html>
<head>
    <title>Discord Logger Pro</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: white;
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        .sidebar {
            width: 350px;
            min-width: 300px;
            max-width: 400px;
            background: #2c2c2c;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
        }

        .sidebar-header {
            padding: 20px;
            background: #5865f2;
            border-bottom: 1px solid #404040;
            text-align: center;
        }

        .sidebar-header h1 {
            font-size: 18px;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .sidebar-header .status {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .section {
            margin-bottom: 25px;
        }

        .section h3 {
            color: #888;
            font-size: 11px;
            text-transform: uppercase;
            margin-bottom: 12px;
            letter-spacing: 1px;
            font-weight: 600;
        }

        .item {
            padding: 12px 15px;
            margin: 3px 0;
            background: linear-gradient(135deg, #333 0%, #2a2a2a 100%);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item:hover {
            background: linear-gradient(135deg, #404040 0%, #353535 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .item.active {
            background: linear-gradient(135deg, #5865f2 0%, #4752c4 100%);
            border-color: #7289da;
            box-shadow: 0 4px 15px rgba(88, 101, 242, 0.3);
        }

        .item.has-messages {
            border-left: 4px solid #faa61a;
            background: linear-gradient(135deg, #3d3520 0%, #2d2a1a 100%);
        }

        .item.has-messages::after {
            content: '💾';
            font-size: 12px;
        }

        .item-count {
            background: rgba(255,255,255,0.1);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }

        .main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1a1a1a;
            min-width: 0;
            width: calc(100vw - 350px);
        }

        .main-header {
            height: 70px;
            background: #2c2c2c;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            flex-shrink: 0;
            width: 100%;
        }

        .main-header h2 {
            font-size: 20px;
            font-weight: 600;
        }

        .header-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .main-content {
            flex: 1;
            padding: 25px 30px;
            overflow-y: auto;
            background: #1a1a1a;
            height: calc(100vh - 70px);
            width: 100%;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
            border-radius: 12px;
            border-left: 4px solid #5865f2;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .message:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .message-author {
            font-weight: 600;
            color: #5865f2;
            font-size: 14px;
        }

        .message-time {
            font-size: 11px;
            color: #888;
            background: rgba(255,255,255,0.05);
            padding: 2px 8px;
            border-radius: 8px;
        }

        .message-content {
            color: #ddd;
            line-height: 1.5;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            background: #5865f2;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            background: #4752c4;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #666 0%, #555 100%);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #777 0%, #666 100%);
        }

        .setup {
            max-width: 500px;
            margin: 10vh auto;
            padding: 40px;
            background: #2c2c2c;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border: 1px solid #404040;
            position: relative;
            top: 50%;
            transform: translateY(-50%);
        }

        .setup h2 {
            text-align: center;
            margin-bottom: 10px;
            font-size: 24px;
            background: linear-gradient(135deg, #5865f2 0%, #4752c4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .setup p {
            text-align: center;
            color: #888;
            margin-bottom: 30px;
        }

        .setup input {
            width: 100%;
            padding: 15px;
            margin: 15px 0;
            background: linear-gradient(135deg, #333 0%, #2a2a2a 100%);
            border: 2px solid #404040;
            color: white;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .setup input:focus {
            outline: none;
            border-color: #5865f2;
            box-shadow: 0 0 0 3px rgba(88, 101, 242, 0.1);
        }

        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status.success {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status.error {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(220, 53, 69, 0.1) 100%);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .welcome {
            text-align: center;
            padding: 60px 20px;
            color: #888;
        }

        .welcome h2 {
            font-size: 28px;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #5865f2 0%, #4752c4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome p {
            font-size: 16px;
            line-height: 1.6;
        }

        .auto-scan-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 1000;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 40px;
            color: #888;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #333;
            border-top: 2px solid #5865f2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive design for different screen sizes */
        @media (min-width: 1920px) {
            .sidebar {
                width: 400px;
            }
            .main {
                width: calc(100vw - 400px);
            }
            .main-content {
                padding: 30px 40px;
            }
        }

        @media (min-width: 2560px) {
            .sidebar {
                width: 450px;
            }
            .main {
                width: calc(100vw - 450px);
            }
            .main-content {
                padding: 40px 60px;
                max-width: 1400px;
                margin: 0 auto;
            }
        }

        @media (max-width: 1366px) {
            .sidebar {
                width: 300px;
            }
            .main {
                width: calc(100vw - 300px);
            }
            .main-content {
                padding: 20px 25px;
            }
        }

        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }
            .main {
                width: calc(100vw - 280px);
            }
            .main-content {
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Auto-scan status indicator -->
        <div class="auto-scan-status" id="auto-scan-status" style="display: none;">
            <div class="spinner"></div>
            <span>ULTRA-FAST scanning (500ms)</span>
        </div>

        <!-- Setup screen -->
        <div class="setup" id="setup-screen">
            <h2><i class="fas fa-shield-alt"></i> Discord Logger Pro</h2>
            <p>Professional Discord monitoring with real-time updates</p>
            <input type="password" id="token-input" placeholder="Enter your Discord token">
            <button class="btn" onclick="setupToken()">
                <i class="fas fa-rocket"></i>
                Initialize System
            </button>
            <div id="setup-status"></div>
        </div>

        <!-- Main application -->
        <div id="main-screen" style="display: none; width: 100%; display: flex;">
            <div class="sidebar">
                <div class="sidebar-header">
                    <h1><i class="fas fa-comments"></i> Discord Logger Pro</h1>
                    <div class="status">
                        <div class="status-dot"></div>
                        <span>Real-time monitoring active</span>
                    </div>
                </div>

                <div class="sidebar-content">
                    <div class="section">
                        <h3><i class="fas fa-server"></i> Servers</h3>
                        <button class="btn btn-secondary" onclick="loadGuilds()">
                            <i class="fas fa-sync-alt"></i>
                            Refresh Servers
                        </button>
                        <div id="guilds-list">
                            <div class="loading">
                                <div class="spinner"></div>
                                <span>Loading servers...</span>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <h3><i class="fas fa-hashtag"></i> Channels</h3>
                        <div id="channels-list">
                            <div style="color: #666; padding: 15px; text-align: center; font-size: 13px;">
                                <i class="fas fa-arrow-up"></i><br>
                                Select a server first
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main">
                <div class="main-header">
                    <h2 id="current-title">
                        <i class="fas fa-home"></i>
                        Discord Logger Pro
                    </h2>
                    <div class="header-controls">
                        <button class="btn btn-secondary" onclick="toggleAutoScan()" id="auto-scan-btn">
                            <i class="fas fa-play"></i>
                            Start Auto-Scan
                        </button>
                        <button class="btn" onclick="scanGuild()" id="scan-btn" disabled>
                            <i class="fas fa-search"></i>
                            Scan Guild
                        </button>
                    </div>
                </div>

                <div class="main-content">
                    <div id="messages-container">
                        <div class="welcome">
                            <h2>Welcome to Discord Logger Pro</h2>
                            <p>ULTRA-FAST Discord monitoring with 500ms real-time updates</p>
                            <p style="margin-top: 20px; font-size: 14px; opacity: 0.8;">
                                <i class="fas fa-bolt"></i> 500ms scanning &nbsp;&nbsp;
                                <i class="fas fa-database"></i> Instant storage &nbsp;&nbsp;
                                <i class="fas fa-eye"></i> Real-time tracking
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentGuild = null;
        let currentChannel = null;
        let isAuthenticated = false;
        let autoScanActive = false;
        let autoScanInterval = null;
        let autoRefreshInterval = null;

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('setup-status');
            const icon = type === 'success' ? '<i class="fas fa-check-circle"></i>' :
                        type === 'error' ? '<i class="fas fa-exclamation-circle"></i>' :
                        '<i class="fas fa-info-circle"></i>';
            statusEl.innerHTML = '<div class="status ' + type + '">' + icon + ' ' + message + '</div>';
        }

        function toggleAutoScan() {
            const btn = document.getElementById('auto-scan-btn');
            const statusEl = document.getElementById('auto-scan-status');

            if (autoScanActive) {
                // Stop auto-scan
                autoScanActive = false;
                clearInterval(autoScanInterval);
                clearInterval(autoRefreshInterval);
                btn.innerHTML = '<i class="fas fa-play"></i> Start Auto-Scan';
                statusEl.style.display = 'none';
                console.log('Auto-scan stopped');
            } else {
                // Start auto-scan
                autoScanActive = true;
                btn.innerHTML = '<i class="fas fa-stop"></i> Stop Auto-Scan';
                statusEl.style.display = 'flex';

                // Start intervals - ULTRA FAST!
                autoScanInterval = setInterval(autoScanChannels, 2000); // Every 2 seconds
                autoRefreshInterval = setInterval(refreshCurrentView, 500); // Every 500ms!

                console.log('Auto-scan started');
                autoScanChannels(); // Run immediately
            }
        }

        async function autoScanChannels() {
            if (!isAuthenticated || !currentGuild) return;

            try {
                // Visual feedback for ultra-fast scanning
                const statusEl = document.getElementById('auto-scan-status');
                if (statusEl) {
                    statusEl.style.opacity = '1';
                    setTimeout(() => { if (statusEl) statusEl.style.opacity = '0.7'; }, 100);
                }

                const response = await fetch('/api/scan/' + currentGuild, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    // Only log significant findings to avoid spam
                    if (data.message.includes('accessible')) {
                        console.log('🔍 Auto-scan:', data.message);
                    }
                    // Refresh channels list to show updated counts
                    await loadChannels(currentGuild);
                }
            } catch (error) {
                console.error('Auto-scan error:', error);
            }
        }

        async function refreshCurrentView() {
            if (!isAuthenticated) return;

            try {
                // Refresh current channel messages if viewing a channel
                if (currentChannel) {
                    const response = await fetch('/api/messages/' + currentChannel);
                    const data = await response.json();

                    if (data.success) {
                        const currentCount = document.querySelectorAll('.message').length;
                        if (data.messages.length !== currentCount) {
                            console.log('New messages detected, refreshing...');
                            displayMessages(data.messages);
                        }
                    }
                }

                // Refresh channels list
                if (currentGuild) {
                    await loadChannels(currentGuild);
                }
            } catch (error) {
                console.error('Auto-refresh error:', error);
            }
        }

        async function setupToken() {
            const token = document.getElementById('token-input').value.trim();
            if (!token) {
                showStatus('Please enter a token', 'error');
                return;
            }

            try {
                const response = await fetch('/api/setup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token: token })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('Connected as ' + data.user.username, 'success');
                    isAuthenticated = true;
                    document.getElementById('setup-screen').style.display = 'none';
                    document.getElementById('main-screen').style.display = 'flex';
                    loadGuilds();
                } else {
                    showStatus('Error: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('Connection failed: ' + error.message, 'error');
            }
        }

        async function loadGuilds() {
            try {
                const response = await fetch('/api/guilds');
                const data = await response.json();
                
                if (data.success) {
                    displayGuilds(data.guilds);
                } else {
                    console.error('Failed to load guilds:', data.error);
                }
            } catch (error) {
                console.error('Error loading guilds:', error);
            }
        }

        function displayGuilds(guilds) {
            const container = document.getElementById('guilds-list');
            container.innerHTML = '';

            if (guilds.length === 0) {
                container.innerHTML = '<div style="color: #666; padding: 15px; text-align: center; font-size: 13px;"><i class="fas fa-exclamation-triangle"></i><br>No servers found</div>';
                return;
            }

            guilds.forEach(guild => {
                const div = document.createElement('div');
                div.className = 'item';
                div.innerHTML = `
                    <span><i class="fas fa-server" style="margin-right: 8px; color: #888;"></i>${guild.name}</span>
                    <span class="item-count">${guild.member_count || 0}</span>
                `;
                div.onclick = () => selectGuild(guild.id, guild.name);
                container.appendChild(div);
            });
        }

        async function selectGuild(guildId, guildName) {
            currentGuild = guildId;
            
            document.querySelectorAll('#guilds-list .item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            document.getElementById('current-title').textContent = guildName;
            document.getElementById('scan-btn').disabled = false;
            
            await loadChannels(guildId);
        }

        async function loadChannels(guildId) {
            try {
                const response = await fetch('/api/channels/' + guildId);
                const data = await response.json();
                
                if (data.success) {
                    displayChannels(data.channels);
                }
            } catch (error) {
                console.error('Error loading channels:', error);
            }
        }

        function displayChannels(channels) {
            const container = document.getElementById('channels-list');
            container.innerHTML = '';

            if (channels.length === 0) {
                container.innerHTML = '<div style="color: #666; padding: 15px; text-align: center; font-size: 13px;"><i class="fas fa-hashtag"></i><br>No channels found</div>';
                return;
            }

            channels.forEach(channel => {
                const div = document.createElement('div');
                div.className = 'item';
                if (channel.has_messages) {
                    div.classList.add('has-messages');
                }

                const messageCount = channel.message_count || 0;
                const icon = channel.has_messages ? 'fas fa-hashtag' : 'far fa-hashtag';

                div.innerHTML = `
                    <span><i class="${icon}" style="margin-right: 8px; color: ${channel.has_messages ? '#faa61a' : '#888'};"></i>${channel.name}</span>
                    <span class="item-count">${messageCount}</span>
                `;
                div.onclick = () => selectChannel(channel.id, channel.name);
                container.appendChild(div);
            });
        }

        async function selectChannel(channelId, channelName) {
            currentChannel = channelId;
            
            document.querySelectorAll('#channels-list .item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            document.getElementById('current-title').textContent = '# ' + channelName;
            
            await loadMessages(channelId);
        }

        async function loadMessages(channelId) {
            try {
                document.getElementById('messages-container').innerHTML = 'Loading messages...';
                
                const response = await fetch('/api/messages/' + channelId);
                const data = await response.json();
                
                if (data.success) {
                    displayMessages(data.messages);
                }
            } catch (error) {
                console.error('Error loading messages:', error);
            }
        }

        function displayMessages(messages) {
            const container = document.getElementById('messages-container');

            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="welcome">
                        <h2><i class="fas fa-inbox"></i> No Messages Found</h2>
                        <p>This channel appears to be empty or inaccessible</p>
                        <p style="margin-top: 15px; font-size: 14px; opacity: 0.7;">
                            Try using the "Scan Guild" button to check for accessible channels
                        </p>
                    </div>
                `;
                return;
            }

            container.innerHTML = '';
            messages.forEach((message, index) => {
                const div = document.createElement('div');
                div.className = 'message';

                const time = new Date(message.timestamp).toLocaleString();
                const content = message.content || '<em style="opacity: 0.7;">No text content</em>';

                // Generate avatar initials
                const initials = message.author_name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);

                div.innerHTML = `
                    <div class="message-header">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #5865f2, #4752c4); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">
                                ${initials}
                            </div>
                            <span class="message-author">${message.author_name}</span>
                        </div>
                        <span class="message-time"><i class="far fa-clock"></i> ${time}</span>
                    </div>
                    <div class="message-content">${content}</div>
                `;

                container.appendChild(div);
            });

            // Scroll to bottom
            container.scrollTop = container.scrollHeight;
        }

        async function scanGuild() {
            if (!currentGuild) return;
            
            const btn = document.getElementById('scan-btn');
            btn.textContent = 'Scanning...';
            btn.disabled = true;
            
            try {
                const response = await fetch('/api/scan/' + currentGuild, { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    alert(data.message);
                    await loadChannels(currentGuild);
                }
            } catch (error) {
                console.error('Scan error:', error);
            } finally {
                btn.textContent = 'Scan Guild';
                btn.disabled = false;
            }
        }
    </script>
</body>
</html>'''

# Routes
@app.route('/')
def index():
    return HTML_TEMPLATE

@app.route('/api/setup', methods=['POST'])
def setup():
    global DISCORD_TOKEN
    
    try:
        data = request.get_json()
        token = data.get('token', '').strip()
        
        if not token:
            return jsonify({'success': False, 'error': 'Token required'})
        
        # Test token
        result = test_token(token)
        if result['success']:
            DISCORD_TOKEN = token
            # Load guilds immediately
            guilds = get_guilds(token)
            print(f"Setup complete: {len(guilds)} guilds loaded")

            # Start auto-scanning automatically
            start_auto_scan()

        return jsonify(result)
    except Exception as e:
        print(f"Setup error: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/guilds')
def api_guilds():
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    try:
        # Try fresh data first
        guilds = get_guilds(DISCORD_TOKEN)
        
        # Fallback to database
        if not guilds:
            conn = sqlite3.connect('working_discord.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM guilds ORDER BY name')
            guilds = [dict(row) for row in cursor.fetchall()]
            conn.close()
        
        return jsonify({'success': True, 'guilds': guilds})
    except Exception as e:
        print(f"Error in api_guilds: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/channels/<guild_id>')
def api_channels(guild_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    try:
        channels = get_channels(DISCORD_TOKEN, guild_id)
        return jsonify({'success': True, 'channels': channels})
    except Exception as e:
        print(f"Error in api_channels: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/messages/<channel_id>')
def api_messages(channel_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    try:
        messages = get_messages(DISCORD_TOKEN, channel_id)
        return jsonify({'success': True, 'messages': messages})
    except Exception as e:
        print(f"Error in api_messages: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/scan/<guild_id>', methods=['POST'])
def api_scan(guild_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    try:
        channels = get_channels(DISCORD_TOKEN, guild_id)
        accessible_count = 0
        
        for channel in channels:
            messages = get_messages(DISCORD_TOKEN, channel['id'])
            if messages:
                accessible_count += 1
            time.sleep(0.1)  # Rate limiting
        
        return jsonify({
            'success': True,
            'message': f'Scanned {len(channels)} channels, {accessible_count} accessible'
        })
    except Exception as e:
        print(f"Error in api_scan: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/auto-scan', methods=['POST'])
def api_auto_scan():
    """Start/stop auto-scanning"""
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})

    try:
        data = request.get_json()
        action = data.get('action', 'start')

        if action == 'start':
            start_auto_scan()
            return jsonify({'success': True, 'message': 'Auto-scan started'})
        else:
            global AUTO_SCAN_ACTIVE
            AUTO_SCAN_ACTIVE = False
            return jsonify({'success': True, 'message': 'Auto-scan stopped'})
    except Exception as e:
        print(f"Error in api_auto_scan: {e}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("=" * 50)
    print("WORKING DISCORD LOGGER")
    print("=" * 50)
    print("Web interface: http://127.0.0.1:5001")
    print("=" * 50)
    
    init_database()
    app.run(host='127.0.0.1', port=5001, debug=False)
