#!/usr/bin/env python3
"""
Working Discord Logger - Simplified but reliable version
Single file, no external dependencies, guaranteed to work
"""

import requests
import sqlite3
import json
import time
from datetime import datetime
from flask import Flask, request, jsonify

# Global variables
app = Flask(__name__)
DISCORD_TOKEN = None
BASE_URL = "https://discord.com/api/v9"

def init_database():
    """Initialize SQLite database"""
    conn = sqlite3.connect('working_discord.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS guilds (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            member_count INTEGER DEFAULT 0,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS channels (
            id TEXT PRIMARY KEY,
            guild_id TEXT,
            name TEXT NOT NULL,
            type INTEGER,
            has_messages INTEGER DEFAULT 0,
            message_count INTEGER DEFAULT 0,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id TEXT PRIMARY KEY,
            channel_id TEXT,
            author_name TEXT,
            content TEXT,
            timestamp TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()
    print("Database initialized successfully")

def test_token(token):
    """Test Discord token"""
    headers = {"Authorization": token}
    
    try:
        print(f"Testing token (length: {len(token)})")
        response = requests.get(f"{BASE_URL}/users/@me", headers=headers, timeout=10)
        print(f"Token test response: {response.status_code}")
        
        if response.status_code == 200:
            user_data = response.json()
            print(f"Token valid for user: {user_data['username']}")
            return {"success": True, "user": user_data}
        else:
            print(f"Token test failed: {response.status_code} - {response.text}")
            return {"success": False, "error": f"HTTP {response.status_code}"}
    except Exception as e:
        print(f"Token test error: {e}")
        return {"success": False, "error": str(e)}

def get_guilds(token):
    """Get user guilds"""
    headers = {"Authorization": token}
    
    try:
        print("Fetching guilds from Discord API")
        response = requests.get(f"{BASE_URL}/users/@me/guilds", headers=headers, timeout=10)
        print(f"Guilds response: {response.status_code}")
        
        if response.status_code == 200:
            guilds = response.json()
            print(f"Got {len(guilds)} guilds from API")
            
            # Save to database
            conn = sqlite3.connect('working_discord.db')
            cursor = conn.cursor()
            
            for guild in guilds:
                cursor.execute('''
                    INSERT OR REPLACE INTO guilds (id, name, member_count, timestamp)
                    VALUES (?, ?, ?, ?)
                ''', (
                    guild['id'],
                    guild['name'],
                    guild.get('approximate_member_count', 0),
                    datetime.now().isoformat()
                ))
            
            conn.commit()
            conn.close()
            print(f"Saved {len(guilds)} guilds to database")
            return guilds
        else:
            print(f"Failed to get guilds: {response.status_code}")
            return []
    except Exception as e:
        print(f"Error getting guilds: {e}")
        return []

def get_channels(token, guild_id):
    """Get guild channels"""
    headers = {"Authorization": token}
    
    try:
        print(f"Fetching channels for guild {guild_id}")
        response = requests.get(f"{BASE_URL}/guilds/{guild_id}/channels", headers=headers, timeout=10)
        print(f"Channels response: {response.status_code}")
        
        if response.status_code == 200:
            channels = response.json()
            text_channels = [ch for ch in channels if ch['type'] == 0]
            print(f"Got {len(text_channels)} text channels")
            
            # Save to database
            conn = sqlite3.connect('working_discord.db')
            cursor = conn.cursor()
            
            for channel in text_channels:
                cursor.execute('''
                    INSERT OR REPLACE INTO channels (id, guild_id, name, type, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    channel['id'],
                    guild_id,
                    channel['name'],
                    channel['type'],
                    datetime.now().isoformat()
                ))
            
            conn.commit()
            conn.close()
            print(f"Saved {len(text_channels)} channels to database")
            return text_channels
        else:
            print(f"Failed to get channels: {response.status_code}")
            return get_cached_channels(guild_id)
    except Exception as e:
        print(f"Error getting channels: {e}")
        return get_cached_channels(guild_id)

def get_cached_channels(guild_id):
    """Get channels from database"""
    try:
        conn = sqlite3.connect('working_discord.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM channels WHERE guild_id = ? ORDER BY name', (guild_id,))
        channels = [dict(row) for row in cursor.fetchall()]
        conn.close()
        print(f"Got {len(channels)} cached channels")
        return channels
    except Exception as e:
        print(f"Error getting cached channels: {e}")
        return []

def get_messages(token, channel_id):
    """Get channel messages"""
    headers = {"Authorization": token}
    
    try:
        print(f"Fetching messages for channel {channel_id}")
        response = requests.get(f"{BASE_URL}/channels/{channel_id}/messages?limit=50", headers=headers, timeout=10)
        print(f"Messages response: {response.status_code}")
        
        if response.status_code == 200:
            messages = response.json()
            print(f"Got {len(messages)} messages")
            
            # Save to database
            conn = sqlite3.connect('working_discord.db')
            cursor = conn.cursor()
            
            for message in messages:
                cursor.execute('''
                    INSERT OR REPLACE INTO messages (id, channel_id, author_name, content, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    message['id'],
                    channel_id,
                    message['author']['username'],
                    message['content'],
                    message['timestamp']
                ))
            
            # Update channel message count
            cursor.execute('''
                UPDATE channels SET has_messages = 1, message_count = ? WHERE id = ?
            ''', (len(messages), channel_id))
            
            conn.commit()
            conn.close()
            print(f"Saved {len(messages)} messages to database")
            return get_cached_messages(channel_id)
        else:
            print(f"No access to channel {channel_id}: {response.status_code}")
            return get_cached_messages(channel_id)
    except Exception as e:
        print(f"Error getting messages: {e}")
        return get_cached_messages(channel_id)

def get_cached_messages(channel_id):
    """Get messages from database"""
    try:
        conn = sqlite3.connect('working_discord.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM messages WHERE channel_id = ? ORDER BY timestamp', (channel_id,))
        messages = [dict(row) for row in cursor.fetchall()]
        conn.close()
        print(f"Got {len(messages)} cached messages")
        return messages
    except Exception as e:
        print(f"Error getting cached messages: {e}")
        return []

# HTML Template
HTML_TEMPLATE = '''<!DOCTYPE html>
<html>
<head>
    <title>Working Discord Logger</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: white; display: flex; height: 100vh; }
        .sidebar { width: 300px; background: #2c2c2c; padding: 20px; overflow-y: auto; }
        .main { flex: 1; padding: 20px; overflow-y: auto; }
        .section { margin-bottom: 20px; }
        .section h3 { color: #888; font-size: 12px; text-transform: uppercase; margin-bottom: 10px; }
        .item { padding: 8px; margin: 2px 0; background: #333; border-radius: 4px; cursor: pointer; }
        .item:hover { background: #444; }
        .item.active { background: #5865f2; }
        .item.has-messages { border-left: 3px solid #faa61a; }
        .message { margin-bottom: 10px; padding: 10px; background: #333; border-radius: 4px; }
        .message-author { font-weight: bold; color: #5865f2; }
        .message-time { font-size: 11px; color: #888; }
        .message-content { margin-top: 5px; }
        .btn { padding: 8px 16px; background: #5865f2; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #4752c4; }
        .setup { max-width: 400px; margin: 50px auto; padding: 20px; background: #2c2c2c; border-radius: 8px; }
        .setup input { width: 100%; padding: 10px; margin: 10px 0; background: #333; border: 1px solid #555; color: white; border-radius: 4px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: rgba(40, 167, 69, 0.2); color: #28a745; }
        .status.error { background: rgba(220, 53, 69, 0.2); color: #dc3545; }
    </style>
</head>
<body>
    <div id="app">
        <div class="setup" id="setup-screen">
            <h2>Working Discord Logger</h2>
            <p>Enter your Discord token to start:</p>
            <input type="password" id="token-input" placeholder="Discord Token">
            <button class="btn" onclick="setupToken()">Connect</button>
            <div id="setup-status"></div>
        </div>
        
        <div id="main-screen" style="display: none; width: 100%; display: flex;">
            <div class="sidebar">
                <div class="section">
                    <h3>Servers</h3>
                    <button class="btn" onclick="loadGuilds()">Refresh</button>
                    <div id="guilds-list"></div>
                </div>
                <div class="section">
                    <h3>Channels</h3>
                    <div id="channels-list">Select a server first</div>
                </div>
            </div>
            <div class="main">
                <h2 id="current-title">Working Discord Logger</h2>
                <button class="btn" onclick="scanGuild()" id="scan-btn" disabled>Scan Guild</button>
                <div id="messages-container">
                    <p>Select a channel to view messages</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentGuild = null;
        let currentChannel = null;
        let isAuthenticated = false;

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('setup-status');
            statusEl.innerHTML = '<div class="status ' + type + '">' + message + '</div>';
        }

        async function setupToken() {
            const token = document.getElementById('token-input').value.trim();
            if (!token) {
                showStatus('Please enter a token', 'error');
                return;
            }

            try {
                const response = await fetch('/api/setup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token: token })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('Connected as ' + data.user.username, 'success');
                    isAuthenticated = true;
                    document.getElementById('setup-screen').style.display = 'none';
                    document.getElementById('main-screen').style.display = 'flex';
                    loadGuilds();
                } else {
                    showStatus('Error: ' + data.error, 'error');
                }
            } catch (error) {
                showStatus('Connection failed: ' + error.message, 'error');
            }
        }

        async function loadGuilds() {
            try {
                const response = await fetch('/api/guilds');
                const data = await response.json();
                
                if (data.success) {
                    displayGuilds(data.guilds);
                } else {
                    console.error('Failed to load guilds:', data.error);
                }
            } catch (error) {
                console.error('Error loading guilds:', error);
            }
        }

        function displayGuilds(guilds) {
            const container = document.getElementById('guilds-list');
            container.innerHTML = '';
            
            guilds.forEach(guild => {
                const div = document.createElement('div');
                div.className = 'item';
                div.textContent = guild.name + ' (' + (guild.member_count || 0) + ')';
                div.onclick = () => selectGuild(guild.id, guild.name);
                container.appendChild(div);
            });
        }

        async function selectGuild(guildId, guildName) {
            currentGuild = guildId;
            
            document.querySelectorAll('#guilds-list .item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            document.getElementById('current-title').textContent = guildName;
            document.getElementById('scan-btn').disabled = false;
            
            await loadChannels(guildId);
        }

        async function loadChannels(guildId) {
            try {
                const response = await fetch('/api/channels/' + guildId);
                const data = await response.json();
                
                if (data.success) {
                    displayChannels(data.channels);
                }
            } catch (error) {
                console.error('Error loading channels:', error);
            }
        }

        function displayChannels(channels) {
            const container = document.getElementById('channels-list');
            container.innerHTML = '';
            
            channels.forEach(channel => {
                const div = document.createElement('div');
                div.className = 'item';
                if (channel.has_messages) {
                    div.classList.add('has-messages');
                }
                div.textContent = '# ' + channel.name + ' (' + (channel.message_count || 0) + ')';
                div.onclick = () => selectChannel(channel.id, channel.name);
                container.appendChild(div);
            });
        }

        async function selectChannel(channelId, channelName) {
            currentChannel = channelId;
            
            document.querySelectorAll('#channels-list .item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            document.getElementById('current-title').textContent = '# ' + channelName;
            
            await loadMessages(channelId);
        }

        async function loadMessages(channelId) {
            try {
                document.getElementById('messages-container').innerHTML = 'Loading messages...';
                
                const response = await fetch('/api/messages/' + channelId);
                const data = await response.json();
                
                if (data.success) {
                    displayMessages(data.messages);
                }
            } catch (error) {
                console.error('Error loading messages:', error);
            }
        }

        function displayMessages(messages) {
            const container = document.getElementById('messages-container');
            
            if (messages.length === 0) {
                container.innerHTML = 'No messages found';
                return;
            }
            
            container.innerHTML = '';
            messages.forEach(message => {
                const div = document.createElement('div');
                div.className = 'message';
                
                const time = new Date(message.timestamp).toLocaleString();
                
                div.innerHTML = 
                    '<div class="message-author">' + message.author_name + 
                    ' <span class="message-time">' + time + '</span></div>' +
                    '<div class="message-content">' + (message.content || 'No content') + '</div>';
                
                container.appendChild(div);
            });
        }

        async function scanGuild() {
            if (!currentGuild) return;
            
            const btn = document.getElementById('scan-btn');
            btn.textContent = 'Scanning...';
            btn.disabled = true;
            
            try {
                const response = await fetch('/api/scan/' + currentGuild, { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    alert(data.message);
                    await loadChannels(currentGuild);
                }
            } catch (error) {
                console.error('Scan error:', error);
            } finally {
                btn.textContent = 'Scan Guild';
                btn.disabled = false;
            }
        }
    </script>
</body>
</html>'''

# Routes
@app.route('/')
def index():
    return HTML_TEMPLATE

@app.route('/api/setup', methods=['POST'])
def setup():
    global DISCORD_TOKEN
    
    try:
        data = request.get_json()
        token = data.get('token', '').strip()
        
        if not token:
            return jsonify({'success': False, 'error': 'Token required'})
        
        # Test token
        result = test_token(token)
        if result['success']:
            DISCORD_TOKEN = token
            # Load guilds immediately
            guilds = get_guilds(token)
            print(f"Setup complete: {len(guilds)} guilds loaded")
        
        return jsonify(result)
    except Exception as e:
        print(f"Setup error: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/guilds')
def api_guilds():
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    try:
        # Try fresh data first
        guilds = get_guilds(DISCORD_TOKEN)
        
        # Fallback to database
        if not guilds:
            conn = sqlite3.connect('working_discord.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM guilds ORDER BY name')
            guilds = [dict(row) for row in cursor.fetchall()]
            conn.close()
        
        return jsonify({'success': True, 'guilds': guilds})
    except Exception as e:
        print(f"Error in api_guilds: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/channels/<guild_id>')
def api_channels(guild_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    try:
        channels = get_channels(DISCORD_TOKEN, guild_id)
        return jsonify({'success': True, 'channels': channels})
    except Exception as e:
        print(f"Error in api_channels: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/messages/<channel_id>')
def api_messages(channel_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    try:
        messages = get_messages(DISCORD_TOKEN, channel_id)
        return jsonify({'success': True, 'messages': messages})
    except Exception as e:
        print(f"Error in api_messages: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/scan/<guild_id>', methods=['POST'])
def api_scan(guild_id):
    if not DISCORD_TOKEN:
        return jsonify({'success': False, 'error': 'Not authenticated'})
    
    try:
        channels = get_channels(DISCORD_TOKEN, guild_id)
        accessible_count = 0
        
        for channel in channels:
            messages = get_messages(DISCORD_TOKEN, channel['id'])
            if messages:
                accessible_count += 1
            time.sleep(0.1)  # Rate limiting
        
        return jsonify({
            'success': True,
            'message': f'Scanned {len(channels)} channels, {accessible_count} accessible'
        })
    except Exception as e:
        print(f"Error in api_scan: {e}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("=" * 50)
    print("WORKING DISCORD LOGGER")
    print("=" * 50)
    print("Web interface: http://127.0.0.1:5001")
    print("=" * 50)
    
    init_database()
    app.run(host='127.0.0.1', port=5001, debug=False)
