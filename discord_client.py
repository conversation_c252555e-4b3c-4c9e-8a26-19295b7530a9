import discord
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional
from database import DatabaseManager
from config import Config

class DiscordLogger(discord.Client):
    def __init__(self, db_manager: DatabaseManager):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True
        intents.guild_messages = True
        intents.dm_messages = True
        
        super().__init__(intents=intents)
        
        self.db = db_manager
        self.logger = logging.getLogger(__name__)
        self.monitored_channels = set()
        self.last_message_ids = {}
        
    async def on_ready(self):
        """Called when the bot is ready"""
        self.logger.info(f'Logged in as {self.user} (ID: {self.user.id})')
        
        # Scan all guilds and channels
        await self.scan_guilds()
        await self.scan_channels()
        
        # Start monitoring tasks
        self.loop.create_task(self.monitor_messages())
        self.loop.create_task(self.monitor_channels())
        
        self.logger.info('Discord logger is ready!')
    
    async def scan_guilds(self):
        """Scan and save all accessible guilds"""
        for guild in self.guilds:
            guild_data = {
                'id': guild.id,
                'name': guild.name,
                'icon': str(guild.icon) if guild.icon else None,
                'owner_id': guild.owner_id
            }
            self.db.save_guild(guild_data)
            self.logger.info(f'Saved guild: {guild.name} (ID: {guild.id})')
    
    async def scan_channels(self):
        """Scan and save all accessible channels"""
        # Scan guild channels
        for guild in self.guilds:
            for channel in guild.channels:
                if isinstance(channel, (discord.TextChannel, discord.VoiceChannel, discord.CategoryChannel)):
                    await self.save_channel(channel, guild.id)
        
        # Scan DM channels
        for dm_channel in self.private_channels:
            if isinstance(dm_channel, discord.DMChannel):
                await self.save_dm_channel(dm_channel)
    
    async def save_channel(self, channel, guild_id: int):
        """Save channel information"""
        try:
            # Check if we can read the channel
            can_read = channel.permissions_for(channel.guild.me).read_messages if hasattr(channel, 'permissions_for') else True
            
            channel_data = {
                'id': channel.id,
                'guild_id': guild_id,
                'name': channel.name,
                'type': channel.type.value,
                'topic': getattr(channel, 'topic', None),
                'position': getattr(channel, 'position', 0),
                'is_accessible': can_read
            }
            
            self.db.save_channel(channel_data)
            
            if can_read and isinstance(channel, discord.TextChannel):
                self.monitored_channels.add(channel.id)
                # Get recent messages
                await self.scan_channel_history(channel)
            
            self.logger.info(f'Saved channel: {channel.name} (ID: {channel.id}, Accessible: {can_read})')
            
        except Exception as e:
            self.logger.error(f'Error saving channel {channel.name}: {e}')
    
    async def save_dm_channel(self, dm_channel):
        """Save DM channel information"""
        try:
            recipient = dm_channel.recipient
            dm_data = {
                'id': dm_channel.id,
                'recipient_id': recipient.id if recipient else None,
                'recipient_name': str(recipient) if recipient else 'Unknown'
            }
            
            self.db.save_dm_channel(dm_data)
            self.monitored_channels.add(dm_channel.id)
            
            # Get recent messages
            await self.scan_channel_history(dm_channel)
            
            self.logger.info(f'Saved DM channel with {recipient}')
            
        except Exception as e:
            self.logger.error(f'Error saving DM channel: {e}')
    
    async def scan_channel_history(self, channel, limit: int = 100):
        """Scan recent messages in a channel"""
        try:
            async for message in channel.history(limit=limit):
                await self.save_message(message)
                self.last_message_ids[channel.id] = message.id
                
        except discord.Forbidden:
            self.logger.warning(f'No permission to read history in {channel.name}')
        except Exception as e:
            self.logger.error(f'Error scanning history in {channel.name}: {e}')
    
    async def save_message(self, message):
        """Save message to database"""
        try:
            # Prepare attachments data
            attachments = []
            for attachment in message.attachments:
                attachments.append({
                    'id': attachment.id,
                    'filename': attachment.filename,
                    'url': attachment.url,
                    'size': attachment.size
                })
            
            # Prepare embeds data
            embeds = []
            for embed in message.embeds:
                embeds.append({
                    'title': embed.title,
                    'description': embed.description,
                    'url': embed.url,
                    'color': embed.color.value if embed.color else None
                })
            
            message_data = {
                'id': message.id,
                'channel_id': message.channel.id,
                'author_id': message.author.id,
                'author_name': str(message.author),
                'content': message.content,
                'embeds': embeds,
                'attachments': attachments,
                'created_at': message.created_at,
                'edited_at': message.edited_at
            }
            
            self.db.save_message(message_data)
            
        except Exception as e:
            self.logger.error(f'Error saving message {message.id}: {e}')
    
    async def on_message(self, message):
        """Handle new messages"""
        if message.channel.id in self.monitored_channels:
            await self.save_message(message)
            self.last_message_ids[message.channel.id] = message.id
    
    async def on_message_edit(self, before, after):
        """Handle message edits"""
        if after.channel.id in self.monitored_channels:
            await self.save_message(after)
    
    async def on_message_delete(self, message):
        """Handle message deletions"""
        if message.channel.id in self.monitored_channels:
            self.db.mark_message_deleted(message.id)
    
    async def on_guild_channel_create(self, channel):
        """Handle new channel creation"""
        if channel.guild.id in [g.id for g in self.guilds]:
            await self.save_channel(channel, channel.guild.id)
    
    async def on_guild_channel_update(self, before, after):
        """Handle channel updates"""
        if after.guild.id in [g.id for g in self.guilds]:
            await self.save_channel(after, after.guild.id)
    
    async def monitor_messages(self):
        """Periodically check for new messages"""
        while not self.is_closed():
            try:
                for channel_id in list(self.monitored_channels):
                    channel = self.get_channel(channel_id)
                    if channel and isinstance(channel, (discord.TextChannel, discord.DMChannel)):
                        try:
                            # Get messages newer than last known
                            last_id = self.last_message_ids.get(channel_id, 0)
                            async for message in channel.history(limit=50, after=discord.Object(id=last_id)):
                                await self.save_message(message)
                                self.last_message_ids[channel_id] = message.id
                                
                        except discord.Forbidden:
                            # Lost access to channel
                            if channel_id in self.monitored_channels:
                                self.monitored_channels.remove(channel_id)
                                self.logger.warning(f'Lost access to channel {channel_id}')
                        except Exception as e:
                            self.logger.error(f'Error monitoring channel {channel_id}: {e}')
                
                await asyncio.sleep(Config.MESSAGE_CHECK_INTERVAL)
                
            except Exception as e:
                self.logger.error(f'Error in message monitoring: {e}')
                await asyncio.sleep(Config.MESSAGE_CHECK_INTERVAL)
    
    async def monitor_channels(self):
        """Periodically check for new accessible channels"""
        while not self.is_closed():
            try:
                await self.scan_channels()
                await asyncio.sleep(Config.CHANNEL_CHECK_INTERVAL)
                
            except Exception as e:
                self.logger.error(f'Error in channel monitoring: {e}')
                await asyncio.sleep(Config.CHANNEL_CHECK_INTERVAL)
    
    def get_guild_data(self) -> List[Dict]:
        """Get all guild data"""
        try:
            # If client is ready, also get live data
            if self.is_ready():
                for guild in self.guilds:
                    guild_data = {
                        'id': guild.id,
                        'name': guild.name,
                        'icon': str(guild.icon) if guild.icon else None,
                        'owner_id': guild.owner_id
                    }
                    self.db.save_guild(guild_data)

            return self.db.get_guilds()
        except Exception as e:
            self.logger.error(f"Error getting guild data: {e}")
            return self.db.get_guilds()

    def get_channel_data(self, guild_id: Optional[int] = None) -> List[Dict]:
        """Get channel data"""
        try:
            # If client is ready, also get live data
            if self.is_ready() and guild_id:
                guild = self.get_guild(guild_id)
                if guild:
                    for channel in guild.channels:
                        if isinstance(channel, (discord.TextChannel, discord.VoiceChannel, discord.CategoryChannel)):
                            asyncio.create_task(self.save_channel(channel, guild.id))

            return self.db.get_channels(guild_id)
        except Exception as e:
            self.logger.error(f"Error getting channel data: {e}")
            return self.db.get_channels(guild_id)

    def get_message_data(self, channel_id: int, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Get message data"""
        return self.db.get_messages(channel_id, limit, offset)

    def get_dm_data(self) -> List[Dict]:
        """Get DM channel data"""
        try:
            # If client is ready, also get live data
            if self.is_ready():
                for dm_channel in self.private_channels:
                    if isinstance(dm_channel, discord.DMChannel):
                        asyncio.create_task(self.save_dm_channel(dm_channel))

            return self.db.get_dm_channels()
        except Exception as e:
            self.logger.error(f"Error getting DM data: {e}")
            return self.db.get_dm_channels()

    async def force_refresh_data(self):
        """Force refresh all data from Discord"""
        try:
            if not self.is_ready():
                self.logger.warning("Client not ready, cannot refresh data")
                return False

            self.logger.info("Force refreshing Discord data...")

            # Refresh guilds
            await self.scan_guilds()

            # Refresh channels
            await self.scan_channels()

            self.logger.info("Data refresh completed")
            return True

        except Exception as e:
            self.logger.error(f"Error during force refresh: {e}")
            return False
