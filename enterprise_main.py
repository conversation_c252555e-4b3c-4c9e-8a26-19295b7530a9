"""
Enterprise Discord Logger - Main Application
Professional-grade Discord logging system with enterprise architecture
"""

import asyncio
import signal
import sys
from datetime import datetime
from typing import Dict, Any
from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
import threading

# Import enterprise modules
from enterprise_config import config
from enterprise_logger import get_logger, LoggerFactory
from enterprise_database import db
from enterprise_discord_client import discord_client
from enterprise_scanner import scanner

class EnterpriseDiscordLogger:
    """Main application class"""
    
    def __init__(self):
        self.logger = get_logger('main')
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = config.web.secret_key
        self.socketio = SocketIO(self.app, cors_allowed_origins=config.web.cors_origins)
        self.is_running = False
        self.background_tasks = []
        
        self._setup_routes()
        self._setup_socketio()
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """Setup graceful shutdown handlers"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, shutting down gracefully...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            """Main page"""
            if not config.discord.token:
                return render_template('enterprise_setup.html')
            return render_template('enterprise_index.html')
        
        @self.app.route('/api/setup', methods=['POST'])
        def setup():
            """Setup Discord token"""
            try:
                data = request.get_json()
                token = data.get('token', '').strip()
                
                if not token:
                    return jsonify({'success': False, 'error': 'Token required'})

                # Validate token format
                if len(token) < 50:
                    return jsonify({'success': False, 'error': 'Token appears to be too short'})

                # Clean token (remove any prefixes if present)
                if token.startswith('Bearer '):
                    token = token[7:]
                elif token.startswith('Bot '):
                    token = token[4:]

                # Update configuration
                config.discord.token = token.strip()

                # Test authentication
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    user_info = loop.run_until_complete(discord_client.get_user_info())
                    loop.close()

                    if not user_info:
                        self.logger.error(f"Authentication failed for token: {token[:10]}...")
                        return jsonify({'success': False, 'error': 'Invalid token or insufficient permissions'})

                    self.logger.info(f"Successfully authenticated as {user_info['username']}")

                    # Load initial data after successful authentication
                    try:
                        guilds = loop.run_until_complete(discord_client.get_guilds())
                        self.logger.info(f"Loaded {len(guilds)} guilds after authentication")
                    except Exception as e:
                        self.logger.warning(f"Failed to load guilds: {e}")

                    return jsonify({'success': True, 'user': user_info})

                except Exception as auth_error:
                    self.logger.error(f"Authentication error: {auth_error}")
                    return jsonify({'success': False, 'error': f'Authentication failed: {str(auth_error)}'})
                
            except Exception as e:
                self.logger.error(f"Setup error: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/status')
        def get_status():
            """Get system status"""
            try:
                return jsonify({
                    'success': True,
                    'authenticated': config.discord.token is not None,
                    'user': discord_client.user_info,
                    'scanner_running': scanner.is_running,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/metrics')
        def get_metrics():
            """Get system metrics"""
            try:
                return jsonify({
                    'success': True,
                    'metrics': {
                        'database': db.get_metrics(),
                        'discord_client': discord_client.get_metrics(),
                        'scanner': scanner.get_metrics(),
                        'logging': LoggerFactory.get_all_metrics()
                    },
                    'config': config.to_dict(),
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/guilds')
        def get_guilds():
            """Get guilds"""
            try:
                # First try to get from database
                guilds = db.execute_query(
                    "SELECT * FROM guilds WHERE is_accessible = 1 ORDER BY name",
                    fetch='all'
                )

                # If no guilds in database and we have a token, try to load from Discord
                if len(guilds) == 0 and config.discord.token:
                    self.logger.info("No guilds in database, attempting to load from Discord...")
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        fresh_guilds = loop.run_until_complete(discord_client.get_guilds())
                        loop.close()

                        # Get updated guilds from database
                        guilds = db.execute_query(
                            "SELECT * FROM guilds WHERE is_accessible = 1 ORDER BY name",
                            fetch='all'
                        )
                        self.logger.info(f"Loaded {len(guilds)} guilds from Discord")
                    except Exception as e:
                        self.logger.error(f"Failed to load guilds from Discord: {e}")

                return jsonify({
                    'success': True,
                    'guilds': [dict(guild) for guild in guilds]
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/channels/<guild_id>')
        def get_channels(guild_id):
            """Get channels for guild"""
            try:
                channels = db.execute_query('''
                    SELECT *, 
                           (SELECT COUNT(*) FROM messages WHERE channel_id = channels.id) as message_count
                    FROM channels 
                    WHERE guild_id = ? AND type IN (0, 5, 11)
                    ORDER BY position, name
                ''', (guild_id,), fetch='all')
                
                return jsonify({
                    'success': True,
                    'channels': [dict(channel) for channel in channels]
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/messages/<channel_id>')
        def get_messages(channel_id):
            """Get messages for channel"""
            try:
                page = request.args.get('page', 0, type=int)
                limit = request.args.get('limit', 50, type=int)
                offset = page * limit
                
                messages = db.execute_query('''
                    SELECT * FROM messages 
                    WHERE channel_id = ? 
                    ORDER BY timestamp ASC
                    LIMIT ? OFFSET ?
                ''', (channel_id, limit, offset), fetch='all')
                
                return jsonify({
                    'success': True,
                    'messages': [dict(message) for message in messages]
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/scan-guild/<guild_id>', methods=['POST'])
        def scan_guild(guild_id):
            """Trigger guild scan"""
            try:
                if not scanner.is_running:
                    return jsonify({'success': False, 'error': 'Scanner not running'})
                
                # Add scan task
                asyncio.create_task(scanner.scan_guild(guild_id))
                
                return jsonify({
                    'success': True,
                    'message': f'Scan initiated for guild {guild_id}'
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
    
    def _setup_socketio(self):
        """Setup SocketIO events"""
        
        @self.socketio.on('connect')
        def handle_connect():
            self.logger.info('Client connected to WebSocket')
            emit('status', {'connected': True})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            self.logger.info('Client disconnected from WebSocket')
        
        @self.socketio.on('request_metrics')
        def handle_metrics_request():
            """Send real-time metrics"""
            try:
                metrics = {
                    'database': db.get_metrics(),
                    'discord_client': discord_client.get_metrics(),
                    'scanner': scanner.get_metrics(),
                    'timestamp': datetime.now().isoformat()
                }
                emit('metrics_update', metrics)
            except Exception as e:
                self.logger.error(f"Error sending metrics: {e}")
    
    async def start(self):
        """Start the application"""
        try:
            self.logger.info("Starting Enterprise Discord Logger")
            
            # Validate configuration (but allow missing token for setup)
            config_errors = config.validate()
            token_missing = any("Discord token" in error for error in config_errors)

            if config_errors and not token_missing:
                for error in config_errors:
                    self.logger.error(f"Configuration error: {error}")
                return False
            elif token_missing:
                self.logger.warning("Discord token not configured - setup required")
            else:
                self.logger.info("Configuration validated successfully")
            
            # Initialize Discord client if token is available
            if config.discord.token:
                try:
                    user_info = await discord_client.get_user_info()
                    if user_info:
                        self.logger.info(f"Discord client authenticated as {user_info['username']}")

                        # Load initial data
                        await discord_client.get_guilds()

                        # Start scanner
                        scanner_task = asyncio.create_task(scanner.start())
                        self.background_tasks.append(scanner_task)
                    else:
                        self.logger.warning("Discord authentication failed")
                except Exception as e:
                    self.logger.error(f"Discord client initialization failed: {e}")
            else:
                self.logger.info("Discord token not provided - web interface available for setup")
            
            # Start metrics broadcasting
            metrics_task = asyncio.create_task(self._broadcast_metrics())
            self.background_tasks.append(metrics_task)
            
            self.is_running = True
            
            # Start web server in a separate thread
            self.logger.info(f"Starting web server on {config.web.host}:{config.web.port}")

            def run_flask():
                self.socketio.run(
                    self.app,
                    host=config.web.host,
                    port=config.web.port,
                    debug=False,  # Disable debug in thread
                    allow_unsafe_werkzeug=True
                )

            # Run Flask in a separate thread
            import threading
            flask_thread = threading.Thread(target=run_flask, daemon=True)
            flask_thread.start()

            # Keep the main thread alive
            try:
                while self.is_running:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("Received keyboard interrupt")
                await self.shutdown()
            
        except Exception as e:
            self.logger.error(f"Failed to start application: {e}")
            return False
    
    async def shutdown(self):
        """Graceful shutdown"""
        self.logger.info("Shutting down Enterprise Discord Logger")
        
        self.is_running = False
        
        # Stop scanner
        await scanner.stop()
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        # Close Discord client
        await discord_client.close()
        
        self.logger.info("Shutdown complete")
        sys.exit(0)
    
    async def _broadcast_metrics(self):
        """Broadcast metrics to connected clients"""
        while self.is_running:
            try:
                await asyncio.sleep(config.monitoring.metrics_interval)
                
                if config.monitoring.enable_metrics:
                    metrics = {
                        'database': db.get_metrics(),
                        'discord_client': discord_client.get_metrics(),
                        'scanner': scanner.get_metrics(),
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    self.socketio.emit('metrics_update', metrics)
            
            except Exception as e:
                self.logger.error(f"Error broadcasting metrics: {e}")

def main():
    """Main entry point"""
    app = EnterpriseDiscordLogger()
    
    # Run the application
    try:
        asyncio.run(app.start())
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Application error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
