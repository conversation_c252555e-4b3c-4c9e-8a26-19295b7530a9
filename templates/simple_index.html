<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Logger</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #36393f;
            color: #dcddde;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
        }
        .sidebar {
            width: 300px;
            background: #2f3136;
            border-right: 1px solid #202225;
            overflow-y: auto;
        }
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .header {
            height: 48px;
            background: #36393f;
            border-bottom: 1px solid #202225;
            display: flex;
            align-items: center;
            padding: 0 16px;
        }
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        .guild-item, .channel-item {
            padding: 8px 16px;
            cursor: pointer;
            border-bottom: 1px solid #202225;
        }
        .guild-item:hover, .channel-item:hover {
            background: #40444b;
        }
        .guild-item.active, .channel-item.active {
            background: #5865f2;
        }
        .message {
            margin-bottom: 16px;
            padding: 8px;
            background: #40444b;
            border-radius: 4px;
        }
        .message-author {
            font-weight: bold;
            color: #5865f2;
            margin-bottom: 4px;
        }
        .message-content {
            color: #dcddde;
        }
        .message-time {
            font-size: 12px;
            color: #72767d;
            margin-top: 4px;
        }
        .loading {
            text-align: center;
            color: #72767d;
            padding: 20px;
        }
        button {
            background: #5865f2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #4752c4;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div style="padding: 16px; border-bottom: 1px solid #202225;">
            <h3>Серверы</h3>
            <button onclick="loadGuilds()">Загрузить серверы</button>
        </div>
        <div id="guilds-list"></div>
        
        <div style="padding: 16px; border-bottom: 1px solid #202225;">
            <h3>Каналы</h3>
        </div>
        <div id="channels-list"></div>
    </div>
    
    <div class="main-content">
        <div class="header">
            <h2 id="current-channel">Discord Logger</h2>
        </div>
        <div class="content">
            <div id="messages-container">
                <div class="loading">
                    <h3>Добро пожаловать в Discord Logger!</h3>
                    <p>Нажмите "Загрузить серверы" для начала</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentGuild = null;
        let currentChannel = null;

        async function loadGuilds() {
            try {
                const response = await fetch('/api/guilds');
                const data = await response.json();
                
                if (data.success) {
                    const guildsList = document.getElementById('guilds-list');
                    guildsList.innerHTML = '';
                    
                    data.guilds.forEach(guild => {
                        const div = document.createElement('div');
                        div.className = 'guild-item';
                        div.textContent = guild.name;
                        div.onclick = () => selectGuild(guild.id, guild.name);
                        guildsList.appendChild(div);
                    });
                    
                    document.getElementById('messages-container').innerHTML = 
                        '<div class="loading"><h3>Серверы загружены!</h3><p>Выберите сервер для просмотра каналов</p></div>';
                } else {
                    alert('Ошибка загрузки серверов: ' + data.error);
                }
            } catch (error) {
                alert('Ошибка: ' + error.message);
            }
        }

        async function selectGuild(guildId, guildName) {
            currentGuild = guildId;
            
            // Update active guild
            document.querySelectorAll('.guild-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Load channels
            try {
                const response = await fetch(`/api/channels/${guildId}`);
                const data = await response.json();
                
                if (data.success) {
                    const channelsList = document.getElementById('channels-list');
                    channelsList.innerHTML = '';
                    
                    data.channels.forEach(channel => {
                        if (channel.type === 0) { // Text channels only
                            const div = document.createElement('div');
                            div.className = 'channel-item';
                            div.textContent = '# ' + channel.name;
                            div.onclick = () => selectChannel(channel.id, channel.name);
                            channelsList.appendChild(div);
                        }
                    });
                    
                    document.getElementById('messages-container').innerHTML = 
                        `<div class="loading"><h3>Каналы сервера "${guildName}"</h3><p>Выберите канал для просмотра сообщений</p></div>`;
                } else {
                    alert('Ошибка загрузки каналов: ' + data.error);
                }
            } catch (error) {
                alert('Ошибка: ' + error.message);
            }
        }

        async function selectChannel(channelId, channelName) {
            currentChannel = channelId;
            
            // Update active channel
            document.querySelectorAll('.channel-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            document.getElementById('current-channel').textContent = '# ' + channelName;
            document.getElementById('messages-container').innerHTML = 
                '<div class="loading">Загрузка сообщений...</div>';
            
            // Load messages
            try {
                const response = await fetch(`/api/messages/${channelId}`);
                const data = await response.json();
                
                if (data.success) {
                    const messagesContainer = document.getElementById('messages-container');
                    messagesContainer.innerHTML = '';
                    
                    if (data.messages.length === 0) {
                        messagesContainer.innerHTML = '<div class="loading">В этом канале нет сообщений</div>';
                    } else {
                        data.messages.reverse().forEach(message => {
                            const div = document.createElement('div');
                            div.className = 'message';
                            
                            const time = new Date(message.timestamp).toLocaleString();
                            
                            div.innerHTML = `
                                <div class="message-author">${message.author_name}</div>
                                <div class="message-content">${message.content || '<em>Нет текста</em>'}</div>
                                <div class="message-time">${time}</div>
                            `;
                            
                            messagesContainer.appendChild(div);
                        });
                    }
                } else {
                    document.getElementById('messages-container').innerHTML = 
                        '<div class="loading">Ошибка загрузки сообщений</div>';
                }
            } catch (error) {
                document.getElementById('messages-container').innerHTML = 
                    '<div class="loading">Ошибка: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
