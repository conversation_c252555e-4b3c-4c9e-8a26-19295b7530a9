<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Logger</title>
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: 'Whitney', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: #36393f;
            color: #dcddde;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            overflow: hidden;
        }
        .sidebar {
            width: 240px;
            background: #2f3136;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #202225;
        }
        .sidebar-section {
            padding: 16px;
            border-bottom: 1px solid #202225;
        }
        .sidebar-section h3 {
            margin: 0 0 12px 0;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            color: #8e9297;
            letter-spacing: 0.02em;
        }
        .sidebar-content {
            flex: 1;
            overflow-y: auto;
        }
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #36393f;
        }
        .header {
            height: 48px;
            background: #36393f;
            border-bottom: 1px solid #202225;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            box-shadow: 0 1px 0 rgba(4,4,5,0.2), 0 1.5px 0 rgba(6,6,7,0.05), 0 2px 0 rgba(4,4,5,0.05);
        }
        .header h2 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #b9bbbe;
        }
        .auto-refresh input[type="checkbox"] {
            margin: 0;
        }
        .content {
            flex: 1;
            padding: 0;
            overflow-y: auto;
            background: #36393f;
        }
        .guild-item, .channel-item {
            padding: 6px 8px;
            margin: 1px 8px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            color: #8e9297;
            transition: all 0.15s ease-out;
            display: flex;
            align-items: center;
            min-height: 32px;
        }
        .guild-item:hover, .channel-item:hover {
            background: #393c43;
            color: #dcddde;
        }
        .guild-item.active, .channel-item.active {
            background: #5865f2;
            color: #ffffff;
        }
        .channel-item {
            font-size: 14px;
            padding-left: 16px;
        }
        .channel-item::before {
            content: '#';
            margin-right: 6px;
            font-weight: 300;
            color: #8e9297;
        }
        .channel-item.active::before {
            color: #ffffff;
        }
        .channel-item.has-cached-messages {
            color: #faa61a;
            position: relative;
        }
        .channel-item.has-cached-messages::after {
            content: '💾';
            position: absolute;
            right: 8px;
            font-size: 12px;
        }
        .channel-item.temporarily-accessible {
            color: #3ba55d;
        }
        .channel-item.temporarily-accessible::after {
            content: '⚡';
            position: absolute;
            right: 8px;
            font-size: 12px;
        }
        .messages-container {
            padding: 16px;
        }
        .message {
            margin-bottom: 17px;
            padding: 2px 0;
            position: relative;
            display: flex;
            align-items: flex-start;
            min-height: 44px;
            padding-left: 72px;
            word-wrap: break-word;
        }
        .message:hover {
            background: rgba(4,4,5,0.07);
            margin-left: -72px;
            margin-right: -16px;
            padding-left: 72px;
            padding-right: 16px;
        }
        .message-avatar {
            position: absolute;
            left: 16px;
            top: 2px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            font-size: 16px;
        }
        .message-content-wrapper {
            flex: 1;
            min-width: 0;
        }
        .message-header {
            display: flex;
            align-items: baseline;
            margin-bottom: 2px;
        }
        .message-author {
            font-weight: 500;
            color: #ffffff;
            margin-right: 8px;
            font-size: 16px;
        }
        .message-time {
            font-size: 12px;
            color: #a3a6aa;
            font-weight: 400;
        }
        .message-content {
            color: #dcddde;
            line-height: 1.375;
            font-size: 16px;
            word-wrap: break-word;
        }
        .message-deleted {
            opacity: 0.6;
            background: rgba(240, 71, 71, 0.1);
            border-left: 4px solid #f04747;
            padding-left: 8px;
            margin-left: -12px;
        }
        .message-deleted .message-content::before {
            content: '[УДАЛЕНО] ';
            color: #f04747;
            font-weight: 600;
        }
        .loading {
            text-align: center;
            color: #72767d;
            padding: 40px 20px;
        }
        .loading h3 {
            margin: 0 0 8px 0;
            color: #ffffff;
        }
        button {
            background: #5865f2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.17s ease;
        }
        button:hover {
            background: #4752c4;
        }
        button:active {
            background: #3c45a5;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online {
            background: #3ba55d;
        }
        .status-updating {
            background: #faa61a;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-content">
            <div id="guilds-list"></div>

            <div class="sidebar-section" style="border-top: 1px solid #202225; border-bottom: none;">
                <h3>Каналы</h3>
                <button id="scan-button" onclick="manualScan()" style="display: none; font-size: 12px; padding: 4px 8px;">🔍 Сканировать</button>
            </div>
            <div id="channels-list"></div>
        </div>
    </div>

    <div class="main-content">
        <div class="header">
            <h2 id="current-channel">Discord Logger</h2>
            <div class="auto-refresh">
                <span class="status-indicator status-online" id="status-indicator"></span>
                <input type="checkbox" id="auto-refresh-toggle" checked>
                <label for="auto-refresh-toggle">Авто-обновление</label>
                <span id="refresh-status">Готов</span>
            </div>
        </div>
        <div class="content">
            <div class="messages-container" id="messages-container">
                <div class="loading">
                    <h3>Добро пожаловать в Discord Logger!</h3>
                    <p>Нажмите "Загрузить серверы" для начала</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentGuild = null;
        let currentChannel = null;
        let autoRefreshInterval = null;
        let knownMessages = new Set();

        function updateStatus(status, text) {
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('refresh-status');

            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        function getInitials(name) {
            return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
        }

        function formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

            if (messageDate.getTime() === today.getTime()) {
                return date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });
            } else {
                return date.toLocaleDateString('ru-RU', { day: '2-digit', month: '2-digit' }) + ' ' +
                       date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });
            }
        }

        async function loadGuilds() {
            try {
                updateStatus('updating', 'Загрузка серверов...');
                const response = await fetch('/api/guilds');
                const data = await response.json();

                if (data.success) {
                    const guildsList = document.getElementById('guilds-list');
                    guildsList.innerHTML = '';

                    data.guilds.forEach(guild => {
                        const div = document.createElement('div');
                        div.className = 'guild-item';
                        div.textContent = guild.name;
                        div.onclick = () => selectGuild(guild.id, guild.name);
                        guildsList.appendChild(div);
                    });

                    document.getElementById('messages-container').innerHTML =
                        '<div class="loading"><h3>Discord Logger</h3><p>Выберите сервер для просмотра каналов</p></div>';
                    updateStatus('online', 'Готов');
                } else {
                    updateStatus('online', 'Ошибка');
                    console.error('Ошибка загрузки серверов:', data.error);
                }
            } catch (error) {
                updateStatus('online', 'Ошибка');
                console.error('Ошибка:', error);
            }
        }

        // Auto-load guilds on page load
        window.addEventListener('load', async () => {
            await loadGuilds();
        });

        async function manualScan() {
            if (!currentGuild) {
                alert('Сначала выберите сервер');
                return;
            }

            const button = document.getElementById('scan-button');
            const originalText = button.textContent;
            button.textContent = '🔄 Сканирование...';
            button.disabled = true;

            try {
                const guildName = document.querySelector('.guild-item.active').textContent;
                await scanGuildChannels(currentGuild, guildName);
            } finally {
                button.textContent = originalText;
                button.disabled = false;
            }
        }

        async function selectGuild(guildId, guildName) {
            currentGuild = guildId;
            currentChannel = null;
            stopAutoRefresh();

            // Update active guild
            document.querySelectorAll('.guild-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // Clear channels and show scan button
            document.getElementById('channels-list').innerHTML = '';
            document.getElementById('scan-button').style.display = 'inline-block';

            // Load channels and start aggressive scanning
            try {
                updateStatus('updating', 'Загрузка каналов...');
                const response = await fetch(`/api/channels/${guildId}`);
                const data = await response.json();

                if (data.success) {
                    displayChannels(data.channels, guildName);

                    // Start aggressive scanning in background
                    updateStatus('updating', 'Сканирование каналов...');
                    scanGuildChannels(guildId, guildName);

                } else {
                    updateStatus('online', 'Ошибка');
                    console.error('Ошибка загрузки каналов:', data.error);
                }
            } catch (error) {
                updateStatus('online', 'Ошибка');
                console.error('Ошибка:', error);
            }
        }

        function displayChannels(channels, guildName) {
            const channelsList = document.getElementById('channels-list');
            channelsList.innerHTML = '';

            channels.forEach(channel => {
                if (channel.type === 0) { // Text channels only
                    const div = document.createElement('div');
                    div.className = 'channel-item';

                    // Add special classes for channels with cached messages
                    if (channel.has_cached_messages) {
                        div.classList.add('has-cached-messages');
                    }
                    if (channel.is_temporarily_accessible) {
                        div.classList.add('temporarily-accessible');
                    }

                    div.textContent = channel.name;
                    div.onclick = () => selectChannel(channel.id, channel.name);
                    channelsList.appendChild(div);
                }
            });

            document.getElementById('messages-container').innerHTML =
                `<div class="loading"><h3>Каналы сервера "${guildName}"</h3><p>Сканирование каналов... Выберите канал для просмотра сообщений</p></div>`;
        }

        async function scanGuildChannels(guildId, guildName) {
            try {
                console.log(`Starting aggressive scan for guild ${guildId}`);
                const response = await fetch(`/api/scan-guild/${guildId}`, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    console.log(`Scan complete: ${data.accessible_channels} accessible channels found`);

                    // Reload channels to show updated status
                    const channelsResponse = await fetch(`/api/channels/${guildId}`);
                    const channelsData = await channelsResponse.json();

                    if (channelsData.success) {
                        displayChannels(channelsData.channels, guildName);
                    }

                    updateStatus('online', `Готов (найдено ${data.accessible_channels} доступных каналов)`);
                } else {
                    console.error('Scan failed:', data.error);
                    updateStatus('online', 'Готов');
                }
            } catch (error) {
                console.error('Error during scan:', error);
                updateStatus('online', 'Готов');
            }
        }

        async function selectChannel(channelId, channelName) {
            currentChannel = channelId;
            knownMessages.clear();

            // Update active channel
            document.querySelectorAll('.channel-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            document.getElementById('current-channel').textContent = '# ' + channelName;

            // Load messages
            await loadMessages(true);

            // Start auto-refresh if enabled
            if (document.getElementById('auto-refresh-toggle').checked) {
                startAutoRefresh();
            }
        }

        async function loadMessages(isInitial = false) {
            if (!currentChannel) return;

            try {
                if (isInitial) {
                    updateStatus('updating', 'Загрузка сообщений...');
                    document.getElementById('messages-container').innerHTML =
                        '<div class="loading">Загрузка сообщений...</div>';
                }

                console.log(`Loading messages for channel ${currentChannel}, initial: ${isInitial}`);
                const response = await fetch(`/api/messages/${currentChannel}`);
                const data = await response.json();
                console.log('Messages response:', data);

                if (data.success) {
                    console.log(`Got ${data.messages.length} messages`);
                    displayMessages(data.messages, isInitial);
                    if (isInitial) updateStatus('online', 'Готов');
                } else {
                    console.error('Failed to load messages:', data.error);
                    if (isInitial) {
                        document.getElementById('messages-container').innerHTML =
                            '<div class="loading">Ошибка загрузки сообщений: ' + data.error + '</div>';
                        updateStatus('online', 'Ошибка');
                    }
                }
            } catch (error) {
                console.error('Error loading messages:', error);
                if (isInitial) {
                    document.getElementById('messages-container').innerHTML =
                        '<div class="loading">Ошибка: ' + error.message + '</div>';
                    updateStatus('online', 'Ошибка');
                }
            }
        }

        function displayMessages(messages, isInitial = false) {
            const messagesContainer = document.getElementById('messages-container');

            console.log(`Displaying ${messages.length} messages, initial: ${isInitial}`);

            if (isInitial) {
                messagesContainer.innerHTML = '';
                knownMessages.clear();
            }

            if (messages.length === 0) {
                if (isInitial) {
                    messagesContainer.innerHTML = '<div class="loading">В этом канале нет сообщений</div>';
                }
                return;
            }

            // Add new messages
            messages.forEach(message => {
                if (!knownMessages.has(message.id)) {
                    knownMessages.add(message.id);

                    const div = document.createElement('div');
                    div.className = 'message';
                    div.dataset.messageId = message.id;

                    const initials = getInitials(message.author_name);
                    const time = formatTime(message.timestamp);

                    div.innerHTML = `
                        <div class="message-avatar">${initials}</div>
                        <div class="message-content-wrapper">
                            <div class="message-header">
                                <span class="message-author">${message.author_name}</span>
                                <span class="message-time">${time}</span>
                            </div>
                            <div class="message-content">${message.content || '<em>Нет текста</em>'}</div>
                        </div>
                    `;

                    messagesContainer.appendChild(div);
                    console.log(`Added message from ${message.author_name}: ${message.content.substring(0, 50)}...`);
                }
            });

            // Scroll to bottom on initial load
            if (isInitial) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }

        function startAutoRefresh() {
            stopAutoRefresh();
            autoRefreshInterval = setInterval(async () => {
                if (currentChannel && document.getElementById('auto-refresh-toggle').checked) {
                    updateStatus('updating', 'Обновление...');
                    await loadMessages(false);
                    updateStatus('online', 'Готов');
                }
            }, 500); // Обновление каждые 500мс для реального времени
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }

        // Event listeners
        document.getElementById('auto-refresh-toggle').addEventListener('change', function() {
            if (this.checked && currentChannel) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });
    </script>
</body>
</html>
