{% extends "base.html" %}

{% block title %}Discord Logger{% endblock %}

{% block content %}
<div class="discord-layout">
    <!-- Sidebar with servers -->
    <div class="sidebar">
        <div class="server-list">
            <div class="server-item dm-button" id="dm-button">
                <div class="server-icon">DM</div>
            </div>
            <div class="server-divider"></div>
            <div id="guild-list">
                <!-- Guilds will be loaded here -->
            </div>
        </div>
    </div>
    
    <!-- Channel list -->
    <div class="channel-list">
        <div class="channel-header">
            <h3 id="channel-header-title">Выберите сервер</h3>
        </div>
        <div class="channel-container" id="channel-container">
            <!-- Channels will be loaded here -->
        </div>
    </div>
    
    <!-- Main content area -->
    <div class="main-content">
        <div class="chat-header">
            <h2 id="chat-title">Добро пожаловать в Discord Logger</h2>
            <div class="status-indicator" id="status-indicator">
                <span class="status-dot"></span>
                <span id="status-text">Подключение...</span>
            </div>
        </div>
        
        <div class="message-container" id="message-container">
            <div class="welcome-message">
                <h3>Discord Logger</h3>
                <p>Выберите канал слева для просмотра сообщений</p>
            </div>
        </div>
        
        <div class="message-input-container">
            <div class="message-info">
                <small>Логгер работает в режиме только чтения</small>
            </div>
        </div>
    </div>
</div>

<!-- Loading overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="loading-spinner"></div>
    <p>Загрузка...</p>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize the Discord Logger app
document.addEventListener('DOMContentLoaded', function() {
    window.discordLogger = new DiscordLoggerApp();
});
</script>
{% endblock %}
