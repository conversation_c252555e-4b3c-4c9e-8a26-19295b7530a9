<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Logger - Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #36393f;
            color: #dcddde;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .setup-container {
            background: #2f3136;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 400px;
        }
        h1 {
            text-align: center;
            color: #5865f2;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #b9bbbe;
        }
        input {
            width: 100%;
            padding: 12px;
            background: #40444b;
            border: 1px solid #202225;
            border-radius: 4px;
            color: #dcddde;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #5865f2;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #5865f2;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background: #4752c4;
        }
        button:disabled {
            background: #4f545c;
            cursor: not-allowed;
        }
        .help {
            margin-top: 20px;
            padding: 15px;
            background: #40444b;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.5;
        }
        .help h3 {
            margin-top: 0;
            color: #5865f2;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <h1>Discord Logger</h1>
        
        <div class="form-group">
            <label for="token">Discord Token:</label>
            <input type="password" id="token" placeholder="Введите ваш Discord токен">
        </div>
        
        <button id="connect-btn">Подключиться</button>
        
        <div class="help">
            <h3>Как получить токен:</h3>
            <ol>
                <li>Откройте Discord в браузере</li>
                <li>Нажмите F12 → Network</li>
                <li>Обновите страницу (F5)</li>
                <li>Найдите запрос к API Discord</li>
                <li>Скопируйте токен из заголовка Authorization</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('connect-btn').addEventListener('click', async function() {
            const token = document.getElementById('token').value.trim();
            const button = this;
            
            if (!token) {
                alert('Пожалуйста, введите Discord токен');
                return;
            }
            
            button.disabled = true;
            button.textContent = 'Подключение...';
            
            try {
                const response = await fetch('/api/setup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token: token })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`Подключен как ${data.user.username}!`);
                    window.location.reload();
                } else {
                    alert('Ошибка: ' + data.error);
                    button.disabled = false;
                    button.textContent = 'Подключиться';
                }
            } catch (error) {
                alert('Ошибка подключения: ' + error.message);
                button.disabled = false;
                button.textContent = 'Подключиться';
            }
        });
    </script>
</body>
</html>
