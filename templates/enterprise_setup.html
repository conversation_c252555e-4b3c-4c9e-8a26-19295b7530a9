<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Discord Logger - Setup</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .setup-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo i {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .logo h1 {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }
        
        .logo p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .input-wrapper {
            position: relative;
        }
        
        .input-wrapper i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 16px;
        }
        
        input[type="password"] {
            width: 100%;
            padding: 15px 15px 15px 45px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn-primary {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-primary:active {
            transform: translateY(0);
        }
        
        .btn-primary:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .help-section {
            margin-top: 30px;
            padding: 20px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .help-section h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .help-section ol {
            padding-left: 20px;
            line-height: 1.6;
        }
        
        .help-section li {
            margin-bottom: 8px;
            color: #555;
            font-size: 14px;
        }
        
        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
            display: none;
        }
        
        .status-success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }
        
        .status-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.2);
        }
        
        .features {
            margin-top: 30px;
            text-align: center;
        }
        
        .features h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .feature-list {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .feature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            flex: 1;
            min-width: 120px;
        }
        
        .feature-item i {
            font-size: 24px;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .feature-item span {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="logo">
            <i class="fas fa-shield-alt"></i>
            <h1>Enterprise Discord Logger</h1>
            <p>Professional-grade Discord monitoring solution</p>
        </div>
        
        <div id="status-message" class="status-message"></div>
        
        <form id="setup-form">
            <div class="form-group">
                <label for="token">Discord Authorization Token</label>
                <div class="input-wrapper">
                    <i class="fas fa-key"></i>
                    <input type="password" id="token" placeholder="Enter your Discord token" required>
                </div>
            </div>
            
            <button type="submit" class="btn-primary" id="connect-btn">
                <span class="loading-spinner" id="loading-spinner"></span>
                <span id="btn-text">Initialize System</span>
            </button>
        </form>
        
        <div class="features">
            <h4>Enterprise Features</h4>
            <div class="feature-list">
                <div class="feature-item">
                    <i class="fas fa-bolt"></i>
                    <span>Real-time Monitoring</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-database"></i>
                    <span>Advanced Database</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-chart-line"></i>
                    <span>Analytics & Metrics</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-cogs"></i>
                    <span>Intelligent Scanning</span>
                </div>
            </div>
        </div>
        
        <div class="help-section">
            <h3><i class="fas fa-info-circle"></i> How to get your Discord token:</h3>
            <ol>
                <li>Open Discord in your web browser</li>
                <li>Press F12 to open Developer Tools</li>
                <li>Go to the Network tab</li>
                <li>Refresh the page (F5)</li>
                <li>Look for any request to discord.com/api</li>
                <li>In the request headers, find "Authorization"</li>
                <li>Copy the token value (without "Bearer " prefix)</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('setup-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const token = document.getElementById('token').value.trim();
            const button = document.getElementById('connect-btn');
            const btnText = document.getElementById('btn-text');
            const spinner = document.getElementById('loading-spinner');
            const statusMessage = document.getElementById('status-message');
            
            if (!token) {
                showStatus('Please enter your Discord token', 'error');
                return;
            }
            
            // Show loading state
            button.disabled = true;
            spinner.style.display = 'inline-block';
            btnText.textContent = 'Initializing...';
            statusMessage.style.display = 'none';
            
            try {
                const response = await fetch('/api/setup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token: token })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus(`Successfully authenticated as ${data.user.username}!`, 'success');
                    btnText.textContent = 'Redirecting...';
                    
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                } else {
                    showStatus('Authentication failed: ' + data.error, 'error');
                    resetButton();
                }
            } catch (error) {
                showStatus('Connection error: ' + error.message, 'error');
                resetButton();
            }
            
            function resetButton() {
                button.disabled = false;
                spinner.style.display = 'none';
                btnText.textContent = 'Initialize System';
            }
            
            function showStatus(message, type) {
                statusMessage.textContent = message;
                statusMessage.className = `status-message status-${type}`;
                statusMessage.style.display = 'block';
            }
        });
    </script>
</body>
</html>
