<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Discord Logger</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: #2c2c2c;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #404040;
            background: #333333;
        }
        
        .sidebar-header h2 {
            color: #ffffff;
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #888;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .sidebar-content {
            flex: 1;
            overflow-y: auto;
        }
        
        .section {
            padding: 15px 20px;
            border-bottom: 1px solid #404040;
        }
        
        .section h3 {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 10px;
            letter-spacing: 1px;
        }
        
        .guild-item, .channel-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .guild-item:hover, .channel-item:hover {
            background: #404040;
        }
        
        .guild-item.active, .channel-item.active {
            background: #5865f2;
            color: white;
        }
        
        .channel-item {
            padding-left: 20px;
            font-size: 13px;
        }
        
        .channel-item.has-messages {
            color: #faa61a;
        }
        
        .channel-item.temporarily-accessible {
            color: #3ba55d;
        }
        
        .channel-item.temporarily-accessible::after {
            content: '⚡';
            font-size: 10px;
        }
        
        .channel-item.has-messages::after {
            content: '💾';
            font-size: 10px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1a1a1a;
        }
        
        .main-header {
            height: 60px;
            background: #2c2c2c;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .main-header h1 {
            font-size: 18px;
            color: #ffffff;
        }
        
        .header-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #5865f2;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4752c4;
        }
        
        .btn-secondary {
            background: #404040;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #505050;
        }
        
        .metrics-display {
            display: flex;
            gap: 15px;
            font-size: 11px;
            color: #888;
        }
        
        .metric-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .metric-value {
            font-weight: bold;
            color: #ffffff;
        }
        
        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .message-container {
            max-width: 800px;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #333;
            display: flex;
            gap: 12px;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            color: white;
            flex-shrink: 0;
        }
        
        .message-content {
            flex: 1;
        }
        
        .message-header {
            display: flex;
            align-items: baseline;
            gap: 10px;
            margin-bottom: 5px;
        }
        
        .message-author {
            font-weight: 600;
            color: #ffffff;
        }
        
        .message-time {
            font-size: 11px;
            color: #888;
        }
        
        .message-text {
            color: #cccccc;
            line-height: 1.4;
            word-wrap: break-word;
        }
        
        .welcome-screen {
            text-align: center;
            padding: 60px 20px;
            color: #888;
        }
        
        .welcome-screen i {
            font-size: 64px;
            margin-bottom: 20px;
            color: #5865f2;
        }
        
        .welcome-screen h2 {
            margin-bottom: 10px;
            color: #ffffff;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #888;
        }
        
        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .scan-progress {
            background: #333;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #5865f2;
        }
        
        .scan-progress h4 {
            margin-bottom: 8px;
            color: #ffffff;
        }
        
        .progress-bar {
            background: #404040;
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            background: #5865f2;
            height: 100%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> Enterprise Logger</h2>
                <div class="status-indicator">
                    <div class="status-dot" id="status-dot"></div>
                    <span id="status-text">Connected</span>
                </div>
            </div>
            
            <div class="sidebar-content">
                <div class="section">
                    <h3>Servers</h3>
                    <button class="btn btn-secondary" onclick="loadGuilds()" style="width: 100%; margin-bottom: 10px; font-size: 11px;">
                        <i class="fas fa-sync"></i> Refresh Servers
                    </button>
                    <div id="guilds-list">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            Loading servers...
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h3>Channels</h3>
                    <div id="channels-list">
                        <div style="color: #666; font-size: 12px; padding: 10px;">
                            Select a server to view channels
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="main-header">
                <h1 id="current-channel">Enterprise Discord Logger</h1>
                <div class="header-controls">
                    <div class="metrics-display" id="metrics-display">
                        <div class="metric-item">
                            <div class="metric-value" id="total-messages">0</div>
                            <div>Messages</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="active-scans">0</div>
                            <div>Active Scans</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="success-rate">0%</div>
                            <div>Success Rate</div>
                        </div>
                    </div>
                    <button class="btn btn-secondary" onclick="showMetrics()">
                        <i class="fas fa-chart-bar"></i> Metrics
                    </button>
                    <button class="btn btn-primary" onclick="scanCurrentGuild()" id="scan-btn">
                        <i class="fas fa-search"></i> Scan
                    </button>
                </div>
            </div>
            
            <div class="content-area">
                <div id="content-container">
                    <div class="welcome-screen">
                        <i class="fas fa-comments"></i>
                        <h2>Welcome to Enterprise Discord Logger</h2>
                        <p>Select a server and channel to view messages</p>
                        <p style="margin-top: 10px; font-size: 12px;">
                            🔍 Intelligent scanning • 💾 Persistent storage • ⚡ Real-time monitoring
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let socket;
        let currentGuild = null;
        let currentChannel = null;
        let metrics = {};
        
        // Initialize application
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            checkAuthAndLoadData();
            updateMetricsDisplay();
        });

        async function checkAuthAndLoadData() {
            try {
                // Check if user is authenticated
                const statusResponse = await fetch('/api/status');
                const statusData = await statusResponse.json();

                if (statusData.success && statusData.authenticated) {
                    console.log('User authenticated, loading data...');
                    await loadGuilds();
                } else {
                    console.log('User not authenticated');
                    document.getElementById('guilds-list').innerHTML = `
                        <div style="color: #666; font-size: 12px; padding: 10px;">
                            Please configure Discord token first
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error checking authentication:', error);
            }
        }
        
        function initializeSocket() {
            socket = io();
            
            socket.on('connect', function() {
                console.log('Connected to server');
                updateStatus('Connected', true);
            });
            
            socket.on('disconnect', function() {
                console.log('Disconnected from server');
                updateStatus('Disconnected', false);
            });
            
            socket.on('metrics_update', function(data) {
                metrics = data;
                updateMetricsDisplay();
            });
            
            // Request metrics every 30 seconds
            setInterval(() => {
                if (socket.connected) {
                    socket.emit('request_metrics');
                }
            }, 30000);
        }
        
        function updateStatus(text, connected) {
            document.getElementById('status-text').textContent = text;
            const dot = document.getElementById('status-dot');
            dot.style.background = connected ? '#28a745' : '#dc3545';
        }
        
        async function loadGuilds() {
            try {
                const response = await fetch('/api/guilds');
                const data = await response.json();
                
                if (data.success) {
                    displayGuilds(data.guilds);
                } else {
                    console.error('Failed to load guilds:', data.error);
                }
            } catch (error) {
                console.error('Error loading guilds:', error);
            }
        }
        
        function displayGuilds(guilds) {
            const container = document.getElementById('guilds-list');
            container.innerHTML = '';
            
            guilds.forEach(guild => {
                const div = document.createElement('div');
                div.className = 'guild-item';
                div.innerHTML = `
                    <i class="fas fa-server"></i>
                    <span>${guild.name}</span>
                `;
                div.onclick = () => selectGuild(guild.id, guild.name);
                container.appendChild(div);
            });
        }
        
        async function selectGuild(guildId, guildName) {
            currentGuild = guildId;
            currentChannel = null;
            
            // Update active guild
            document.querySelectorAll('.guild-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.guild-item').classList.add('active');
            
            // Load channels
            await loadChannels(guildId);
            
            // Update header
            document.getElementById('current-channel').textContent = guildName;
            
            // Show welcome message for guild
            document.getElementById('content-container').innerHTML = `
                <div class="welcome-screen">
                    <i class="fas fa-hashtag"></i>
                    <h2>${guildName}</h2>
                    <p>Select a channel to view messages</p>
                </div>
            `;
        }
        
        async function loadChannels(guildId) {
            try {
                const response = await fetch(`/api/channels/${guildId}`);
                const data = await response.json();
                
                if (data.success) {
                    displayChannels(data.channels);
                } else {
                    console.error('Failed to load channels:', data.error);
                }
            } catch (error) {
                console.error('Error loading channels:', error);
            }
        }
        
        function displayChannels(channels) {
            const container = document.getElementById('channels-list');
            container.innerHTML = '';
            
            channels.forEach(channel => {
                const div = document.createElement('div');
                div.className = 'channel-item';
                
                if (channel.message_count > 0) {
                    div.classList.add('has-messages');
                }
                if (channel.was_temporarily_accessible) {
                    div.classList.add('temporarily-accessible');
                }
                
                div.innerHTML = `
                    <i class="fas fa-hashtag"></i>
                    <span>${channel.name}</span>
                    ${channel.message_count > 0 ? `<small>(${channel.message_count})</small>` : ''}
                `;
                div.onclick = () => selectChannel(channel.id, channel.name);
                container.appendChild(div);
            });
        }
        
        async function selectChannel(channelId, channelName) {
            currentChannel = channelId;
            
            // Update active channel
            document.querySelectorAll('.channel-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.channel-item').classList.add('active');
            
            // Update header
            document.getElementById('current-channel').textContent = `# ${channelName}`;
            
            // Load messages
            await loadMessages(channelId);
        }
        
        async function loadMessages(channelId) {
            try {
                document.getElementById('content-container').innerHTML = `
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        Loading messages...
                    </div>
                `;
                
                const response = await fetch(`/api/messages/${channelId}`);
                const data = await response.json();
                
                if (data.success) {
                    displayMessages(data.messages);
                } else {
                    console.error('Failed to load messages:', data.error);
                }
            } catch (error) {
                console.error('Error loading messages:', error);
            }
        }
        
        function displayMessages(messages) {
            const container = document.getElementById('content-container');
            
            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="welcome-screen">
                        <i class="fas fa-inbox"></i>
                        <h2>No messages found</h2>
                        <p>This channel appears to be empty or inaccessible</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = '<div class="message-container"></div>';
            const messageContainer = container.querySelector('.message-container');
            
            messages.forEach(message => {
                const div = document.createElement('div');
                div.className = 'message';
                
                const initials = message.author_name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
                const time = new Date(message.timestamp).toLocaleString();
                
                div.innerHTML = `
                    <div class="message-avatar">${initials}</div>
                    <div class="message-content">
                        <div class="message-header">
                            <span class="message-author">${message.author_name}</span>
                            <span class="message-time">${time}</span>
                        </div>
                        <div class="message-text">${message.content || '<em>No text content</em>'}</div>
                    </div>
                `;
                
                messageContainer.appendChild(div);
            });
        }
        
        async function scanCurrentGuild() {
            if (!currentGuild) {
                alert('Please select a server first');
                return;
            }
            
            const btn = document.getElementById('scan-btn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';
            btn.disabled = true;
            
            try {
                const response = await fetch(`/api/scan-guild/${currentGuild}`, { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    // Reload channels to show updated status
                    await loadChannels(currentGuild);
                } else {
                    console.error('Scan failed:', data.error);
                }
            } catch (error) {
                console.error('Error during scan:', error);
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }
        
        function updateMetricsDisplay() {
            if (metrics.database) {
                document.getElementById('total-messages').textContent = 
                    metrics.database.total_queries || '0';
            }
            
            if (metrics.scanner) {
                document.getElementById('active-scans').textContent = 
                    metrics.scanner.active_scans || '0';
                document.getElementById('success-rate').textContent = 
                    Math.round((metrics.scanner.success_rate || 0) * 100) + '%';
            }
        }
        
        function showMetrics() {
            // TODO: Implement metrics modal
            console.log('Current metrics:', metrics);
            alert('Metrics: ' + JSON.stringify(metrics, null, 2));
        }
    </script>
</body>
</html>
