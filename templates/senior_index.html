<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Senior Discord Logger</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            height: 100vh;
            display: flex;
        }
        
        .sidebar {
            width: 300px;
            background: #2c2c2c;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            padding: 20px;
            background: #333;
            border-bottom: 1px solid #404040;
        }
        
        .header h1 {
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .setup-form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .setup-form input {
            padding: 10px;
            background: #404040;
            border: 1px solid #555;
            border-radius: 4px;
            color: white;
            font-size: 14px;
        }
        
        .setup-form button {
            padding: 10px;
            background: #5865f2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .setup-form button:hover {
            background: #4752c4;
        }
        
        .setup-form button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .section {
            margin-bottom: 20px;
        }
        
        .section h3 {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 10px;
            letter-spacing: 1px;
        }
        
        .item {
            padding: 8px 12px;
            margin: 2px 0;
            background: #333;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .item:hover {
            background: #404040;
        }
        
        .item.active {
            background: #5865f2;
        }
        
        .item.has-messages {
            border-left: 3px solid #faa61a;
        }
        
        .main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .main-header {
            height: 60px;
            background: #2c2c2c;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            background: #333;
            border-radius: 8px;
            border-left: 4px solid #5865f2;
        }
        
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .message-author {
            font-weight: bold;
            color: #5865f2;
        }
        
        .message-time {
            color: #888;
        }
        
        .message-content {
            color: #ddd;
            line-height: 1.4;
        }
        
        .welcome {
            text-align: center;
            padding: 60px 20px;
            color: #888;
        }
        
        .welcome h2 {
            margin-bottom: 10px;
            color: #fff;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status.success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }
        
        .status.error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        
        .btn {
            padding: 8px 16px;
            background: #5865f2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn:hover {
            background: #4752c4;
        }
        
        .btn-secondary {
            background: #666;
        }
        
        .btn-secondary:hover {
            background: #777;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="header">
            <h1>🛡️ Senior Discord Logger</h1>
            <div class="setup-form" id="setup-form">
                <input type="password" id="token-input" placeholder="Discord Token" />
                <button onclick="setupToken()" id="setup-btn">Connect</button>
            </div>
            <div id="status-message"></div>
        </div>
        
        <div class="content">
            <div class="section">
                <h3>Servers</h3>
                <button class="btn btn-secondary" onclick="loadGuilds()" style="width: 100%; margin-bottom: 10px;">
                    🔄 Refresh
                </button>
                <div id="guilds-list">
                    <div style="color: #666; padding: 10px; font-size: 12px;">
                        Enter Discord token to load servers
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h3>Channels</h3>
                <div id="channels-list">
                    <div style="color: #666; padding: 10px; font-size: 12px;">
                        Select a server first
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="main">
        <div class="main-header">
            <h2 id="current-title">Senior Discord Logger</h2>
            <div>
                <button class="btn btn-secondary" onclick="scanCurrentGuild()" id="scan-btn" disabled>
                    🔍 Scan Guild
                </button>
            </div>
        </div>
        
        <div class="main-content">
            <div id="main-content">
                <div class="welcome">
                    <h2>Welcome to Senior Discord Logger</h2>
                    <p>Professional-grade Discord monitoring system</p>
                    <p style="margin-top: 20px; font-size: 14px;">
                        1. Enter your Discord token<br>
                        2. Select a server<br>
                        3. Choose a channel to view messages
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentGuild = null;
        let currentChannel = null;
        let isAuthenticated = false;

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status-message');
            statusEl.innerHTML = `<div class="status ${type}">${message}</div>`;
            
            if (type === 'success') {
                setTimeout(() => statusEl.innerHTML = '', 3000);
            }
        }

        async function setupToken() {
            const token = document.getElementById('token-input').value.trim();
            const btn = document.getElementById('setup-btn');
            
            if (!token) {
                showStatus('Please enter a Discord token', 'error');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = 'Connecting...';
            
            try {
                const response = await fetch('/api/setup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus(`Connected as ${data.user.username}!`, 'success');
                    isAuthenticated = true;
                    document.getElementById('setup-form').style.display = 'none';
                    await loadGuilds();
                } else {
                    showStatus(`Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus(`Connection failed: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = 'Connect';
            }
        }

        async function loadGuilds() {
            try {
                const response = await fetch('/api/guilds');
                const data = await response.json();
                
                if (data.success) {
                    displayGuilds(data.guilds);
                } else {
                    showStatus(`Failed to load guilds: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus(`Error loading guilds: ${error.message}`, 'error');
            }
        }

        function displayGuilds(guilds) {
            const container = document.getElementById('guilds-list');
            
            if (guilds.length === 0) {
                container.innerHTML = '<div style="color: #666; padding: 10px; font-size: 12px;">No servers found</div>';
                return;
            }
            
            container.innerHTML = '';
            guilds.forEach(guild => {
                const div = document.createElement('div');
                div.className = 'item';
                div.innerHTML = `
                    <span>${guild.name}</span>
                    <small>${guild.member_count || 0}</small>
                `;
                div.onclick = () => selectGuild(guild.id, guild.name);
                container.appendChild(div);
            });
        }

        async function selectGuild(guildId, guildName) {
            currentGuild = guildId;
            
            // Update active guild
            document.querySelectorAll('#guilds-list .item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.item').classList.add('active');
            
            document.getElementById('current-title').textContent = guildName;
            document.getElementById('scan-btn').disabled = false;
            
            await loadChannels(guildId);
        }

        async function loadChannels(guildId) {
            try {
                const response = await fetch(`/api/channels/${guildId}`);
                const data = await response.json();
                
                if (data.success) {
                    displayChannels(data.channels);
                } else {
                    showStatus(`Failed to load channels: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus(`Error loading channels: ${error.message}`, 'error');
            }
        }

        function displayChannels(channels) {
            const container = document.getElementById('channels-list');
            
            if (channels.length === 0) {
                container.innerHTML = '<div style="color: #666; padding: 10px; font-size: 12px;">No channels found</div>';
                return;
            }
            
            container.innerHTML = '';
            channels.forEach(channel => {
                const div = document.createElement('div');
                div.className = 'item';
                if (channel.has_messages) {
                    div.classList.add('has-messages');
                }
                
                div.innerHTML = `
                    <span># ${channel.name}</span>
                    <small>${channel.message_count || 0}</small>
                `;
                div.onclick = () => selectChannel(channel.id, channel.name);
                container.appendChild(div);
            });
        }

        async function selectChannel(channelId, channelName) {
            currentChannel = channelId;
            
            // Update active channel
            document.querySelectorAll('#channels-list .item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.item').classList.add('active');
            
            document.getElementById('current-title').textContent = `# ${channelName}`;
            
            await loadMessages(channelId);
        }

        async function loadMessages(channelId) {
            try {
                document.getElementById('main-content').innerHTML = '<div class="welcome"><h2>Loading messages...</h2></div>';
                
                const response = await fetch(`/api/messages/${channelId}`);
                const data = await response.json();
                
                if (data.success) {
                    displayMessages(data.messages);
                } else {
                    showStatus(`Failed to load messages: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus(`Error loading messages: ${error.message}`, 'error');
            }
        }

        function displayMessages(messages) {
            const container = document.getElementById('main-content');
            
            if (messages.length === 0) {
                container.innerHTML = '<div class="welcome"><h2>No messages found</h2><p>This channel appears to be empty or inaccessible</p></div>';
                return;
            }
            
            container.innerHTML = '';
            messages.forEach(message => {
                const div = document.createElement('div');
                div.className = 'message';
                
                const time = new Date(message.timestamp).toLocaleString();
                
                div.innerHTML = `
                    <div class="message-header">
                        <span class="message-author">${message.author_name}</span>
                        <span class="message-time">${time}</span>
                    </div>
                    <div class="message-content">${message.content || '<em>No text content</em>'}</div>
                `;
                
                container.appendChild(div);
            });
        }

        async function scanCurrentGuild() {
            if (!currentGuild) return;
            
            const btn = document.getElementById('scan-btn');
            const originalText = btn.textContent;
            btn.textContent = '🔄 Scanning...';
            btn.disabled = true;
            
            try {
                const response = await fetch(`/api/scan/${currentGuild}`, { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    showStatus(data.message, 'success');
                    await loadChannels(currentGuild);
                } else {
                    showStatus(`Scan failed: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus(`Scan error: ${error.message}`, 'error');
            } finally {
                btn.textContent = originalText;
                btn.disabled = false;
            }
        }
    </script>
</body>
</html>
