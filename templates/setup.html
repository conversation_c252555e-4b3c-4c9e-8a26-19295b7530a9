{% extends "base.html" %}

{% block title %}Discord Logger - Setup{% endblock %}

{% block content %}
<div class="setup-container">
    <div class="setup-card">
        <h1>Discord Logger Setup</h1>
        <p>Введите ваш Discord токен для начала логирования сообщений.</p>
        
        <div class="auth-tabs">
            <button class="tab-button active" onclick="switchTab('token')">Токен</button>
            <button class="tab-button" onclick="switchTab('email')">Email + Пароль</button>
        </div>

        <div class="setup-form">
            <!-- Token authentication -->
            <div id="token-auth" class="auth-method active">
                <div class="form-group">
                    <label for="discord-token">Discord Token:</label>
                    <input type="password" id="discord-token" placeholder="Введите ваш Discord токен">
                    <small>Токен будет использован только для чтения сообщений</small>
                </div>
            </div>

            <!-- Email + Password authentication -->
            <div id="email-auth" class="auth-method">
                <div class="form-group">
                    <label for="discord-email">Email:</label>
                    <input type="email" id="discord-email" placeholder="Введите ваш Discord email">
                </div>
                <div class="form-group">
                    <label for="discord-password">Пароль:</label>
                    <input type="password" id="discord-password" placeholder="Введите ваш Discord пароль">
                </div>
                <small>Данные будут использованы только для авторизации в Discord</small>
            </div>

            <button id="start-logger" class="btn-primary">Запустить логгер</button>
            <button id="test-token" class="btn-secondary">Тестировать токен</button>
        </div>

        <div id="test-result" class="test-result" style="display: none;">
            <h4>Результат теста:</h4>
            <pre id="test-output"></pre>
        </div>
        
        <div class="help-section">
            <h3>Как получить Discord токен:</h3>
            <ol>
                <li>Откройте Discord в браузере</li>
                <li>Нажмите F12 для открытия Developer Tools</li>
                <li>Перейдите на вкладку Network</li>
                <li>Обновите страницу (F5)</li>
                <li>Найдите запрос к API Discord</li>
                <li>В заголовках найдите Authorization</li>
                <li>Скопируйте значение после "Bearer "</li>
            </ol>
        </div>
    </div>
</div>

<style>
.setup-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: #36393f;
    color: #dcddde;
}

.setup-card {
    background: #2f3136;
    padding: 2rem;
    border-radius: 8px;
    max-width: 500px;
    width: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.setup-card h1 {
    text-align: center;
    margin-bottom: 1rem;
    color: #ffffff;
}

.auth-tabs {
    display: flex;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #40444b;
}

.tab-button {
    flex: 1;
    padding: 0.75rem;
    background: transparent;
    border: none;
    color: #b9bbbe;
    cursor: pointer;
    transition: all 0.2s;
    border-bottom: 2px solid transparent;
}

.tab-button.active {
    color: #5865f2;
    border-bottom-color: #5865f2;
}

.tab-button:hover {
    color: #dcddde;
}

.auth-method {
    display: none;
}

.auth-method.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #40444b;
    border-radius: 4px;
    background: #40444b;
    color: #dcddde;
    font-size: 1rem;
}

.form-group small {
    display: block;
    margin-top: 0.25rem;
    color: #b9bbbe;
    font-size: 0.875rem;
}

.btn-primary {
    width: 100%;
    padding: 0.75rem;
    background: #5865f2;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background: #4752c4;
}

.btn-secondary {
    width: 100%;
    padding: 0.75rem;
    background: #4f545c;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-top: 0.5rem;
}

.btn-secondary:hover {
    background: #5865f2;
}

.test-result {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #40444b;
    border-radius: 4px;
}

.test-result h4 {
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.test-result pre {
    background: #2f3136;
    padding: 0.75rem;
    border-radius: 4px;
    color: #dcddde;
    font-size: 0.875rem;
    overflow-x: auto;
}

.help-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #40444b;
}

.help-section h3 {
    margin-bottom: 1rem;
    color: #ffffff;
}

.help-section ol {
    padding-left: 1.5rem;
}

.help-section li {
    margin-bottom: 0.5rem;
    color: #b9bbbe;
}
</style>

<script>
function switchTab(tabName) {
    // Remove active class from all tabs and methods
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.auth-method').forEach(method => method.classList.remove('active'));

    // Add active class to selected tab and method
    event.target.classList.add('active');
    document.getElementById(tabName + '-auth').classList.add('active');
}

document.getElementById('start-logger').addEventListener('click', async function() {
    const button = this;
    let authData = {};

    // Determine which authentication method is active
    const activeMethod = document.querySelector('.auth-method.active').id;

    if (activeMethod === 'token-auth') {
        const token = document.getElementById('discord-token').value.trim();
        if (!token) {
            alert('Пожалуйста, введите Discord токен');
            return;
        }
        authData = { token: token };
    } else if (activeMethod === 'email-auth') {
        const email = document.getElementById('discord-email').value.trim();
        const password = document.getElementById('discord-password').value.trim();

        if (!email || !password) {
            alert('Пожалуйста, введите email и пароль');
            return;
        }
        authData = { email: email, password: password };
    }

    // Disable button and show loading
    button.disabled = true;
    button.textContent = 'Подключение...';

    try {
        const response = await fetch('/api/setup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(authData)
        });

        const data = await response.json();

        if (data.success) {
            alert('Discord клиент успешно подключен!');
            window.location.href = '/';
        } else {
            let errorMsg = data.error || 'Неизвестная ошибка';
            if (errorMsg.includes('Improper token')) {
                errorMsg = 'Неправильный токен Discord. Проверьте правильность токена.';
            } else if (errorMsg.includes('401') || errorMsg.includes('Unauthorized')) {
                errorMsg = 'Неправильный email или пароль. Проверьте данные для входа.';
            } else if (errorMsg.includes('Captcha')) {
                errorMsg = 'Discord требует прохождение капчи. Попробуйте позже или используйте токен.';
            }
            alert('Ошибка: ' + errorMsg);
            button.disabled = false;
            button.textContent = 'Запустить логгер';
        }
    } catch (error) {
        alert('Ошибка подключения: ' + error.message);
        button.disabled = false;
        button.textContent = 'Запустить логгер';
    }
});

// Test token button
document.getElementById('test-token').addEventListener('click', async function() {
    const button = this;
    const activeMethod = document.querySelector('.auth-method.active').id;
    const testResult = document.getElementById('test-result');
    const testOutput = document.getElementById('test-output');

    let token = '';

    if (activeMethod === 'token-auth') {
        token = document.getElementById('discord-token').value.trim();
        if (!token) {
            alert('Пожалуйста, введите Discord токен');
            return;
        }
    } else {
        alert('Тестирование токена доступно только для метода "Токен"');
        return;
    }

    // Disable button and show loading
    button.disabled = true;
    button.textContent = 'Тестирование...';

    try {
        const response = await fetch('/api/test-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ token: token })
        });

        const data = await response.json();

        // Show result
        testResult.style.display = 'block';
        testOutput.textContent = JSON.stringify(data, null, 2);

        if (data.success) {
            testOutput.style.color = '#3ba55d';
        } else {
            testOutput.style.color = '#ed4245';
        }

    } catch (error) {
        testResult.style.display = 'block';
        testOutput.textContent = 'Ошибка тестирования: ' + error.message;
        testOutput.style.color = '#ed4245';
    } finally {
        button.disabled = false;
        button.textContent = 'Тестировать токен';
    }
});
</script>
{% endblock %}
