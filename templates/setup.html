{% extends "base.html" %}

{% block title %}Discord Logger - Setup{% endblock %}

{% block content %}
<div class="setup-container">
    <div class="setup-card">
        <h1>Discord Logger Setup</h1>
        <p>Введите ваш Discord токен для начала логирования сообщений.</p>
        
        <div class="setup-form">
            <div class="form-group">
                <label for="discord-token">Discord Token:</label>
                <input type="password" id="discord-token" placeholder="Введите ваш Discord токен">
                <small>Токен будет использован только для чтения сообщений</small>
            </div>
            
            <button id="start-logger" class="btn-primary">Запустить логгер</button>
        </div>
        
        <div class="help-section">
            <h3>Как получить Discord токен:</h3>
            <ol>
                <li>Откройте Discord в браузере</li>
                <li>Нажмите F12 для открытия Developer Tools</li>
                <li>Перейдите на вкладку Network</li>
                <li>Обновите страницу (F5)</li>
                <li>Найдите запрос к API Discord</li>
                <li>В заголовках найдите Authorization</li>
                <li>Скопируйте значение после "Bearer "</li>
            </ol>
        </div>
    </div>
</div>

<style>
.setup-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: #36393f;
    color: #dcddde;
}

.setup-card {
    background: #2f3136;
    padding: 2rem;
    border-radius: 8px;
    max-width: 500px;
    width: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.setup-card h1 {
    text-align: center;
    margin-bottom: 1rem;
    color: #ffffff;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #40444b;
    border-radius: 4px;
    background: #40444b;
    color: #dcddde;
    font-size: 1rem;
}

.form-group small {
    display: block;
    margin-top: 0.25rem;
    color: #b9bbbe;
    font-size: 0.875rem;
}

.btn-primary {
    width: 100%;
    padding: 0.75rem;
    background: #5865f2;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background: #4752c4;
}

.help-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #40444b;
}

.help-section h3 {
    margin-bottom: 1rem;
    color: #ffffff;
}

.help-section ol {
    padding-left: 1.5rem;
}

.help-section li {
    margin-bottom: 0.5rem;
    color: #b9bbbe;
}
</style>

<script>
document.getElementById('start-logger').addEventListener('click', async function() {
    const token = document.getElementById('discord-token').value.trim();
    const button = this;

    if (!token) {
        alert('Пожалуйста, введите Discord токен');
        return;
    }

    // Disable button and show loading
    button.disabled = true;
    button.textContent = 'Подключение...';

    try {
        const response = await fetch('/api/setup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ token: token })
        });

        const data = await response.json();

        if (data.success) {
            alert('Discord клиент успешно подключен!');
            window.location.href = '/';
        } else {
            alert('Ошибка: ' + data.error);
            button.disabled = false;
            button.textContent = 'Запустить логгер';
        }
    } catch (error) {
        alert('Ошибка подключения: ' + error.message);
        button.disabled = false;
        button.textContent = 'Запустить логгер';
    }
});
</script>
{% endblock %}
