import requests
import logging
import time
from typing import Dict, List, Optional
from database import DatabaseManager

class DiscordAPI:
    def __init__(self, token: str, db_manager: DatabaseManager):
        self.token = token.strip()
        self.db = db_manager
        self.logger = logging.getLogger(__name__)
        self.base_url = "https://discord.com/api/v9"
        self.headers = {
            "Authorization": self.token,
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        self.user_info = None
        
    def test_connection(self) -> Dict:
        """Test if the token works"""
        try:
            response = requests.get(f"{self.base_url}/users/@me", headers=self.headers)
            if response.status_code == 200:
                self.user_info = response.json()
                return {"success": True, "user": self.user_info}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_guilds(self) -> List[Dict]:
        """Get user's guilds"""
        try:
            response = requests.get(f"{self.base_url}/users/@me/guilds", headers=self.headers)
            if response.status_code == 200:
                guilds = response.json()
                
                # Save to database
                for guild in guilds:
                    guild_data = {
                        'id': int(guild['id']),
                        'name': guild['name'],
                        'icon': guild.get('icon'),
                        'owner_id': int(guild.get('owner_id', 0)) if guild.get('owner_id') else None
                    }
                    self.db.save_guild(guild_data)
                
                self.logger.info(f"Loaded {len(guilds)} guilds")
                return guilds
            else:
                self.logger.error(f"Failed to get guilds: {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"Error getting guilds: {e}")
            return []
    
    def get_guild_channels(self, guild_id: int) -> List[Dict]:
        """Get channels for a specific guild"""
        try:
            response = requests.get(f"{self.base_url}/guilds/{guild_id}/channels", headers=self.headers)
            if response.status_code == 200:
                channels = response.json()
                
                # Save to database
                for channel in channels:
                    channel_data = {
                        'id': int(channel['id']),
                        'guild_id': guild_id,
                        'name': channel['name'],
                        'type': channel['type'],
                        'topic': channel.get('topic'),
                        'position': channel.get('position', 0),
                        'is_accessible': True  # If we can see it, we can access it
                    }
                    self.db.save_channel(channel_data)
                
                self.logger.info(f"Loaded {len(channels)} channels for guild {guild_id}")
                return channels
            else:
                self.logger.error(f"Failed to get channels for guild {guild_id}: {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"Error getting channels for guild {guild_id}: {e}")
            return []
    
    def get_dm_channels(self) -> List[Dict]:
        """Get DM channels"""
        try:
            response = requests.get(f"{self.base_url}/users/@me/channels", headers=self.headers)
            if response.status_code == 200:
                dm_channels = response.json()
                
                # Save to database
                for dm in dm_channels:
                    if dm['type'] == 1:  # DM channel
                        recipients = dm.get('recipients', [])
                        recipient_name = recipients[0]['username'] if recipients else 'Unknown'
                        recipient_id = int(recipients[0]['id']) if recipients else None
                        
                        dm_data = {
                            'id': int(dm['id']),
                            'recipient_id': recipient_id,
                            'recipient_name': recipient_name
                        }
                        self.db.save_dm_channel(dm_data)
                
                self.logger.info(f"Loaded {len(dm_channels)} DM channels")
                return dm_channels
            else:
                self.logger.error(f"Failed to get DM channels: {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"Error getting DM channels: {e}")
            return []
    
    def get_channel_messages(self, channel_id: int, limit: int = 50) -> List[Dict]:
        """Get messages from a channel"""
        try:
            params = {"limit": limit}
            response = requests.get(f"{self.base_url}/channels/{channel_id}/messages", 
                                  headers=self.headers, params=params)
            
            if response.status_code == 200:
                messages = response.json()
                
                # Save to database
                for message in messages:
                    message_data = {
                        'id': int(message['id']),
                        'channel_id': channel_id,
                        'author_id': int(message['author']['id']),
                        'author_name': message['author']['username'],
                        'content': message['content'],
                        'embeds': message.get('embeds', []),
                        'attachments': [
                            {
                                'id': int(att['id']),
                                'filename': att['filename'],
                                'url': att['url'],
                                'size': att['size']
                            } for att in message.get('attachments', [])
                        ],
                        'created_at': message['timestamp'],
                        'edited_at': message.get('edited_timestamp')
                    }
                    self.db.save_message(message_data)
                
                self.logger.info(f"Loaded {len(messages)} messages from channel {channel_id}")
                return messages
            else:
                self.logger.error(f"Failed to get messages from channel {channel_id}: {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"Error getting messages from channel {channel_id}: {e}")
            return []
    
    def refresh_all_data(self, load_messages: bool = True) -> bool:
        """Refresh all data from Discord"""
        try:
            self.logger.info("Starting full data refresh...")

            # Test connection first
            test_result = self.test_connection()
            if not test_result["success"]:
                self.logger.error(f"Connection test failed: {test_result['error']}")
                return False

            # Get guilds
            guilds = self.get_guilds()

            # Get channels for each guild
            total_channels = 0
            for guild in guilds:
                guild_id = int(guild['id'])
                channels = self.get_guild_channels(guild_id)
                total_channels += len(channels)

                # Only load messages if requested and for performance
                if load_messages:
                    from config import Config
                    message_limit = getattr(Config, 'INITIAL_MESSAGE_LIMIT', 5)
                    channels_processed = 0
                    max_channels = getattr(Config, 'MAX_CHANNELS_PER_GUILD', 50)

                    for channel in channels:
                        if channel['type'] == 0 and channels_processed < max_channels:  # Text channel
                            channel_id = int(channel['id'])
                            self.get_channel_messages(channel_id, message_limit)
                            channels_processed += 1
                            time.sleep(0.05)  # Faster rate limiting

            # Get DM channels
            dm_channels = self.get_dm_channels()

            # Get messages from DM channels (only if requested)
            if load_messages:
                for dm in dm_channels[:5]:  # Limit to first 5 DMs
                    if dm['type'] == 1:  # DM channel
                        dm_id = int(dm['id'])
                        self.get_channel_messages(dm_id, 5)
                        time.sleep(0.05)  # Faster rate limiting

            self.logger.info(f"Data refresh completed: {len(guilds)} guilds, {total_channels} channels, {len(dm_channels)} DMs")
            return True

        except Exception as e:
            self.logger.error(f"Error during data refresh: {e}")
            return False

    def quick_connect(self) -> bool:
        """Quick connection without loading messages"""
        try:
            self.logger.info("Starting quick connection...")

            # Test connection first
            test_result = self.test_connection()
            if not test_result["success"]:
                self.logger.error(f"Connection test failed: {test_result['error']}")
                return False

            # Get guilds only
            guilds = self.get_guilds()

            # Get channels for each guild (without messages)
            total_channels = 0
            for guild in guilds:
                guild_id = int(guild['id'])
                channels = self.get_guild_channels(guild_id)
                total_channels += len(channels)

            # Get DM channels (without messages)
            dm_channels = self.get_dm_channels()

            self.logger.info(f"Quick connection completed: {len(guilds)} guilds, {total_channels} channels, {len(dm_channels)} DMs")
            return True

        except Exception as e:
            self.logger.error(f"Error during quick connection: {e}")
            return False
    
    def is_ready(self) -> bool:
        """Check if API is ready"""
        return self.user_info is not None
    
    def get_user_info(self) -> Optional[Dict]:
        """Get current user info"""
        return self.user_info
