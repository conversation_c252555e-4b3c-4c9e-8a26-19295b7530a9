"""
Enterprise Channel Scanner
Intelligent, multi-threaded channel scanning with adaptive strategies
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Set, Optional
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor
from enterprise_config import config, ScanStrategy
from enterprise_logger import get_logger
from enterprise_database import db
from enterprise_discord_client import discord_client

class ScanStatus(Enum):
    PENDING = "pending"
    SCANNING = "scanning"
    SUCCESS = "success"
    FAILED = "failed"
    RATE_LIMITED = "rate_limited"
    NO_ACCESS = "no_access"

@dataclass
class ScanTask:
    """Individual scan task"""
    guild_id: str
    channel_id: str
    channel_name: str
    priority: int = 1
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class ScanResult:
    """Scan operation result"""
    task: ScanTask
    status: ScanStatus
    messages_found: int = 0
    duration: float = 0.0
    error_message: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class ChannelPriorityCalculator:
    """Calculate channel scan priority based on various factors"""
    
    def __init__(self):
        self.logger = get_logger('scanner.priority')
    
    def calculate_priority(self, channel: Dict[str, Any], guild_id: str) -> int:
        """Calculate channel priority (higher = more important)"""
        priority = 1
        
        # Channel type priority
        if channel['type'] == 0:  # Text channel
            priority += 10
        elif channel['type'] == 5:  # Announcement channel
            priority += 8
        elif channel['type'] == 11:  # Thread
            priority += 5
        
        # Channel name patterns (higher priority for interesting channels)
        name = channel['name'].lower()
        high_priority_patterns = [
            'general', 'chat', 'main', 'discussion', 'talk',
            'admin', 'mod', 'staff', 'private', 'secret',
            'announcement', 'news', 'update', 'important'
        ]
        
        for pattern in high_priority_patterns:
            if pattern in name:
                priority += 15
                break
        
        # Position priority (channels at top are usually more important)
        position = channel.get('position', 999)
        if position < 5:
            priority += 10 - position
        
        # Check if channel was previously accessible
        with db.transaction() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT was_temporarily_accessible, message_count, last_accessible_at
                FROM channels WHERE id = ?
            ''', (channel['id'],))
            
            result = cursor.fetchone()
            if result:
                was_accessible, message_count, last_accessible = result
                
                if was_accessible:
                    priority += 20  # High priority for previously accessible channels
                
                if message_count and message_count > 0:
                    priority += min(message_count // 10, 50)  # More messages = higher priority
                
                # Recent accessibility bonus
                if last_accessible:
                    last_time = datetime.fromisoformat(last_accessible)
                    hours_ago = (datetime.now() - last_time).total_seconds() / 3600
                    if hours_ago < 24:
                        priority += 30  # Very high priority for recently accessible
                    elif hours_ago < 168:  # 1 week
                        priority += 15
        
        return priority

class AdaptiveScanner:
    """Adaptive channel scanner with multiple strategies"""
    
    def __init__(self):
        self.logger = get_logger('scanner.adaptive')
        self.priority_calculator = ChannelPriorityCalculator()
        self.scan_queue: asyncio.Queue = asyncio.Queue()
        self.active_scans: Set[str] = set()
        self.scan_history: List[ScanResult] = []
        self.is_running = False
        
    async def start(self):
        """Start the scanner"""
        if self.is_running:
            return
        
        self.is_running = True
        self.logger.info("Starting adaptive scanner")
        
        # Start worker tasks
        workers = []
        for i in range(config.scanner.concurrent_channels):
            worker = asyncio.create_task(self._scan_worker(f"worker-{i}"))
            workers.append(worker)
        
        # Start periodic scanner
        periodic_task = asyncio.create_task(self._periodic_scan())
        workers.append(periodic_task)
        
        try:
            await asyncio.gather(*workers)
        except Exception as e:
            self.logger.error(f"Scanner error: {e}")
        finally:
            self.is_running = False
    
    async def stop(self):
        """Stop the scanner"""
        self.is_running = False
        self.logger.info("Stopping adaptive scanner")
    
    async def scan_guild(self, guild_id: str, strategy: ScanStrategy = None) -> List[ScanResult]:
        """Scan all channels in a guild"""
        if strategy is None:
            strategy = config.scanner.strategy
        
        self.logger.log_scanner_event("guild_scan_started", guild_id=guild_id, strategy=strategy.value)
        
        # Get channels from Discord API
        channels = await discord_client.get_guild_channels(guild_id)
        if not channels:
            self.logger.warning(f"No channels found for guild {guild_id}")
            return []
        
        # Create scan tasks based on strategy
        tasks = []
        if strategy == ScanStrategy.AGGRESSIVE:
            tasks = self._create_aggressive_tasks(guild_id, channels)
        elif strategy == ScanStrategy.SMART:
            tasks = self._create_smart_tasks(guild_id, channels)
        elif strategy == ScanStrategy.CONSERVATIVE:
            tasks = self._create_conservative_tasks(guild_id, channels)
        
        # Add tasks to queue
        for task in tasks:
            await self.scan_queue.put(task)
        
        self.logger.info(f"Queued {len(tasks)} scan tasks for guild {guild_id}")
        return []
    
    def _create_aggressive_tasks(self, guild_id: str, channels: List[Dict[str, Any]]) -> List[ScanTask]:
        """Create tasks for aggressive scanning (scan everything)"""
        tasks = []
        for channel in channels:
            if channel['type'] in [0, 5, 11]:  # Text, announcement, thread channels
                priority = self.priority_calculator.calculate_priority(channel, guild_id)
                task = ScanTask(
                    guild_id=guild_id,
                    channel_id=channel['id'],
                    channel_name=channel['name'],
                    priority=priority
                )
                tasks.append(task)
        
        # Sort by priority (highest first)
        tasks.sort(key=lambda t: t.priority, reverse=True)
        return tasks
    
    def _create_smart_tasks(self, guild_id: str, channels: List[Dict[str, Any]]) -> List[ScanTask]:
        """Create tasks for smart scanning (prioritize based on patterns)"""
        tasks = []
        for channel in channels:
            if channel['type'] in [0, 5, 11]:
                priority = self.priority_calculator.calculate_priority(channel, guild_id)
                
                # Only scan high-priority channels in smart mode
                if priority >= 20:
                    task = ScanTask(
                        guild_id=guild_id,
                        channel_id=channel['id'],
                        channel_name=channel['name'],
                        priority=priority
                    )
                    tasks.append(task)
        
        tasks.sort(key=lambda t: t.priority, reverse=True)
        return tasks
    
    def _create_conservative_tasks(self, guild_id: str, channels: List[Dict[str, Any]]) -> List[ScanTask]:
        """Create tasks for conservative scanning (only previously accessible)"""
        tasks = []
        
        with db.transaction() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, name FROM channels 
                WHERE guild_id = ? AND was_temporarily_accessible = 1
                ORDER BY last_accessible_at DESC
            ''', (guild_id,))
            
            accessible_channels = cursor.fetchall()
        
        for row in accessible_channels:
            channel_id, channel_name = row
            # Find channel in current list
            channel = next((c for c in channels if c['id'] == channel_id), None)
            if channel:
                priority = self.priority_calculator.calculate_priority(channel, guild_id)
                task = ScanTask(
                    guild_id=guild_id,
                    channel_id=channel_id,
                    channel_name=channel_name,
                    priority=priority + 50  # Bonus for previously accessible
                )
                tasks.append(task)
        
        tasks.sort(key=lambda t: t.priority, reverse=True)
        return tasks
    
    async def _scan_worker(self, worker_id: str):
        """Worker task for processing scan queue"""
        self.logger.debug(f"Scan worker {worker_id} started")
        
        while self.is_running:
            try:
                # Get task from queue with timeout
                task = await asyncio.wait_for(self.scan_queue.get(), timeout=1.0)
                
                # Skip if already scanning this channel
                if task.channel_id in self.active_scans:
                    continue
                
                self.active_scans.add(task.channel_id)
                
                try:
                    result = await self._scan_channel(task)
                    self.scan_history.append(result)
                    
                    # Log scan result to database
                    with db.transaction() as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                            INSERT INTO scan_history 
                            (guild_id, channel_id, scan_type, status, messages_found, 
                             error_message, duration, timestamp)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            task.guild_id,
                            task.channel_id,
                            config.scanner.strategy.value,
                            result.status.value,
                            result.messages_found,
                            result.error_message,
                            result.duration,
                            result.timestamp.isoformat()
                        ))
                
                finally:
                    self.active_scans.discard(task.channel_id)
                    self.scan_queue.task_done()
            
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Scan worker {worker_id} error: {e}")
    
    async def _scan_channel(self, task: ScanTask) -> ScanResult:
        """Scan individual channel"""
        start_time = time.time()
        
        self.logger.log_scanner_event(
            "channel_scan_started",
            guild_id=task.guild_id,
            channel_id=task.channel_id,
            channel_name=task.channel_name,
            priority=task.priority
        )
        
        try:
            messages = await discord_client.get_channel_messages(
                task.channel_id, 
                config.scanner.message_batch_size
            )
            
            duration = time.time() - start_time
            
            if messages:
                result = ScanResult(
                    task=task,
                    status=ScanStatus.SUCCESS,
                    messages_found=len(messages),
                    duration=duration
                )
                
                self.logger.log_scanner_event(
                    "channel_scan_success",
                    guild_id=task.guild_id,
                    channel_id=task.channel_id,
                    messages_found=len(messages),
                    duration=duration
                )
            else:
                result = ScanResult(
                    task=task,
                    status=ScanStatus.NO_ACCESS,
                    duration=duration
                )
            
            return result
        
        except Exception as e:
            duration = time.time() - start_time
            result = ScanResult(
                task=task,
                status=ScanStatus.FAILED,
                duration=duration,
                error_message=str(e)
            )
            
            self.logger.log_scanner_event(
                "channel_scan_failed",
                guild_id=task.guild_id,
                channel_id=task.channel_id,
                error=str(e),
                duration=duration
            )
            
            return result
    
    async def _periodic_scan(self):
        """Periodic scanning of all guilds"""
        while self.is_running:
            try:
                await asyncio.sleep(config.scanner.scan_interval)
                
                if not self.is_running:
                    break
                
                self.logger.info("Starting periodic scan")
                
                # Get all guilds from database
                guilds = db.execute_query(
                    "SELECT id FROM guilds WHERE is_accessible = 1",
                    fetch='all'
                )
                
                for guild_row in guilds:
                    guild_id = guild_row['id']
                    await self.scan_guild(guild_id)
                
                self.logger.info(f"Periodic scan completed for {len(guilds)} guilds")
            
            except Exception as e:
                self.logger.error(f"Periodic scan error: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get scanner metrics"""
        total_scans = len(self.scan_history)
        successful_scans = len([r for r in self.scan_history if r.status == ScanStatus.SUCCESS])
        
        return {
            'total_scans': total_scans,
            'successful_scans': successful_scans,
            'success_rate': successful_scans / max(total_scans, 1),
            'active_scans': len(self.active_scans),
            'queue_size': self.scan_queue.qsize(),
            'is_running': self.is_running,
            'strategy': config.scanner.strategy.value,
            'concurrent_workers': config.scanner.concurrent_channels
        }

# Global scanner instance
scanner = AdaptiveScanner()
