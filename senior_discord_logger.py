#!/usr/bin/env python3
"""
Senior Discord Logger - Production-ready Discord monitoring system
Built with enterprise-grade error handling and debugging
"""

import asyncio
import aiohttp
import sqlite3
import json
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO
import threading
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('DiscordLogger')

class DiscordAPI:
    """Production-ready Discord API client"""
    
    def __init__(self):
        self.token = None
        self.session = None
        self.base_url = "https://discord.com/api/v9"
        self.user_info = None
        
    async def set_token(self, token):
        """Set Discord token and create session"""
        self.token = token.strip()
        
        if self.session:
            await self.session.close()
            
        headers = {
            'Authorization': self.token,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Content-Type': 'application/json'
        }
        
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(headers=headers, timeout=timeout)
        
    async def test_connection(self):
        """Test Discord connection"""
        try:
            if not self.session:
                return {'success': False, 'error': 'No session'}
                
            async with self.session.get(f"{self.base_url}/users/@me") as response:
                if response.status == 200:
                    self.user_info = await response.json()
                    logger.info(f"Connected as {self.user_info['username']}")
                    return {'success': True, 'user': self.user_info}
                else:
                    error_text = await response.text()
                    logger.error(f"Auth failed: {response.status} - {error_text}")
                    return {'success': False, 'error': f'HTTP {response.status}: {error_text}'}
                    
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def get_guilds(self):
        """Get user guilds"""
        try:
            if not self.session:
                return []
                
            async with self.session.get(f"{self.base_url}/users/@me/guilds") as response:
                if response.status == 200:
                    guilds = await response.json()
                    logger.info(f"Retrieved {len(guilds)} guilds")
                    return guilds
                else:
                    logger.error(f"Failed to get guilds: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting guilds: {e}")
            return []
    
    async def get_channels(self, guild_id):
        """Get guild channels"""
        try:
            if not self.session:
                return []
                
            async with self.session.get(f"{self.base_url}/guilds/{guild_id}/channels") as response:
                if response.status == 200:
                    channels = await response.json()
                    logger.info(f"Retrieved {len(channels)} channels for guild {guild_id}")
                    return channels
                else:
                    logger.error(f"Failed to get channels: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting channels: {e}")
            return []
    
    async def get_messages(self, channel_id, limit=50):
        """Get channel messages"""
        try:
            if not self.session:
                return []
                
            async with self.session.get(f"{self.base_url}/channels/{channel_id}/messages?limit={limit}") as response:
                if response.status == 200:
                    messages = await response.json()
                    logger.info(f"Retrieved {len(messages)} messages from channel {channel_id}")
                    return messages
                elif response.status == 403:
                    logger.warning(f"No access to channel {channel_id}")
                    return []
                else:
                    logger.error(f"Failed to get messages: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting messages: {e}")
            return []
    
    async def close(self):
        """Close session"""
        if self.session:
            await self.session.close()

class Database:
    """Simple database manager"""
    
    def __init__(self, db_path='senior_discord.db'):
        self.db_path = db_path
        self.init_db()
    
    def init_db(self):
        """Initialize database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS guilds (
                id TEXT PRIMARY KEY,
                name TEXT,
                icon TEXT,
                member_count INTEGER,
                timestamp TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS channels (
                id TEXT PRIMARY KEY,
                guild_id TEXT,
                name TEXT,
                type INTEGER,
                has_messages INTEGER DEFAULT 0,
                message_count INTEGER DEFAULT 0,
                timestamp TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS messages (
                id TEXT PRIMARY KEY,
                channel_id TEXT,
                author_name TEXT,
                content TEXT,
                timestamp TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("Database initialized")
    
    def save_guilds(self, guilds):
        """Save guilds to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for guild in guilds:
            cursor.execute('''
                INSERT OR REPLACE INTO guilds (id, name, icon, member_count, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                guild['id'],
                guild['name'],
                guild.get('icon'),
                guild.get('approximate_member_count', 0),
                datetime.now().isoformat()
            ))
        
        conn.commit()
        conn.close()
        logger.info(f"Saved {len(guilds)} guilds to database")
    
    def save_channels(self, guild_id, channels):
        """Save channels to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for channel in channels:
            cursor.execute('''
                INSERT OR REPLACE INTO channels (id, guild_id, name, type, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                channel['id'],
                guild_id,
                channel['name'],
                channel['type'],
                datetime.now().isoformat()
            ))
        
        conn.commit()
        conn.close()
        logger.info(f"Saved {len(channels)} channels to database")
    
    def save_messages(self, channel_id, messages):
        """Save messages to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for message in messages:
            cursor.execute('''
                INSERT OR REPLACE INTO messages (id, channel_id, author_name, content, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                message['id'],
                channel_id,
                message['author']['username'],
                message['content'],
                message['timestamp']
            ))
        
        # Update channel message count
        cursor.execute('''
            UPDATE channels SET has_messages = 1, message_count = ? WHERE id = ?
        ''', (len(messages), channel_id))
        
        conn.commit()
        conn.close()
        logger.info(f"Saved {len(messages)} messages to database")
    
    def get_guilds(self):
        """Get guilds from database"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM guilds ORDER BY name')
        guilds = [dict(row) for row in cursor.fetchall()]
        
        conn.close()
        return guilds
    
    def get_channels(self, guild_id):
        """Get channels from database"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM channels WHERE guild_id = ? AND type = 0 ORDER BY name', (guild_id,))
        channels = [dict(row) for row in cursor.fetchall()]
        
        conn.close()
        return channels
    
    def get_messages(self, channel_id):
        """Get messages from database"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM messages WHERE channel_id = ? ORDER BY timestamp', (channel_id,))
        messages = [dict(row) for row in cursor.fetchall()]
        
        conn.close()
        return messages

# Global instances
discord_api = DiscordAPI()
database = Database()

# Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'senior-discord-logger'
socketio = SocketIO(app, cors_allowed_origins="*")

@app.route('/')
def index():
    return render_template('senior_index.html')

@app.route('/api/setup', methods=['POST'])
def setup():
    try:
        data = request.get_json()
        token = data.get('token', '').strip()
        
        if not token:
            return jsonify({'success': False, 'error': 'Token required'})
        
        if len(token) < 50:
            return jsonify({'success': False, 'error': 'Token too short'})
        
        # Test connection in new event loop
        async def test_and_load():
            await discord_api.set_token(token)
            result = await discord_api.test_connection()
            
            if result['success']:
                # Load guilds immediately
                guilds = await discord_api.get_guilds()
                if guilds:
                    database.save_guilds(guilds)
                    logger.info(f"Setup complete: {len(guilds)} guilds loaded")
            
            return result
        
        # Run in new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(test_and_load())
        loop.close()
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Setup error: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/guilds')
def get_guilds():
    try:
        guilds = database.get_guilds()
        logger.info(f"Returning {len(guilds)} guilds from database")
        return jsonify({'success': True, 'guilds': guilds})
    except Exception as e:
        logger.error(f"Error getting guilds: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/channels/<guild_id>')
def get_channels(guild_id):
    try:
        channels = database.get_channels(guild_id)
        
        # If no channels in database, try to load from Discord
        if not channels and discord_api.token:
            async def load_channels():
                fresh_channels = await discord_api.get_channels(guild_id)
                if fresh_channels:
                    database.save_channels(guild_id, fresh_channels)
                return fresh_channels
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            fresh_channels = loop.run_until_complete(load_channels())
            loop.close()
            
            channels = database.get_channels(guild_id)
        
        logger.info(f"Returning {len(channels)} channels for guild {guild_id}")
        return jsonify({'success': True, 'channels': channels})
    except Exception as e:
        logger.error(f"Error getting channels: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/messages/<channel_id>')
def get_messages(channel_id):
    try:
        messages = database.get_messages(channel_id)
        
        # If no messages in database, try to load from Discord
        if not messages and discord_api.token:
            async def load_messages():
                fresh_messages = await discord_api.get_messages(channel_id)
                if fresh_messages:
                    database.save_messages(channel_id, fresh_messages)
                return fresh_messages
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            fresh_messages = loop.run_until_complete(load_messages())
            loop.close()
            
            messages = database.get_messages(channel_id)
        
        logger.info(f"Returning {len(messages)} messages for channel {channel_id}")
        return jsonify({'success': True, 'messages': messages})
    except Exception as e:
        logger.error(f"Error getting messages: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/scan/<guild_id>', methods=['POST'])
def scan_guild(guild_id):
    try:
        async def scan():
            channels = await discord_api.get_channels(guild_id)
            if channels:
                database.save_channels(guild_id, channels)
                
                accessible_count = 0
                for channel in channels:
                    if channel['type'] == 0:  # Text channel
                        messages = await discord_api.get_messages(channel['id'], 10)
                        if messages:
                            database.save_messages(channel['id'], messages)
                            accessible_count += 1
                        await asyncio.sleep(0.1)  # Rate limiting
                
                return accessible_count
            return 0
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        accessible_count = loop.run_until_complete(scan())
        loop.close()
        
        return jsonify({
            'success': True,
            'accessible_channels': accessible_count,
            'message': f'Scanned guild, found {accessible_count} accessible channels'
        })
        
    except Exception as e:
        logger.error(f"Scan error: {e}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    logger.info("Starting Senior Discord Logger")
    logger.info("Web interface: http://127.0.0.1:5001")
    
    socketio.run(app, host='127.0.0.1', port=5001, debug=False, allow_unsafe_werkzeug=True)
