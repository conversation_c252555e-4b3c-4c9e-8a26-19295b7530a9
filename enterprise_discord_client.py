"""
Enterprise Discord API Client
Professional-grade Discord API client with rate limiting, retries, and monitoring
"""

import asyncio
import aiohttp
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from enterprise_config import config
from enterprise_logger import get_logger
from enterprise_database import db

class RequestType(Enum):
    GET_USER = "get_user"
    GET_GUILDS = "get_guilds"
    GET_CHANNELS = "get_channels"
    GET_MESSAGES = "get_messages"

@dataclass
class RateLimitInfo:
    """Rate limit information"""
    limit: int
    remaining: int
    reset_after: float
    bucket: str

@dataclass
class APIMetrics:
    """API call metrics"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    rate_limited_requests: int = 0
    average_response_time: float = 0.0
    last_request_time: Optional[datetime] = None

class RateLimiter:
    """Advanced rate limiter with bucket support"""
    
    def __init__(self):
        self.buckets: Dict[str, RateLimitInfo] = {}
        self.global_rate_limit = False
        self.global_reset_time = 0
        self.logger = get_logger('discord.ratelimiter')
    
    async def wait_if_needed(self, bucket: str = "default"):
        """Wait if rate limited"""
        current_time = time.time()
        
        # Check global rate limit
        if self.global_rate_limit and current_time < self.global_reset_time:
            wait_time = self.global_reset_time - current_time
            self.logger.warning(f"Global rate limit hit, waiting {wait_time:.2f}s")
            await asyncio.sleep(wait_time)
            self.global_rate_limit = False
        
        # Check bucket rate limit
        if bucket in self.buckets:
            rate_limit = self.buckets[bucket]
            if rate_limit.remaining <= 0 and current_time < rate_limit.reset_after:
                wait_time = rate_limit.reset_after - current_time
                self.logger.warning(f"Bucket {bucket} rate limited, waiting {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
    
    def update_rate_limit(self, headers: Dict[str, str], bucket: str = "default"):
        """Update rate limit info from response headers"""
        if 'x-ratelimit-limit' in headers:
            self.buckets[bucket] = RateLimitInfo(
                limit=int(headers.get('x-ratelimit-limit', 0)),
                remaining=int(headers.get('x-ratelimit-remaining', 0)),
                reset_after=time.time() + float(headers.get('x-ratelimit-reset-after', 0)),
                bucket=bucket
            )
        
        # Check for global rate limit
        if headers.get('x-ratelimit-global') == 'true':
            self.global_rate_limit = True
            self.global_reset_time = time.time() + float(headers.get('retry-after', 1))

class EnterpriseDiscordClient:
    """Enterprise Discord API client"""
    
    def __init__(self):
        self.logger = get_logger('discord.client')
        self.metrics = APIMetrics()
        self.rate_limiter = RateLimiter()
        self.session: Optional[aiohttp.ClientSession] = None
        self.user_info: Optional[Dict[str, Any]] = None
    
    async def _ensure_session(self):
        """Ensure aiohttp session is created"""
        if self.session is None or self.session.closed:
            headers = {
                'Authorization': config.discord.token,
                'User-Agent': config.discord.user_agent,
                'Content-Type': 'application/json'
            }

            timeout = aiohttp.ClientTimeout(total=config.discord.timeout)
            connector = aiohttp.TCPConnector(limit=100, limit_per_host=50)

            self.session = aiohttp.ClientSession(
                headers=headers,
                timeout=timeout,
                connector=connector
            )
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        request_type: RequestType,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """Make HTTP request with rate limiting and retries"""
        await self._ensure_session()

        url = f"{config.discord.base_url}{endpoint}"
        bucket = f"{method}:{endpoint.split('?')[0]}"

        for attempt in range(config.discord.max_retries):
            try:
                # Wait for rate limit
                await self.rate_limiter.wait_if_needed(bucket)
                
                start_time = time.time()
                self.metrics.total_requests += 1
                
                async with self.session.request(method, url, **kwargs) as response:
                    duration = time.time() - start_time
                    
                    # Update rate limit info
                    self.rate_limiter.update_rate_limit(dict(response.headers), bucket)
                    
                    # Update metrics
                    self.metrics.average_response_time = (
                        (self.metrics.average_response_time * (self.metrics.total_requests - 1) + duration)
                        / self.metrics.total_requests
                    )
                    self.metrics.last_request_time = datetime.now()
                    
                    # Log API call
                    self.logger.log_api_call(
                        method=method,
                        url=url,
                        status_code=response.status,
                        duration=duration,
                        request_type=request_type.value,
                        attempt=attempt + 1
                    )
                    
                    if response.status == 200:
                        self.metrics.successful_requests += 1
                        data = await response.json()
                        return data
                    
                    elif response.status == 429:
                        # Rate limited
                        self.metrics.rate_limited_requests += 1
                        retry_after = float(response.headers.get('retry-after', 1))
                        self.logger.warning(f"Rate limited, retrying after {retry_after}s")
                        await asyncio.sleep(retry_after)
                        continue
                    
                    elif response.status in [401, 403]:
                        # Authentication/permission error
                        self.metrics.failed_requests += 1
                        error_text = await response.text()
                        self.logger.error(f"Auth error {response.status}: {error_text}")
                        return None
                    
                    elif response.status == 404:
                        # Not found
                        self.metrics.failed_requests += 1
                        self.logger.debug(f"Resource not found: {url}")
                        return None
                    
                    else:
                        # Other error
                        self.metrics.failed_requests += 1
                        error_text = await response.text()
                        self.logger.error(f"API error {response.status}: {error_text}")
                        
                        if attempt < config.discord.max_retries - 1:
                            await asyncio.sleep(2 ** attempt)  # Exponential backoff
                            continue
                        return None
            
            except asyncio.TimeoutError:
                self.logger.error(f"Request timeout for {url}")
                if attempt < config.discord.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                    continue
                self.metrics.failed_requests += 1
                return None
            
            except Exception as e:
                self.logger.error(f"Request error for {url}: {e}")
                if attempt < config.discord.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                    continue
                self.metrics.failed_requests += 1
                return None
        
        return None
    
    async def get_user_info(self) -> Optional[Dict[str, Any]]:
        """Get current user information"""
        if self.user_info:
            return self.user_info
        
        with self.logger.performance_timer("get_user_info"):
            data = await self._make_request('GET', '/users/@me', RequestType.GET_USER)
            if data:
                self.user_info = data
                self.logger.info(f"Authenticated as {data['username']}#{data['discriminator']}")
            return data
    
    async def get_guilds(self) -> List[Dict[str, Any]]:
        """Get user's guilds"""
        with self.logger.performance_timer("get_guilds"):
            data = await self._make_request('GET', '/users/@me/guilds', RequestType.GET_GUILDS)
            if data:
                self.logger.info(f"Retrieved {len(data)} guilds")
                
                # Save to database
                with db.transaction() as conn:
                    cursor = conn.cursor()
                    for guild in data:
                        cursor.execute('''
                            INSERT OR REPLACE INTO guilds 
                            (id, name, icon, owner_id, member_count, last_seen, is_accessible)
                            VALUES (?, ?, ?, ?, ?, ?, 1)
                        ''', (
                            guild['id'],
                            guild['name'],
                            guild.get('icon'),
                            guild.get('owner_id'),
                            guild.get('approximate_member_count'),
                            datetime.now().isoformat()
                        ))
                
                return data
            return []
    
    async def get_guild_channels(self, guild_id: str) -> List[Dict[str, Any]]:
        """Get channels for a guild"""
        with self.logger.performance_timer("get_guild_channels", guild_id=guild_id):
            data = await self._make_request(
                'GET', 
                f'/guilds/{guild_id}/channels', 
                RequestType.GET_CHANNELS
            )
            
            if data:
                self.logger.info(f"Retrieved {len(data)} channels for guild {guild_id}")
                
                # Save to database
                with db.transaction() as conn:
                    cursor = conn.cursor()
                    for channel in data:
                        cursor.execute('''
                            INSERT OR REPLACE INTO channels 
                            (id, guild_id, name, type, topic, position, parent_id, nsfw, 
                             last_seen, is_accessible, last_accessible_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?)
                        ''', (
                            channel['id'],
                            guild_id,
                            channel['name'],
                            channel['type'],
                            channel.get('topic'),
                            channel.get('position', 0),
                            channel.get('parent_id'),
                            channel.get('nsfw', False),
                            datetime.now().isoformat(),
                            datetime.now().isoformat()
                        ))
                
                return data
            return []
    
    async def get_channel_messages(self, channel_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get messages from a channel"""
        with self.logger.performance_timer("get_channel_messages", channel_id=channel_id, limit=limit):
            data = await self._make_request(
                'GET',
                f'/channels/{channel_id}/messages?limit={limit}',
                RequestType.GET_MESSAGES
            )
            
            if data:
                self.logger.info(f"Retrieved {len(data)} messages from channel {channel_id}")
                
                # Save to database
                with db.transaction() as conn:
                    cursor = conn.cursor()
                    
                    # Update channel accessibility
                    cursor.execute('''
                        UPDATE channels 
                        SET message_count = message_count + ?, 
                            last_message_id = ?,
                            was_temporarily_accessible = 1,
                            last_accessible_at = ?
                        WHERE id = ?
                    ''', (
                        len(data),
                        data[0]['id'] if data else None,
                        datetime.now().isoformat(),
                        channel_id
                    ))
                    
                    # Save messages
                    for message in data:
                        cursor.execute('''
                            INSERT OR REPLACE INTO messages 
                            (id, channel_id, guild_id, author_id, author_name, author_discriminator,
                             author_avatar, content, embeds, attachments, reactions, mentions,
                             timestamp, edited_timestamp, message_type)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            message['id'],
                            channel_id,
                            message.get('guild_id'),
                            message['author']['id'],
                            message['author']['username'],
                            message['author'].get('discriminator'),
                            message['author'].get('avatar'),
                            message['content'],
                            json.dumps(message.get('embeds', [])),
                            json.dumps(message.get('attachments', [])),
                            json.dumps(message.get('reactions', [])),
                            json.dumps(message.get('mentions', [])),
                            message['timestamp'],
                            message.get('edited_timestamp'),
                            message.get('type', 0)
                        ))
                
                return data
            return []
    
    async def close(self):
        """Close the client session"""
        if self.session:
            await self.session.close()
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get client metrics"""
        return {
            'total_requests': self.metrics.total_requests,
            'successful_requests': self.metrics.successful_requests,
            'failed_requests': self.metrics.failed_requests,
            'rate_limited_requests': self.metrics.rate_limited_requests,
            'success_rate': self.metrics.successful_requests / max(self.metrics.total_requests, 1),
            'average_response_time': self.metrics.average_response_time,
            'last_request_time': self.metrics.last_request_time.isoformat() if self.metrics.last_request_time else None,
            'authenticated_user': self.user_info['username'] if self.user_info else None
        }

# Global client instance
discord_client = EnterpriseDiscordClient()
