/* Discord-like styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #36393f;
    color: #dcddde;
    overflow: hidden;
}

.discord-layout {
    display: flex;
    height: 100vh;
}

/* Sidebar with servers */
.sidebar {
    width: 72px;
    background: #202225;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 0;
}

.server-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    align-items: center;
}

.server-item {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #36393f;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.server-item:hover {
    border-radius: 16px;
    background: #5865f2;
}

.server-item.active {
    border-radius: 16px;
    background: #5865f2;
}

.server-item.active::before {
    content: '';
    position: absolute;
    left: -16px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 40px;
    background: #ffffff;
    border-radius: 0 4px 4px 0;
}

.server-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #5865f2;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
    overflow: hidden;
}

.server-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.server-divider {
    width: 32px;
    height: 2px;
    background: #36393f;
    border-radius: 1px;
    margin: 4px 0;
}

/* Channel list */
.channel-list {
    width: 240px;
    background: #2f3136;
    display: flex;
    flex-direction: column;
}

.channel-header {
    padding: 16px;
    border-bottom: 1px solid #202225;
    background: #2f3136;
}

.channel-header h3 {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
}

.channel-container {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.channel-category {
    margin-bottom: 8px;
}

.category-header {
    padding: 8px 8px 4px 8px;
    color: #8e9297;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.channel-item {
    padding: 6px 8px;
    margin: 1px 0;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #8e9297;
    transition: all 0.15s ease;
}

.channel-item:hover {
    background: #34373c;
    color: #dcddde;
}

.channel-item.active {
    background: #393c43;
    color: #ffffff;
}

.channel-item.inaccessible {
    opacity: 0.5;
    font-style: italic;
}

.channel-icon {
    margin-right: 6px;
    font-size: 16px;
}

.channel-name {
    font-size: 14px;
    font-weight: 500;
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #36393f;
}

.chat-header {
    height: 48px;
    background: #36393f;
    border-bottom: 1px solid #202225;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.refresh-button {
    background: #4f545c;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    color: #dcddde;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.refresh-button:hover {
    background: #5865f2;
}

.refresh-button:disabled {
    background: #2f3136;
    color: #72767d;
    cursor: not-allowed;
}

.refresh-button.loading {
    animation: spin 1s linear infinite;
}

.chat-header h2 {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #faa61a;
}

.status-dot.connected {
    background: #3ba55d;
}

.status-dot.disconnected {
    background: #ed4245;
}

#status-text {
    font-size: 12px;
    color: #b9bbbe;
}

.message-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.welcome-message {
    text-align: center;
    margin-top: 100px;
}

.welcome-message h3 {
    color: #ffffff;
    margin-bottom: 8px;
}

.welcome-message p {
    color: #b9bbbe;
}

.message {
    margin-bottom: 16px;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.1s ease;
}

.message:hover {
    background: rgba(4, 4, 5, 0.07);
}

.message.deleted {
    opacity: 0.6;
    background: rgba(237, 66, 69, 0.1);
}

.message-header {
    display: flex;
    align-items: baseline;
    margin-bottom: 4px;
}

.message-author {
    font-weight: 600;
    color: #ffffff;
    margin-right: 8px;
}

.message-timestamp {
    font-size: 12px;
    color: #72767d;
}

.message-content {
    color: #dcddde;
    line-height: 1.375;
    word-wrap: break-word;
}

.message-edited {
    font-size: 10px;
    color: #72767d;
    margin-left: 4px;
}

.message-deleted-indicator {
    color: #ed4245;
    font-size: 12px;
    font-style: italic;
    margin-top: 4px;
}

.message-attachments {
    margin-top: 8px;
}

.attachment {
    display: inline-block;
    padding: 4px 8px;
    background: #2f3136;
    border-radius: 4px;
    margin-right: 8px;
    margin-bottom: 4px;
    color: #00aff4;
    text-decoration: none;
    font-size: 14px;
}

.attachment:hover {
    background: #36393f;
}

.message-input-container {
    padding: 16px;
    background: #36393f;
}

.message-info {
    text-align: center;
}

.message-info small {
    color: #72767d;
    font-size: 12px;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #36393f;
    border-top: 4px solid #5865f2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2e3338;
}

::-webkit-scrollbar-thumb {
    background: #202225;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1a1d21;
}

/* Responsive design */
@media (max-width: 768px) {
    .sidebar {
        width: 60px;
    }
    
    .channel-list {
        width: 200px;
    }
    
    .server-item {
        width: 40px;
        height: 40px;
    }
    
    .server-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }
}
