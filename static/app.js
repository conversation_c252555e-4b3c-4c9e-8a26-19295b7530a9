class DiscordLoggerApp {
    constructor() {
        this.socket = null;
        this.currentGuild = null;
        this.currentChannel = null;
        this.guilds = [];
        this.channels = [];
        this.messages = [];
        this.dmChannels = [];
        
        this.init();
    }
    
    init() {
        this.initSocket();
        this.loadData();
        this.setupEventListeners();
    }
    
    initSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.updateStatus('connected', 'Подключено');
        });
        
        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.updateStatus('disconnected', 'Отключено');
        });
        
        this.socket.on('new_message', (data) => {
            this.handleNewMessage(data);
        });
        
        this.socket.on('message_updated', (data) => {
            this.handleMessageUpdate(data);
        });
        
        this.socket.on('message_deleted', (data) => {
            this.handleMessageDelete(data);
        });
    }
    
    async loadData() {
        this.showLoading(true);

        try {
            // Check status first
            const statusResponse = await fetch('/api/status');
            const statusData = await statusResponse.json();

            if (!statusData.success || !statusData.connected) {
                this.updateStatus('disconnected', 'Discord не подключен');
                this.showLoading(false);
                return;
            }

            this.updateStatus('connected', `Подключен как ${statusData.user.name}`);

            // Try quick setup first
            try {
                const quickSetupResponse = await fetch('/api/quick-setup', { method: 'POST' });
                const quickSetupData = await quickSetupResponse.json();
                console.log('Quick setup result:', quickSetupData);
            } catch (error) {
                console.warn('Quick setup failed:', error);
            }

            // Load guilds
            await this.loadGuilds();

            // Load DM channels
            await this.loadDMChannels();

            // Update welcome message
            const messageContainer = document.getElementById('message-container');
            if (this.guilds.length === 0) {
                messageContainer.innerHTML = `
                    <div class="welcome-message">
                        <h3>Discord Logger готов!</h3>
                        <p>Нажмите кнопку обновления 🔄 для загрузки серверов</p>
                        <button onclick="window.discordLogger.refreshData()" class="btn-primary" style="margin-top: 10px;">Загрузить серверы</button>
                    </div>
                `;
            } else {
                messageContainer.innerHTML = `
                    <div class="welcome-message">
                        <h3>Discord Logger</h3>
                        <p>Выберите сервер слева для просмотра каналов</p>
                    </div>
                `;
            }

        } catch (error) {
            console.error('Error loading data:', error);
            this.updateStatus('disconnected', 'Ошибка загрузки');
        }

        this.showLoading(false);
    }
    
    async loadGuilds() {
        try {
            const response = await fetch('/api/guilds');
            const data = await response.json();
            
            if (data.success) {
                this.guilds = data.guilds;
                this.renderGuilds();
            }
        } catch (error) {
            console.error('Error loading guilds:', error);
        }
    }
    
    async loadDMChannels() {
        try {
            const response = await fetch('/api/dms');
            const data = await response.json();
            
            if (data.success) {
                this.dmChannels = data.dms;
            }
        } catch (error) {
            console.error('Error loading DM channels:', error);
        }
    }
    
    async loadChannels(guildId) {
        try {
            const response = await fetch(`/api/channels/${guildId}`);
            const data = await response.json();
            
            if (data.success) {
                this.channels = data.channels;
                this.renderChannels();
            }
        } catch (error) {
            console.error('Error loading channels:', error);
        }
    }
    
    async loadMessages(channelId, page = 0) {
        try {
            const response = await fetch(`/api/messages/${channelId}?page=${page}`);
            const data = await response.json();
            
            if (data.success) {
                if (page === 0) {
                    this.messages = data.messages;
                } else {
                    this.messages = [...this.messages, ...data.messages];
                }
                this.renderMessages();
            }
        } catch (error) {
            console.error('Error loading messages:', error);
        }
    }
    
    renderGuilds() {
        const guildList = document.getElementById('guild-list');
        guildList.innerHTML = '';
        
        this.guilds.forEach(guild => {
            const guildElement = document.createElement('div');
            guildElement.className = 'server-item';
            guildElement.dataset.guildId = guild.id;
            
            const iconElement = document.createElement('div');
            iconElement.className = 'server-icon';
            
            if (guild.icon) {
                const img = document.createElement('img');
                img.src = `https://cdn.discordapp.com/icons/${guild.id}/${guild.icon}.png`;
                img.alt = guild.name;
                iconElement.appendChild(img);
            } else {
                iconElement.textContent = guild.name.charAt(0).toUpperCase();
            }
            
            guildElement.appendChild(iconElement);
            guildElement.title = guild.name;
            
            guildElement.addEventListener('click', () => {
                this.selectGuild(guild.id, guild.name);
            });
            
            guildList.appendChild(guildElement);
        });
    }
    
    renderChannels() {
        const channelContainer = document.getElementById('channel-container');
        channelContainer.innerHTML = '';
        
        // Group channels by type
        const textChannels = this.channels.filter(ch => ch.type === 0);
        const voiceChannels = this.channels.filter(ch => ch.type === 2);
        const categories = this.channels.filter(ch => ch.type === 4);
        
        // Render text channels
        if (textChannels.length > 0) {
            this.renderChannelCategory('Текстовые каналы', textChannels, '#');
        }
        
        // Render voice channels
        if (voiceChannels.length > 0) {
            this.renderChannelCategory('Голосовые каналы', voiceChannels, '🔊');
        }
    }
    
    renderChannelCategory(title, channels, icon) {
        const channelContainer = document.getElementById('channel-container');
        
        const categoryDiv = document.createElement('div');
        categoryDiv.className = 'channel-category';
        
        const headerDiv = document.createElement('div');
        headerDiv.className = 'category-header';
        headerDiv.textContent = title;
        categoryDiv.appendChild(headerDiv);
        
        channels.forEach(channel => {
            const channelDiv = document.createElement('div');
            channelDiv.className = 'channel-item';
            channelDiv.dataset.channelId = channel.id;
            
            if (!channel.is_accessible) {
                channelDiv.classList.add('inaccessible');
            }
            
            const iconSpan = document.createElement('span');
            iconSpan.className = 'channel-icon';
            iconSpan.textContent = icon;
            
            const nameSpan = document.createElement('span');
            nameSpan.className = 'channel-name';
            nameSpan.textContent = channel.name;
            
            channelDiv.appendChild(iconSpan);
            channelDiv.appendChild(nameSpan);
            
            if (channel.is_accessible) {
                channelDiv.addEventListener('click', () => {
                    this.selectChannel(channel.id, channel.name);
                });
            }
            
            categoryDiv.appendChild(channelDiv);
        });
        
        channelContainer.appendChild(categoryDiv);
    }
    
    renderMessages() {
        const messageContainer = document.getElementById('message-container');
        messageContainer.innerHTML = '';
        
        if (this.messages.length === 0) {
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'welcome-message';
            emptyDiv.innerHTML = '<h3>Нет сообщений</h3><p>В этом канале пока нет сообщений</p>';
            messageContainer.appendChild(emptyDiv);
            return;
        }
        
        // Reverse messages to show newest at bottom
        const sortedMessages = [...this.messages].reverse();
        
        sortedMessages.forEach(message => {
            const messageDiv = this.createMessageElement(message);
            messageContainer.appendChild(messageDiv);
        });
        
        // Scroll to bottom
        messageContainer.scrollTop = messageContainer.scrollHeight;
    }
    
    createMessageElement(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message';
        messageDiv.dataset.messageId = message.id;
        
        if (message.is_deleted) {
            messageDiv.classList.add('deleted');
        }
        
        const headerDiv = document.createElement('div');
        headerDiv.className = 'message-header';
        
        const authorSpan = document.createElement('span');
        authorSpan.className = 'message-author';
        authorSpan.textContent = message.author_name;
        
        const timestampSpan = document.createElement('span');
        timestampSpan.className = 'message-timestamp';
        timestampSpan.textContent = this.formatTimestamp(message.created_at);
        
        headerDiv.appendChild(authorSpan);
        headerDiv.appendChild(timestampSpan);
        
        if (message.edited_at) {
            const editedSpan = document.createElement('span');
            editedSpan.className = 'message-edited';
            editedSpan.textContent = '(изменено)';
            headerDiv.appendChild(editedSpan);
        }
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = message.content || '';
        
        messageDiv.appendChild(headerDiv);
        messageDiv.appendChild(contentDiv);
        
        // Add deleted indicator
        if (message.is_deleted) {
            const deletedDiv = document.createElement('div');
            deletedDiv.className = 'message-deleted-indicator';
            deletedDiv.textContent = 'Сообщение удалено';
            messageDiv.appendChild(deletedDiv);
        }
        
        // Add attachments
        if (message.attachments && message.attachments.length > 0) {
            const attachmentsDiv = document.createElement('div');
            attachmentsDiv.className = 'message-attachments';
            
            message.attachments.forEach(attachment => {
                const attachmentLink = document.createElement('a');
                attachmentLink.className = 'attachment';
                attachmentLink.href = attachment.url;
                attachmentLink.target = '_blank';
                attachmentLink.textContent = attachment.filename;
                attachmentsDiv.appendChild(attachmentLink);
            });
            
            messageDiv.appendChild(attachmentsDiv);
        }
        
        return messageDiv;
    }
    
    selectGuild(guildId, guildName) {
        // Remove active class from all guild items
        document.querySelectorAll('.server-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to selected guild
        document.querySelector(`[data-guild-id="${guildId}"]`).classList.add('active');

        this.currentGuild = guildId;
        this.currentChannel = null;

        document.getElementById('channel-header-title').textContent = guildName;
        document.getElementById('chat-title').textContent = 'Загрузка каналов...';

        // Clear messages
        document.getElementById('message-container').innerHTML =
            '<div class="welcome-message"><h3>Загрузка каналов...</h3><p>Пожалуйста, подождите</p></div>';

        this.loadChannelsOnDemand(guildId);
    }

    async loadChannelsOnDemand(guildId) {
        try {
            // First try to load from database
            await this.loadChannels(guildId);

            // If no channels found, load from Discord API
            if (this.channels.length === 0) {
                console.log('No channels in database, loading from Discord API...');

                const response = await fetch(`/api/load-channels/${guildId}`, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    console.log(`Loaded ${data.count} channels from Discord API`);
                    // Reload from database
                    await this.loadChannels(guildId);
                } else {
                    console.warn('Failed to load channels from Discord API:', data.error);
                }
            }

            document.getElementById('chat-title').textContent = 'Выберите канал';
            document.getElementById('message-container').innerHTML =
                '<div class="welcome-message"><h3>Выберите канал</h3><p>Выберите канал для просмотра сообщений</p></div>';

        } catch (error) {
            console.error('Error loading channels on demand:', error);
            document.getElementById('chat-title').textContent = 'Ошибка загрузки';
            document.getElementById('message-container').innerHTML =
                '<div class="welcome-message"><h3>Ошибка</h3><p>Не удалось загрузить каналы</p></div>';
        }
    }
    
    selectChannel(channelId, channelName) {
        // Remove active class from all channel items
        document.querySelectorAll('.channel-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to selected channel
        document.querySelector(`[data-channel-id="${channelId}"]`).classList.add('active');

        this.currentChannel = channelId;

        document.getElementById('chat-title').textContent = `# ${channelName}`;

        // Join channel for real-time updates
        if (this.socket) {
            this.socket.emit('join_channel', { channel_id: channelId });
        }

        // Load messages with on-demand loading
        this.loadMessagesOnDemand(channelId);
    }

    async loadMessagesOnDemand(channelId) {
        try {
            // Show loading in chat
            const messageContainer = document.getElementById('message-container');
            messageContainer.innerHTML = '<div class="welcome-message"><h3>Загрузка сообщений...</h3><p>Проверка базы данных...</p></div>';

            // First try to load from database
            await this.loadMessages(channelId);

            // If no messages found, try to load from Discord API
            if (this.messages.length === 0) {
                console.log('No messages in database, loading from Discord API...');
                messageContainer.innerHTML = '<div class="welcome-message"><h3>Загрузка сообщений...</h3><p>Загрузка из Discord API...</p></div>';

                // Try direct API call first
                const directResponse = await fetch(`/api/messages/${channelId}`);
                const directData = await directResponse.json();

                if (directData.success && directData.messages.length > 0) {
                    console.log(`Loaded ${directData.messages.length} messages directly`);
                    this.messages = directData.messages;
                    this.renderMessages();
                } else {
                    // Fallback to load-messages endpoint
                    const response = await fetch(`/api/load-messages/${channelId}`, { method: 'POST' });
                    const data = await response.json();

                    if (data.success) {
                        console.log(`Loaded ${data.count} messages from Discord API`);
                        // Reload from database
                        await this.loadMessages(channelId);
                    } else {
                        console.warn('Failed to load messages from Discord API:', data.error);
                        messageContainer.innerHTML = `
                            <div class="welcome-message">
                                <h3>Нет сообщений</h3>
                                <p>В этом канале нет доступных сообщений</p>
                                <small>Возможно, у вас нет доступа к этому каналу</small>
                            </div>
                        `;
                    }
                }
            } else {
                console.log(`Found ${this.messages.length} messages in database`);
            }

        } catch (error) {
            console.error('Error loading messages on demand:', error);
            const messageContainer = document.getElementById('message-container');
            messageContainer.innerHTML = '<div class="welcome-message"><h3>Ошибка загрузки</h3><p>Не удалось загрузить сообщения</p></div>';
        }
    }
    
    selectDMs() {
        // Remove active class from all server items
        document.querySelectorAll('.server-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to DM button
        document.getElementById('dm-button').classList.add('active');
        
        this.currentGuild = null;
        this.currentChannel = null;
        
        document.getElementById('channel-header-title').textContent = 'Личные сообщения';
        document.getElementById('chat-title').textContent = 'Личные сообщения';
        
        // Render DM channels
        this.renderDMChannels();
    }
    
    renderDMChannels() {
        const channelContainer = document.getElementById('channel-container');
        channelContainer.innerHTML = '';
        
        if (this.dmChannels.length === 0) {
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'welcome-message';
            emptyDiv.innerHTML = '<p>Нет личных сообщений</p>';
            channelContainer.appendChild(emptyDiv);
            return;
        }
        
        this.dmChannels.forEach(dm => {
            const channelDiv = document.createElement('div');
            channelDiv.className = 'channel-item';
            channelDiv.dataset.channelId = dm.id;
            
            const iconSpan = document.createElement('span');
            iconSpan.className = 'channel-icon';
            iconSpan.textContent = '@';
            
            const nameSpan = document.createElement('span');
            nameSpan.className = 'channel-name';
            nameSpan.textContent = dm.recipient_name || 'Unknown User';
            
            channelDiv.appendChild(iconSpan);
            channelDiv.appendChild(nameSpan);
            
            channelDiv.addEventListener('click', () => {
                this.selectChannel(dm.id, dm.recipient_name || 'Unknown User');
            });
            
            channelContainer.appendChild(channelDiv);
        });
    }
    
    setupEventListeners() {
        // DM button
        document.getElementById('dm-button').addEventListener('click', () => {
            this.selectDMs();
        });

        // Refresh button
        document.getElementById('refresh-btn').addEventListener('click', async () => {
            await this.refreshData();
        });
    }

    async refreshData() {
        const refreshBtn = document.getElementById('refresh-btn');

        try {
            // Disable button and show loading
            refreshBtn.disabled = true;
            refreshBtn.classList.add('loading');

            // First check debug info
            console.log('Checking Discord client status...');
            const debugResponse = await fetch('/api/debug');
            const debugData = await debugResponse.json();
            console.log('Debug info:', debugData);

            if (!debugData.client_exists) {
                alert('Discord клиент не подключен. Пожалуйста, настройте подключение.');
                return;
            }

            if (!debugData.ready) {
                alert('Discord клиент не готов. Подождите несколько секунд и попробуйте снова.');
                return;
            }

            if (debugData.guilds === 0) {
                alert('Нет доступных серверов. Убедитесь, что ваш аккаунт состоит в серверах Discord.');
            }

            // Force full refresh data
            console.log('Requesting full data refresh...');
            const response = await fetch('/api/refresh', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                console.log('Full refresh successful, reloading data...');

                // Reload all data
                await this.loadGuilds();
                await this.loadDMChannels();

                // Show success message briefly
                const originalText = refreshBtn.textContent;
                refreshBtn.textContent = '✓';
                setTimeout(() => {
                    refreshBtn.textContent = originalText;
                }, 1000);

                console.log('Data reload completed');
            } else {
                console.error('Refresh failed:', data.error);
                alert('Ошибка обновления: ' + data.error);
            }

        } catch (error) {
            console.error('Error refreshing data:', error);
            alert('Ошибка обновления данных: ' + error.message);
        } finally {
            // Re-enable button
            refreshBtn.disabled = false;
            refreshBtn.classList.remove('loading');
        }
    }
    
    updateStatus(status, text) {
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.getElementById('status-text');
        
        statusDot.className = `status-dot ${status}`;
        statusText.textContent = text;
    }
    
    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.remove('hidden');
        } else {
            overlay.classList.add('hidden');
        }
    }
    
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('ru-RU', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    handleNewMessage(data) {
        if (data.channel_id === this.currentChannel) {
            this.messages.unshift(data);
            this.renderMessages();
        }
    }
    
    handleMessageUpdate(data) {
        if (data.channel_id === this.currentChannel) {
            const index = this.messages.findIndex(m => m.id === data.id);
            if (index !== -1) {
                this.messages[index] = data;
                this.renderMessages();
            }
        }
    }
    
    handleMessageDelete(data) {
        if (data.channel_id === this.currentChannel) {
            const messageElement = document.querySelector(`[data-message-id="${data.message_id}"]`);
            if (messageElement) {
                messageElement.classList.add('deleted');
                
                const deletedDiv = document.createElement('div');
                deletedDiv.className = 'message-deleted-indicator';
                deletedDiv.textContent = 'Сообщение удалено';
                messageElement.appendChild(deletedDiv);
            }
        }
    }

    async forceLoadGuilds() {
        try {
            console.log('Force loading guilds...');

            // Show loading message
            const messageContainer = document.getElementById('message-container');
            messageContainer.innerHTML = `
                <div class="welcome-message">
                    <h3>Загрузка серверов...</h3>
                    <div class="loading-spinner" style="margin: 20px auto;"></div>
                </div>
            `;

            // Force load guilds from Discord API
            const response = await fetch('/api/quick-setup', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                console.log(`Quick setup completed: ${data.guilds_count} guilds`);

                // Reload guilds
                await this.loadGuilds();
                await this.loadDMChannels();

                if (this.guilds.length > 0) {
                    messageContainer.innerHTML = `
                        <div class="welcome-message">
                            <h3>Серверы загружены!</h3>
                            <p>Найдено ${this.guilds.length} серверов</p>
                            <p>Выберите сервер слева для просмотра каналов</p>
                        </div>
                    `;
                } else {
                    messageContainer.innerHTML = `
                        <div class="welcome-message">
                            <h3>Серверы не найдены</h3>
                            <p>Возможно, ваш аккаунт не состоит в серверах Discord</p>
                            <div class="action-buttons">
                                <button onclick="window.discordLogger.refreshData()" class="btn-primary">Полное обновление</button>
                            </div>
                        </div>
                    `;
                }
            } else {
                throw new Error(data.error || 'Unknown error');
            }

        } catch (error) {
            console.error('Error force loading guilds:', error);
            const messageContainer = document.getElementById('message-container');
            messageContainer.innerHTML = `
                <div class="welcome-message">
                    <h3>Ошибка загрузки</h3>
                    <p>Не удалось загрузить серверы: ${error.message}</p>
                    <div class="action-buttons">
                        <button onclick="window.discordLogger.forceLoadGuilds()" class="btn-primary">Попробовать снова</button>
                    </div>
                </div>
            `;
        }
    }
}
