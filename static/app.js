class DiscordLoggerApp {
    constructor() {
        this.socket = null;
        this.currentGuild = null;
        this.currentChannel = null;
        this.guilds = [];
        this.channels = [];
        this.messages = [];
        this.dmChannels = [];
        
        this.init();
    }
    
    init() {
        this.initSocket();
        this.loadData();
        this.setupEventListeners();
    }
    
    initSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.updateStatus('connected', 'Подключено');
        });
        
        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.updateStatus('disconnected', 'Отключено');
        });
        
        this.socket.on('new_message', (data) => {
            this.handleNewMessage(data);
        });
        
        this.socket.on('message_updated', (data) => {
            this.handleMessageUpdate(data);
        });
        
        this.socket.on('message_deleted', (data) => {
            this.handleMessageDelete(data);
        });
    }
    
    async loadData() {
        this.showLoading(true);
        
        try {
            // Check status first
            const statusResponse = await fetch('/api/status');
            const statusData = await statusResponse.json();
            
            if (!statusData.success || !statusData.connected) {
                this.updateStatus('disconnected', 'Discord не подключен');
                this.showLoading(false);
                return;
            }
            
            this.updateStatus('connected', `Подключен как ${statusData.user.name}`);
            
            // Load guilds
            await this.loadGuilds();
            
            // Load DM channels
            await this.loadDMChannels();
            
        } catch (error) {
            console.error('Error loading data:', error);
            this.updateStatus('disconnected', 'Ошибка загрузки');
        }
        
        this.showLoading(false);
    }
    
    async loadGuilds() {
        try {
            const response = await fetch('/api/guilds');
            const data = await response.json();
            
            if (data.success) {
                this.guilds = data.guilds;
                this.renderGuilds();
            }
        } catch (error) {
            console.error('Error loading guilds:', error);
        }
    }
    
    async loadDMChannels() {
        try {
            const response = await fetch('/api/dms');
            const data = await response.json();
            
            if (data.success) {
                this.dmChannels = data.dms;
            }
        } catch (error) {
            console.error('Error loading DM channels:', error);
        }
    }
    
    async loadChannels(guildId) {
        try {
            const response = await fetch(`/api/channels/${guildId}`);
            const data = await response.json();
            
            if (data.success) {
                this.channels = data.channels;
                this.renderChannels();
            }
        } catch (error) {
            console.error('Error loading channels:', error);
        }
    }
    
    async loadMessages(channelId, page = 0) {
        try {
            const response = await fetch(`/api/messages/${channelId}?page=${page}`);
            const data = await response.json();
            
            if (data.success) {
                if (page === 0) {
                    this.messages = data.messages;
                } else {
                    this.messages = [...this.messages, ...data.messages];
                }
                this.renderMessages();
            }
        } catch (error) {
            console.error('Error loading messages:', error);
        }
    }
    
    renderGuilds() {
        const guildList = document.getElementById('guild-list');
        guildList.innerHTML = '';
        
        this.guilds.forEach(guild => {
            const guildElement = document.createElement('div');
            guildElement.className = 'server-item';
            guildElement.dataset.guildId = guild.id;
            
            const iconElement = document.createElement('div');
            iconElement.className = 'server-icon';
            
            if (guild.icon) {
                const img = document.createElement('img');
                img.src = `https://cdn.discordapp.com/icons/${guild.id}/${guild.icon}.png`;
                img.alt = guild.name;
                iconElement.appendChild(img);
            } else {
                iconElement.textContent = guild.name.charAt(0).toUpperCase();
            }
            
            guildElement.appendChild(iconElement);
            guildElement.title = guild.name;
            
            guildElement.addEventListener('click', () => {
                this.selectGuild(guild.id, guild.name);
            });
            
            guildList.appendChild(guildElement);
        });
    }
    
    renderChannels() {
        const channelContainer = document.getElementById('channel-container');
        channelContainer.innerHTML = '';
        
        // Group channels by type
        const textChannels = this.channels.filter(ch => ch.type === 0);
        const voiceChannels = this.channels.filter(ch => ch.type === 2);
        const categories = this.channels.filter(ch => ch.type === 4);
        
        // Render text channels
        if (textChannels.length > 0) {
            this.renderChannelCategory('Текстовые каналы', textChannels, '#');
        }
        
        // Render voice channels
        if (voiceChannels.length > 0) {
            this.renderChannelCategory('Голосовые каналы', voiceChannels, '🔊');
        }
    }
    
    renderChannelCategory(title, channels, icon) {
        const channelContainer = document.getElementById('channel-container');
        
        const categoryDiv = document.createElement('div');
        categoryDiv.className = 'channel-category';
        
        const headerDiv = document.createElement('div');
        headerDiv.className = 'category-header';
        headerDiv.textContent = title;
        categoryDiv.appendChild(headerDiv);
        
        channels.forEach(channel => {
            const channelDiv = document.createElement('div');
            channelDiv.className = 'channel-item';
            channelDiv.dataset.channelId = channel.id;
            
            if (!channel.is_accessible) {
                channelDiv.classList.add('inaccessible');
            }
            
            const iconSpan = document.createElement('span');
            iconSpan.className = 'channel-icon';
            iconSpan.textContent = icon;
            
            const nameSpan = document.createElement('span');
            nameSpan.className = 'channel-name';
            nameSpan.textContent = channel.name;
            
            channelDiv.appendChild(iconSpan);
            channelDiv.appendChild(nameSpan);
            
            if (channel.is_accessible) {
                channelDiv.addEventListener('click', () => {
                    this.selectChannel(channel.id, channel.name);
                });
            }
            
            categoryDiv.appendChild(channelDiv);
        });
        
        channelContainer.appendChild(categoryDiv);
    }
    
    renderMessages() {
        const messageContainer = document.getElementById('message-container');
        messageContainer.innerHTML = '';
        
        if (this.messages.length === 0) {
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'welcome-message';
            emptyDiv.innerHTML = '<h3>Нет сообщений</h3><p>В этом канале пока нет сообщений</p>';
            messageContainer.appendChild(emptyDiv);
            return;
        }
        
        // Reverse messages to show newest at bottom
        const sortedMessages = [...this.messages].reverse();
        
        sortedMessages.forEach(message => {
            const messageDiv = this.createMessageElement(message);
            messageContainer.appendChild(messageDiv);
        });
        
        // Scroll to bottom
        messageContainer.scrollTop = messageContainer.scrollHeight;
    }
    
    createMessageElement(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message';
        messageDiv.dataset.messageId = message.id;
        
        if (message.is_deleted) {
            messageDiv.classList.add('deleted');
        }
        
        const headerDiv = document.createElement('div');
        headerDiv.className = 'message-header';
        
        const authorSpan = document.createElement('span');
        authorSpan.className = 'message-author';
        authorSpan.textContent = message.author_name;
        
        const timestampSpan = document.createElement('span');
        timestampSpan.className = 'message-timestamp';
        timestampSpan.textContent = this.formatTimestamp(message.created_at);
        
        headerDiv.appendChild(authorSpan);
        headerDiv.appendChild(timestampSpan);
        
        if (message.edited_at) {
            const editedSpan = document.createElement('span');
            editedSpan.className = 'message-edited';
            editedSpan.textContent = '(изменено)';
            headerDiv.appendChild(editedSpan);
        }
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = message.content || '';
        
        messageDiv.appendChild(headerDiv);
        messageDiv.appendChild(contentDiv);
        
        // Add deleted indicator
        if (message.is_deleted) {
            const deletedDiv = document.createElement('div');
            deletedDiv.className = 'message-deleted-indicator';
            deletedDiv.textContent = 'Сообщение удалено';
            messageDiv.appendChild(deletedDiv);
        }
        
        // Add attachments
        if (message.attachments && message.attachments.length > 0) {
            const attachmentsDiv = document.createElement('div');
            attachmentsDiv.className = 'message-attachments';
            
            message.attachments.forEach(attachment => {
                const attachmentLink = document.createElement('a');
                attachmentLink.className = 'attachment';
                attachmentLink.href = attachment.url;
                attachmentLink.target = '_blank';
                attachmentLink.textContent = attachment.filename;
                attachmentsDiv.appendChild(attachmentLink);
            });
            
            messageDiv.appendChild(attachmentsDiv);
        }
        
        return messageDiv;
    }
    
    selectGuild(guildId, guildName) {
        // Remove active class from all guild items
        document.querySelectorAll('.server-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to selected guild
        document.querySelector(`[data-guild-id="${guildId}"]`).classList.add('active');
        
        this.currentGuild = guildId;
        this.currentChannel = null;
        
        document.getElementById('channel-header-title').textContent = guildName;
        document.getElementById('chat-title').textContent = 'Выберите канал';
        
        // Clear messages
        document.getElementById('message-container').innerHTML = 
            '<div class="welcome-message"><h3>Выберите канал</h3><p>Выберите канал для просмотра сообщений</p></div>';
        
        this.loadChannels(guildId);
    }
    
    selectChannel(channelId, channelName) {
        // Remove active class from all channel items
        document.querySelectorAll('.channel-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to selected channel
        document.querySelector(`[data-channel-id="${channelId}"]`).classList.add('active');
        
        this.currentChannel = channelId;
        
        document.getElementById('chat-title').textContent = `# ${channelName}`;
        
        // Join channel for real-time updates
        if (this.socket) {
            this.socket.emit('join_channel', { channel_id: channelId });
        }
        
        this.loadMessages(channelId);
    }
    
    selectDMs() {
        // Remove active class from all server items
        document.querySelectorAll('.server-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to DM button
        document.getElementById('dm-button').classList.add('active');
        
        this.currentGuild = null;
        this.currentChannel = null;
        
        document.getElementById('channel-header-title').textContent = 'Личные сообщения';
        document.getElementById('chat-title').textContent = 'Личные сообщения';
        
        // Render DM channels
        this.renderDMChannels();
    }
    
    renderDMChannels() {
        const channelContainer = document.getElementById('channel-container');
        channelContainer.innerHTML = '';
        
        if (this.dmChannels.length === 0) {
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'welcome-message';
            emptyDiv.innerHTML = '<p>Нет личных сообщений</p>';
            channelContainer.appendChild(emptyDiv);
            return;
        }
        
        this.dmChannels.forEach(dm => {
            const channelDiv = document.createElement('div');
            channelDiv.className = 'channel-item';
            channelDiv.dataset.channelId = dm.id;
            
            const iconSpan = document.createElement('span');
            iconSpan.className = 'channel-icon';
            iconSpan.textContent = '@';
            
            const nameSpan = document.createElement('span');
            nameSpan.className = 'channel-name';
            nameSpan.textContent = dm.recipient_name || 'Unknown User';
            
            channelDiv.appendChild(iconSpan);
            channelDiv.appendChild(nameSpan);
            
            channelDiv.addEventListener('click', () => {
                this.selectChannel(dm.id, dm.recipient_name || 'Unknown User');
            });
            
            channelContainer.appendChild(channelDiv);
        });
    }
    
    setupEventListeners() {
        // DM button
        document.getElementById('dm-button').addEventListener('click', () => {
            this.selectDMs();
        });
    }
    
    updateStatus(status, text) {
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.getElementById('status-text');
        
        statusDot.className = `status-dot ${status}`;
        statusText.textContent = text;
    }
    
    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.remove('hidden');
        } else {
            overlay.classList.add('hidden');
        }
    }
    
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('ru-RU', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    handleNewMessage(data) {
        if (data.channel_id === this.currentChannel) {
            this.messages.unshift(data);
            this.renderMessages();
        }
    }
    
    handleMessageUpdate(data) {
        if (data.channel_id === this.currentChannel) {
            const index = this.messages.findIndex(m => m.id === data.id);
            if (index !== -1) {
                this.messages[index] = data;
                this.renderMessages();
            }
        }
    }
    
    handleMessageDelete(data) {
        if (data.channel_id === this.currentChannel) {
            const messageElement = document.querySelector(`[data-message-id="${data.message_id}"]`);
            if (messageElement) {
                messageElement.classList.add('deleted');
                
                const deletedDiv = document.createElement('div');
                deletedDiv.className = 'message-deleted-indicator';
                deletedDiv.textContent = 'Сообщение удалено';
                messageElement.appendChild(deletedDiv);
            }
        }
    }
}
